"use strict";(self.webpackChunk=self.webpackChunk||[]).push([[5415],{5415:(e,o,t)=>{t.r(o),t.d(o,{default:()=>y});var s=t(9726),a={id:"cookies",class:"db-card db-tab-div active"},i={class:"db-card-header"},l={class:"db-card-title"},r={class:"db-card-body"},c={class:"form-row"},n={class:"form-col-12 sm:form-col-12"},d={for:"cookies_details_page_id",class:"db-field-title required"},m={key:0,class:"db-field-alert"},u={class:"form-col-12"},p={for:"cookies_summary",class:"db-field-title required"},_={key:0,class:"db-field-alert"},f={class:"form-col-12"},k={type:"submit",class:"db-btn text-white bg-primary"};var v=t(5475),g=t(9856),b=t(6884);const h={name:"CookiesComponent",components:{LoadingComponent:v.A},data:function(){return{loading:{isActive:!1},form:{cookies_details_page_id:null,cookies_summary:""},errors:{}}},computed:{pages:function(){return this.$store.getters["page/lists"]}},mounted:function(){var e=this;try{this.loading.isActive=!0,this.$store.dispatch("page/lists",{order_column:"id",order_type:"asc",status:b.A.ACTIVE}),this.$store.dispatch("cookies/lists").then(function(o){e.form={cookies_details_page_id:o.data.data.cookies_details_page_id,cookies_summary:o.data.data.cookies_summary},e.loading.isActive=!1}).catch(function(o){e.loading.isActive=!1})}catch(e){this.loading.isActive=!1,g.A.error(e)}},methods:{save:function(){var e=this;try{this.loading.isActive=!0,this.$store.dispatch("cookies/save",this.form).then(function(o){var t;e.loading.isActive=!1,g.A.successFlip(null!==(t="put"===o.config.method)&&void 0!==t?t:0,e.$t("menu.cookies")),e.errors=""}).catch(function(o){e.loading.isActive=!1,e.errors=o.response.data.errors})}catch(e){this.loading.isActive=!1,g.A.error(e)}}}};const y=(0,t(6262).A)(h,[["render",function(e,o,t,v,g,b){var h=(0,s.resolveComponent)("LoadingComponent"),y=(0,s.resolveComponent)("vue-select");return(0,s.openBlock)(),(0,s.createElementBlock)(s.Fragment,null,[(0,s.createVNode)(h,{props:g.loading},null,8,["props"]),(0,s.createElementVNode)("div",a,[(0,s.createElementVNode)("div",i,[(0,s.createElementVNode)("h3",l,(0,s.toDisplayString)(e.$t("menu.cookies")),1)]),(0,s.createElementVNode)("div",r,[(0,s.createElementVNode)("form",{onSubmit:o[2]||(o[2]=(0,s.withModifiers)(function(){return b.save&&b.save.apply(b,arguments)},["prevent"])),class:"d-block w-full"},[(0,s.createElementVNode)("div",c,[(0,s.createElementVNode)("div",n,[(0,s.createElementVNode)("label",d,(0,s.toDisplayString)(e.$t("label.cookies_details_page")),1),(0,s.createVNode)(y,{class:(0,s.normalizeClass)(["db-field-control f-b-custom-select",g.errors.cookies_details_page_id?"invalid":""]),id:"cookies_details_page_id",modelValue:g.form.cookies_details_page_id,"onUpdate:modelValue":o[0]||(o[0]=function(e){return g.form.cookies_details_page_id=e}),options:b.pages,"label-by":"title","value-by":"id",closeOnSelect:!0,searchable:!0,clearOnClose:!0,placeholder:"--","search-placeholder":"--"},null,8,["class","modelValue","options"]),g.errors.cookies_details_page_id?((0,s.openBlock)(),(0,s.createElementBlock)("small",m,(0,s.toDisplayString)(g.errors.cookies_details_page_id[0]),1)):(0,s.createCommentVNode)("",!0)]),(0,s.createElementVNode)("div",u,[(0,s.createElementVNode)("label",p,(0,s.toDisplayString)(e.$t("label.cookies_summary")),1),(0,s.withDirectives)((0,s.createElementVNode)("textarea",{"onUpdate:modelValue":o[1]||(o[1]=function(e){return g.form.cookies_summary=e}),class:(0,s.normalizeClass)([g.errors.cookies_summary?"invalid":"","db-field-control"]),id:"cookies_summary"},null,2),[[s.vModelText,g.form.cookies_summary]]),g.errors.cookies_summary?((0,s.openBlock)(),(0,s.createElementBlock)("small",_,(0,s.toDisplayString)(g.errors.cookies_summary[0]),1)):(0,s.createCommentVNode)("",!0)]),(0,s.createElementVNode)("div",f,[(0,s.createElementVNode)("button",k,[o[3]||(o[3]=(0,s.createElementVNode)("i",{class:"lab lab-fill-save"},null,-1)),(0,s.createElementVNode)("span",null,(0,s.toDisplayString)(e.$t("button.save")),1)])])])],32)])])],64)}]])}}]);