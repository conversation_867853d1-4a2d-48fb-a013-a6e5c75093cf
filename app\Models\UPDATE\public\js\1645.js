"use strict";(self.webpackChunk=self.webpackChunk||[]).push([[1645],{1645:(t,e,r)=>{r.r(e),r.d(e,{default:()=>L});var n=r(9726),o={class:"flex items-center gap-4 mb-7"},i={class:"capitalize text-xl font-bold text-primary"},a={class:"rounded-2xl shadow-card mb-6 bg-white"},d={class:"font-semibold p-4"},s={key:0,class:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 mb-5 rounded relative",role:"alert"},l={class:"block sm:inline"},c={class:"mobile:overflow-x-auto"},u={class:"w-full text-left text-sm capitalize"},p={class:"font-semibold border-b border-t border-gray-200"},m={class:"p-4"},f={class:"p-4"},y={class:"p-4"},h={key:0,class:"font-medium"},_={class:"p-4 border-b group-last:border-0 border-gray-100"},g=["onUpdate:modelValue","onChange"],b={key:1,class:"text-danger font-semibold"},q={class:"p-4 border-b group-last:border-0 border-gray-100"},v={class:"inline-flex items-center gap-3"},x=["src"],V={class:"overflow-hidden"},E={class:"text-sm capitalize whitespace-nowrap overflow-hidden text-ellipsis mb-0.5"},N={key:0,class:"text-sm capitalize whitespace-nowrap overflow-hidden text-ellipsis"},k={class:"p-4 border-b group-last:border-0 border-gray-100"},w={class:"flex items-center gap-1 w-20 p-1 rounded-full bg-[#F7F7FC]"},C=["onClick"],A=["id","onKeyup","onUpdate:modelValue"],D=["onClick"],B={class:"rounded-2xl shadow-card mb-6 p-4 bg-white"},$={class:"mb-4"},R={for:"return_reason_id",class:"capitalize font-medium mb-2"},I={key:0,class:"db-field-alert"},S={class:"mb-4"},z={for:"note",class:"capitalize font-medium mb-2"},F={key:0,class:"db-field-alert"},P={class:"capitalize font-medium mb-2"},U={type:"submit",class:"field-button w-fit font-semibold tracking-wide"};var M=r(1811),O=r(9856),T=r(8655),j=r(6884);const K={name:"ReturnOrderRequestComponent",components:{LoadingComponent:M.A},data:function(){return{loading:{isActive:!1},isAllReturn:!1,form:{return_reason_id:null,note:"",order_id:null,order_serial_no:null,products:[]},search:{paginate:1,page:1,per_page:10,order_column:"id",order_type:"desc"},products:[],qty:[],image:"",errors:{}}},computed:{order:function(){return this.$store.getters["frontendOrder/show"]},orderProducts:function(){return this.$store.getters["frontendOrder/orderProducts"]},returnReasons:function(){return this.$store.getters["frontendReturnReason/lists"]}},mounted:function(){var t=this;this.loading.isActive=!0,this.$route.params.id&&(this.loading.isActive=!0,this.$store.dispatch("frontendOrder/show",this.$route.params.id).then(function(e){t.form.order_id=e.data.data.id,t.form.order_serial_no=e.data.data.order_serial_no,t.loading.isActive=!1}).catch(function(e){t.loading.isActive=!1}),this.$store.dispatch("frontendReturnReason/lists",{order_column:"id",order_type:"asc",status:j.A.ACTIVE}))},methods:{changeImage:function(t){this.image=t.target.files},onlyNumber:function(t){return T.A.onlyNumber(t)},close:function(){this.errors.products=""},selectAll:function(t,e){var r=this;e.forEach(function(t,e){var n=1,o=r.qty.find(function(e){return e.product_id===t.product_id}),i=r.qty.findIndex(function(e){return e.product_id===t.product_id}),a={product_id:t.product_id,quantity:n,price:t.price,total:t.total,tax:t.tax,order_quantity:t.order_quantity,return_price:parseFloat(t.quantity*t.price),has_variation:t.has_variation,variation_id:t.variation_id,variation_names:t.variation_names};if(o?(a.quantity=r.qty[i].quantity,n=r.qty[i].quantity):(a.quantity=t.quantity,n=t.quantity),r.isAllReturn){if(t.is_refundable)if(t.isReturn=!0,r.products.find(function(e){return e.product_id===t.product_id})){var d=r.products.findIndex(function(e){return e.product_id===t.product_id});r.products.splice(d,1),r.products.push(a)}else r.products.push(a)}else t.isReturn=!1,r.products=[],r.qty=[]})},selectProduct:function(t,e,r){var n=1,o=this.qty.find(function(t){return t.product_id===r.product_id}),i=this.qty.findIndex(function(t){return t.product_id===r.product_id}),a={product_id:r.product_id,quantity:n,price:r.price,total:r.total,tax:r.tax,order_quantity:r.order_quantity,return_price:parseFloat(r.quantity*r.price),has_variation:r.has_variation,variation_id:r.variation_id,variation_names:r.variation_names};if(o?(a.quantity=this.qty[i].quantity,n=this.qty[i].quantity):(a.quantity=r.quantity,n=r.quantity),t.target.checked)this.products.push(a);else{var d=this.products.findIndex(function(t){return t.product_id===r.product_id});this.products.splice(d,1),this.isAllReturn=!1}},quantityIncrement:function(t,e){var r=1,n=null;if(this.qty.find(function(t){return t.product_id===e.product_id})&&(n=this.qty.findIndex(function(t){return t.product_id===e.product_id}),e.quantity<this.qty[n].max_quantity?e.quantity+=1:e.quantity=this.qty[n].max_quantity,r=e.quantity,this.qty[n].quantity=r),this.products.find(function(t){return t.product_id===e.product_id})){var o=this.products.findIndex(function(t){return t.product_id===e.product_id});this.products[o].quantity=r,this.products[o].return_price=parseFloat(r*e.price)}},quantityDecrement:function(t,e){var r={product_id:e.product_id,quantity:e.quantity,max_quantity:e.order_quantity};e.quantity>1&&(e.quantity-=1);var n=this.qty.find(function(t){return t.product_id===e.product_id}),o=e.quantity,i=null;if(n?(i=this.qty.findIndex(function(t){return t.product_id===e.product_id}),0===o&&(o=1),this.qty[i].quantity=o):(0===(o=e.quantity)&&(o=1),r.quantity=o,this.qty.push(r)),this.products.find(function(t){return t.product_id===e.product_id})){var a=this.products.findIndex(function(t){return t.product_id===e.product_id});this.products[a].quantity=o,this.products[a].return_price=parseFloat(o*e.price)}},quantityUp:function(t,e,r){var n={product_id:r.product_id,quantity:r.quantity,max_quantity:r.order_quantity},o=parseInt(t.target.value),i=null;if((0===o||o<0)&&(o=1,r.quantity=o),o>r.order_quantity&&(o=r.order_quantity,r.quantity=r.order_quantity),this.qty.find(function(t){return t.product_id===r.product_id})?(i=this.qty.findIndex(function(t){return t.product_id===r.product_id}),this.qty[i].quantity=o):(n.quantity=o,this.qty.push(n)),this.products.find(function(t){return t.product_id===r.product_id})){var a=this.products.findIndex(function(t){return t.product_id===r.product_id});this.products[a].quantity=o,this.products[a].return_price=parseFloat(o*r.price)}},save:function(){var t=this;try{var e=new FormData;if(e.append("return_reason_id",null!==this.form.return_reason_id?this.form.return_reason_id:""),e.append("note",this.form.note),e.append("order_id",this.form.order_id),e.append("order_serial_no",this.form.order_serial_no),e.append("products",this.products.length>0?JSON.stringify(this.products):""),this.image)for(var r=0;r<this.image.length;r++)e.append("image[]",this.image[r]);this.loading.isActive=!0,this.$store.dispatch("frontendReturnAndRefund/save",{id:this.$route.params.id,form:e,search:this.search}).then(function(e){t.loading.isActive=!1,O.A.successFlip(0,t.$t("menu.return_orders")),t.form={return_reason_id:null,note:"",order_id:null,order_serial_no:null,products:[]},t.products=[],t.qty=[],t.image="",t.errors={},t.$refs.imageProperty.value=null,t.$router.push({name:"frontend.account.returnOrders"})}).catch(function(e){t.loading.isActive=!1,void 0===e.response.data.errors?e.response.data.message&&(t.errors={},O.A.error(e.response.data.message)):t.errors=e.response.data.errors})}catch(t){this.loading.isActive=!1,O.A.error(t)}}}};const L=(0,r(6262).A)(K,[["render",function(t,e,r,M,O,T){var j=(0,n.resolveComponent)("LoadingComponent"),K=(0,n.resolveComponent)("vue-select");return(0,n.openBlock)(),(0,n.createElementBlock)(n.Fragment,null,[(0,n.createVNode)(j,{props:O.loading},null,8,["props"]),(0,n.createElementVNode)("div",o,[(0,n.createElementVNode)("button",{onClick:e[0]||(e[0]=(0,n.withModifiers)(function(e){return t.$router.back()},["prevent"])),class:"lab-line-undo text-xl font-bold text-primary"}),(0,n.createElementVNode)("h2",i,(0,n.toDisplayString)(t.$t("label.return_request")),1)]),(0,n.createElementVNode)("form",{class:"w-full d-block min-h-dvh",onSubmit:e[8]||(e[8]=(0,n.withModifiers)(function(){return T.save&&T.save.apply(T,arguments)},["prevent"]))},[(0,n.createElementVNode)("div",a,[(0,n.createElementVNode)("h3",d,(0,n.toDisplayString)(t.$t("label.order_id"))+": #"+(0,n.toDisplayString)(T.order.order_serial_no),1),O.errors.products?((0,n.openBlock)(),(0,n.createElementBlock)("div",s,[(0,n.createElementVNode)("span",l,(0,n.toDisplayString)(O.errors.products[0]),1),(0,n.createElementVNode)("span",{class:"absolute top-0 bottom-0 right-0 px-4 py-3 cursor-pointer",onClick:e[1]||(e[1]=function(){return T.close&&T.close.apply(T,arguments)})},e[9]||(e[9]=[(0,n.createElementVNode)("i",{class:"lab lab-fill-close-circle margin-top-5-px"},null,-1)]))])):(0,n.createCommentVNode)("",!0),(0,n.createElementVNode)("div",c,[(0,n.createElementVNode)("table",u,[(0,n.createElementVNode)("thead",p,[(0,n.createElementVNode)("tr",null,[(0,n.createElementVNode)("th",m,[(0,n.withDirectives)((0,n.createElementVNode)("input",{type:"checkbox","onUpdate:modelValue":e[2]||(e[2]=function(t){return O.isAllReturn=t}),onChange:e[3]||(e[3]=function(t){return T.selectAll(t,T.orderProducts)}),class:"cs-custom-checkbox"},null,544),[[n.vModelCheckbox,O.isAllReturn]])]),(0,n.createElementVNode)("th",f,(0,n.toDisplayString)(t.$t("label.products")),1),(0,n.createElementVNode)("th",y,(0,n.toDisplayString)(t.$t("label.quantity")),1)])]),T.orderProducts.length>0?((0,n.openBlock)(),(0,n.createElementBlock)("tbody",h,[((0,n.openBlock)(!0),(0,n.createElementBlock)(n.Fragment,null,(0,n.renderList)(T.orderProducts,function(r,o){return(0,n.openBlock)(),(0,n.createElementBlock)("tr",{key:o,class:"group"},[(0,n.createElementVNode)("td",_,[r.is_refundable?(0,n.withDirectives)(((0,n.openBlock)(),(0,n.createElementBlock)("input",{key:0,type:"checkbox","onUpdate:modelValue":function(t){return r.isReturn=t},onChange:function(t){return T.selectProduct(t,o,r)},class:"cs-custom-checkbox"},null,40,g)),[[n.vModelCheckbox,r.isReturn]]):((0,n.openBlock)(),(0,n.createElementBlock)("span",b,(0,n.toDisplayString)(t.$t("label.not_refundable")),1))]),(0,n.createElementVNode)("td",q,[(0,n.createElementVNode)("div",v,[(0,n.createElementVNode)("img",{src:r.product_image,alt:"product",class:"w-12 h-12 object-cover rounded-md flex-shrink-0"},null,8,x),(0,n.createElementVNode)("div",V,[(0,n.createElementVNode)("h4",E,(0,n.toDisplayString)(r.product_name),1),r.has_variation?((0,n.openBlock)(),(0,n.createElementBlock)("p",N,(0,n.toDisplayString)(r.variation_names),1)):(0,n.createCommentVNode)("",!0)])])]),(0,n.createElementVNode)("td",k,[(0,n.createElementVNode)("div",w,[(0,n.createElementVNode)("button",{onClick:(0,n.withModifiers)(function(t){return T.quantityDecrement(o,r)},["prevent"]),type:"button",class:(0,n.normalizeClass)([1===r.quantity?"cursor-not-allowed text-[#A0A3BD]":"text-primary","lab-fill-circle-minus text-lg leading-none"])},null,10,C),(0,n.withDirectives)((0,n.createElementVNode)("input",{type:"number",id:"quantityInput"+o,onKeyup:function(t){return T.quantityUp(t,o,r)},onKeypress:e[4]||(e[4]=function(t){return T.onlyNumber(t)}),"onUpdate:modelValue":function(t){return r.quantity=t},class:"text-center w-full h-5 text-sm font-medium"},null,40,A),[[n.vModelText,r.quantity]]),(0,n.createElementVNode)("button",{onClick:(0,n.withModifiers)(function(t){return T.quantityIncrement(o,r)},["prevent"]),type:"button",class:(0,n.normalizeClass)([r.quantity===r.order_quantity?"cursor-not-allowed text-[#A0A3BD]":"text-primary","lab-fill-circle-plus text-lg leading-none text-[#A0A3BD]"])},null,10,D)])])])}),128))])):(0,n.createCommentVNode)("",!0)])])]),(0,n.createElementVNode)("div",B,[(0,n.createElementVNode)("div",$,[(0,n.createElementVNode)("label",R,[(0,n.createTextVNode)((0,n.toDisplayString)(t.$t("label.return_reason"))+" ",1),e[10]||(e[10]=(0,n.createElementVNode)("span",{class:"text-danger"},"*",-1))]),(0,n.createVNode)(K,{class:(0,n.normalizeClass)(["field-control appearance-none cursor-pointer",O.errors.return_reason_id?"invalid":""]),id:"return_reason_id",modelValue:O.form.return_reason_id,"onUpdate:modelValue":e[5]||(e[5]=function(t){return O.form.return_reason_id=t}),options:T.returnReasons,"label-by":"title","value-by":"id",closeOnSelect:!0,searchable:!0,clearOnClose:!0,placeholder:"--","search-placeholder":"--"},null,8,["class","modelValue","options"]),O.errors.return_reason_id?((0,n.openBlock)(),(0,n.createElementBlock)("small",I,(0,n.toDisplayString)(O.errors.return_reason_id[0]),1)):(0,n.createCommentVNode)("",!0)]),(0,n.createElementVNode)("div",S,[(0,n.createElementVNode)("label",z,(0,n.toDisplayString)(t.$t("label.return_note")),1),(0,n.withDirectives)((0,n.createElementVNode)("textarea",{id:"note",class:(0,n.normalizeClass)(["field-control",O.errors.note?"invalid":""]),"onUpdate:modelValue":e[6]||(e[6]=function(t){return O.form.note=t})},null,2),[[n.vModelText,O.form.note]]),O.errors.note?((0,n.openBlock)(),(0,n.createElementBlock)("small",F,(0,n.toDisplayString)(O.errors.note[0]),1)):(0,n.createCommentVNode)("",!0)]),(0,n.createElementVNode)("div",null,[(0,n.createElementVNode)("label",P,(0,n.toDisplayString)(t.$t("label.attachment")),1),(0,n.createElementVNode)("input",{onChange:e[7]||(e[7]=function(){return T.changeImage&&T.changeImage.apply(T,arguments)}),class:(0,n.normalizeClass)([O.errors.image?"invalid":"","db-field-control"]),id:"image",type:"file",ref:"imageProperty",accept:"image/png, image/jpeg, image/jpg",multiple:""},null,34)])]),(0,n.createElementVNode)("button",U,(0,n.toDisplayString)(t.$t("button.submit_return")),1)],32)],64)}]])}}]);