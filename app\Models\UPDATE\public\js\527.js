/*! For license information please see 527.js.LICENSE.txt */
(self.webpackChunk=self.webpackChunk||[]).push([[527],{527:(t,e,o)=>{"use strict";o.r(e),o.d(e,{default:()=>wt});var l=o(9726),n={class:"col-12"},i={class:"db-card mb-6"},r={class:"db-card-header"},a={class:"db-card-title"},s={class:"db-card-body"},c={class:"row"},u={class:"form-col-12 sm:form-col-6"},d={class:"db-field-title required"},f=["onClick"],p={key:0,class:"db-field-alert"},h={class:"form-col-12 sm:form-col-6"},b={class:"db-field-title"},q={key:0,class:"db-field-alert"},y={class:"form-col-12 sm:form-col-6"},v={class:"db-field-title required"},m={key:0,class:"db-field-alert"},g={class:"form-col-12 sm:form-col-6"},k={class:"db-field-title"},_={key:0,class:"db-field-alert"},w={class:"form-col-12"},x={class:"rounded-lg border border-amber-100"},O={class:"w-full px-4 py-3 font-medium rounded-t-lg bg-amber-100 text-amber-600"},E={class:"row p-5"},N={class:"form-col-12"},A={class:"db-field-title required"},j={class:"relative w-full h-12"},S={key:0,class:"db-field-alert"},T={class:"form-col-12"},P={class:"db-field-title"},C={class:"db-table-responsive border rounded-md"},L={class:"db-table"},M={class:"db-table-head border-t-0"},I={class:"db-table-head-tr"},R={class:"db-table-head-th"},V={class:"db-table-head-th"},B={class:"db-table-head-th"},D={class:"db-table-head-th"},z={class:"db-table-head-th"},F={class:"db-table-head-th"},U={class:"db-table-head-th"},H={class:"db-table-body"},K={class:"db-table-body-td font-medium"},$={key:0},Z={class:"db-table-body-td"},W={class:"db-table-body-td"},G=["onKeyup","onUpdate:modelValue"],Y={class:"db-table-body-td"},Q={class:"db-table-body-td"},X={class:"db-table-body-td"},J={class:"db-table-body-td"},tt={class:"db-table-body-td",colspan:"2"},et={class:"db-table-body-td"},ot={class:"pl-3"},lt={class:"db-table-body-td"},nt={class:"db-table-body-td"},it={class:"db-table-body-td"},rt={class:"row pt-5"},at={class:"form-col-12"},st={class:"db-field-title"},ct={key:0,class:"db-field-alert"},ut={class:"form-col-12"},dt={class:"flex flex-wrap gap-3"},ft={key:0,type:"submit",class:"db-btn text-white bg-primary"},pt={class:"tracking-wide"};var ht=o(5475),bt=o(6749),qt=o(6341),yt=o(9856),vt=o(7278),mt=o(8655),gt=o(9590),kt=o(9639);const _t={name:"ReturnOrderCreateAndEditComponent",components:{quillEditor:qt.zs,LoadingComponent:ht.A,Datepicker:bt.A,ProductModalComponent:vt.A,SmIconDeleteComponent:gt.A,SmIconSidebarModalEditComponent:kt.A},data:function(){return{file:"",productId:null,errors:{},datatable:[],loading:{isActive:!1},modal:{isShowModal:!1},props:{form:{return_order_id:0,date:"",total:null,user_id:null,reference_no:"",reason:"",products:[]}},selectedProduct:{name:"",quantity:0,tax_id:[],price:0,discount:0,variation_id:0,variation_names:"",product_id:0,sku:"",is_variation:!1,mode:"add"},dataTableIndex:null}},mounted:function(){this.productList(),this.$store.dispatch("user/lists",{vuex:!0,order_type:"asc"}),this.returnOrderInfo()},computed:{setting:function(){return this.$store.getters["frontendSetting/lists"]},products:function(){return this.$store.getters["product/simpleList"]},subtotal:function(){return this.datatable.reduce(function(t,e){return t+ +e.subtotal},0)},users:function(){return this.$store.getters["user/lists"]},totalPrice:function(){return this.datatable.reduce(function(t,e){return t+ +e.total},0)},totalQuantity:function(){return this.datatable.reduce(function(t,e){return t+ +e.quantity},0)},totalDiscount:function(){return this.datatable.reduce(function(t,e){return t+ +e.total_discount},0)},totalTax:function(){return this.datatable.reduce(function(t,e){return t+ +e.total_tax},0)}},methods:{permissionChecker:function(t){return mt.A.permissionChecker(t)},changeFile:function(t){this.file=t.target.files[0]},floatFormat:function(t){return mt.A.floatFormat(t,this.setting.site_digit_after_decimal_point)},onlyNumber:function(t){return mt.A.onlyNumber(t)},taxRateById:function(t){return this.$refs.productModal.taxRateById(t)},selectProduct:function(t){var e=this.products.find(function(e){return e.id===t});e&&(this.selectedProduct={name:e.name,quantity:1,sku:e.sku,tax_id:e.taxes,price:e.buying_price,discount:0,variation_id:0,variation_names:"",product_id:e.id,is_variation:e.is_variation,mode:"add"},e.is_variation?(this.loadInitialVariations(e.id),this.modal.isShowModal=!0):this.productCheck())},modalSubmit:function(){this.productCheck(),this.modal.isShowModal=!1,this.productId=null},productCheck:function(){var t=this,e=null,o=null;"edit"===this.selectedProduct.mode?e=this.datatable[this.dataTableIndex]:this.datatable.length>0&&(e=this.datatable.find(function(e){return e.product_id===t.selectedProduct.product_id&&e.variation_id===t.selectedProduct.variation_id})),e&&(o="edit"===this.selectedProduct.mode?0:e.quantity);var l=0,n=0,i=0,r=this.selectedProduct.discount*this.selectedProduct.quantity;if(this.selectedProduct.tax_id.length>0){for(var a=0;a<this.selectedProduct.tax_id.length;a++){var s=this.selectedProduct.tax_id[a],c=this.taxRateById(s);l+=+this.selectedProduct.price*c/100,i+=+c}n=l*this.selectedProduct.quantity}var u={mode:this.selectedProduct.mode,name:this.selectedProduct.name,quantity:this.selectedProduct.quantity+o,tax_id:this.selectedProduct.tax_id,price:+this.selectedProduct.price,discount:this.selectedProduct.discount,variation_id:this.selectedProduct.variation_id,variation_names:this.selectedProduct.variation_names,product_id:this.selectedProduct.product_id,is_variation:this.selectedProduct.is_variation,tax:l,total_tax:n,total_tax_rate:i,total_discount:r,subtotal:this.selectedProduct.quantity*this.selectedProduct.price,total:(+this.selectedProduct.quantity*this.selectedProduct.price+ +n-+r).toFixed(2),sku:this.selectedProduct.sku,item_id:this.selectedProduct.is_variation?this.selectedProduct.variation_id:this.selectedProduct.product_id};e?(e.quantity=u.quantity,e.tax_id=u.tax_id,e.price=u.price,e.discount=u.discount,e.tax=u.tax,e.total_tax=u.total_tax,e.total_tax_rate=u.total_tax_rate,e.total_discount=u.total_discount,e.subtotal=u.subtotal,e.total=u.total,e.sku=u.sku,e.item_id=u.is_variation?u.variation_id:u.product_id,e.variation_names=u.variation_names,e.variation_id=u.variation_id):this.datatable.push(u)},editDatatable:function(t){var e=this.datatable[t];this.selectedProduct={name:e.name,quantity:e.quantity,tax_id:0===e.tax_id.length?[]:e.tax_id,price:e.price,discount:e.discount,variation_id:e.variation_id,variation_names:e.variation_names,product_id:e.product_id,is_variation:e.is_variation,sku:e.sku,mode:"edit"},this.dataTableIndex=t,this.loadInitialVariations(e.product_id),this.modal.isShowModal=!0},save:function(){var t=this;try{var e,o=new FormData;o.append("user_id",null!==(e=this.props.form.user_id)&&void 0!==e?e:""),o.append("date",this.props.form.date?this.props.form.date:""),o.append("reference_no",this.props.form.reference_no),o.append("subtotal",this.subtotal),o.append("tax",this.totalTax),o.append("discount",this.totalDiscount),o.append("total",this.totalPrice),o.append("reason",this.props.form.reason),o.append("products",JSON.stringify(this.datatable)),this.file&&o.append("file",this.file),this.loading.isActive=!0;var l=this.$store.getters["returnOrder/temp"].temp_id;this.$store.dispatch("returnOrder/save",{form:o}).then(function(e){t.loading.isActive=!1,yt.A.successFlip(null===l?0:1,t.$t("menu.return_orders")),t.reset(),t.$router.push({name:"admin.return-order.list"})}).catch(function(e){t.loading.isActive=!1,t.errors=e.response.data.errors,t.errors.global&&yt.A.error(t.errors.global[0])})}catch(t){this.loading.isActive=!1,yt.A.error(t)}},removeProduct:function(t){this.datatable.splice(t,1)},updateQuantity:function(t){var e=this.datatable[t].tax>0?this.datatable[t].tax:0;this.datatable[t].total_tax=e*this.datatable[t].quantity,this.datatable[t].total_discount=this.datatable[t].discount*this.datatable[t].quantity,this.datatable[t].subtotal=(Number(this.datatable[t].quantity)*Number(this.datatable[t].price)).toFixed(2),this.datatable[t].total=(+this.datatable[t].quantity*this.datatable[t].price+ +this.datatable[t].total_tax-+this.datatable[t].total_discount).toFixed(2)},productList:function(){var t=this;this.loading.isActive=!0,this.$store.dispatch("product/getSimpleProduct").then(function(e){t.loading.isActive=!1}).catch(function(e){t.loading.isActive=!1})},loadInitialVariations:function(t){var e=this;this.loading.isActive=!0,this.$store.commit("productVariation/initialVariation",[]),this.$store.dispatch("productVariation/initialVariation",t).then(function(){e.loading.isActive=!1}).catch(function(t){e.loading.isActive=!1})},returnOrderInfo:function(){var t=this;isNaN(this.$route.params.id)||this.$store.dispatch("returnOrder/edit",this.$route.params.id).then(function(e){t.getReturnOrder(e.data.data)})},getReturnOrder:function(t){this.props.form.return_order_id=t.id,this.props.form.date=t.date,this.props.form.user_id=t.user_id,this.props.form.reference_no=t.reference_no?t.reference_no:"",this.props.form.total=t.total,this.props.form.reason=t.reason;for(var e=[],o=0;o<t.products.length;o++){var l=t.products[o],n=[],i=0;if(l.taxes)for(var r=0;r<l.taxes.length;r++){var a=l.taxes[r];n.push(a.tax_id),i+=+a.tax_rate}e.push({product_id:l.product_id,item_id:l.item_id,variation_names:l.variation_names,sku:l.sku,name:l.product_name,price:this.floatFormat(l.price),quantity:l.quantity,discount:l.discount>0?l.discount/l.quantity:0,total_discount:+l.discount,tax:l.tax>0?l.tax/l.quantity:0,total_tax:l.tax,total_tax_rate:i,tax_id:n,subtotal:l.subtotal,total:l.total,is_variation:!!l.product_variation,variation_id:l.product_variation?l.item_id:0})}this.datatable=e},reset:function(){this.props.form.user_id=null,this.props.form.date="",this.props.form.reference_no="",this.props.form.total=null,this.props.form.reason="",this.props.form.products=[],this.datatable=[],this.file="",this.$refs.fileProperty.value="",this.errors={}}}};const wt=(0,o(6262).A)(_t,[["render",function(t,e,o,ht,bt,qt){var yt=(0,l.resolveComponent)("LoadingComponent"),vt=(0,l.resolveComponent)("Datepicker"),mt=(0,l.resolveComponent)("vue-select"),gt=(0,l.resolveComponent)("SmIconSidebarModalEditComponent"),kt=(0,l.resolveComponent)("SmIconDeleteComponent"),_t=(0,l.resolveComponent)("ProductModalComponent"),wt=(0,l.resolveComponent)("quill-editor");return(0,l.openBlock)(),(0,l.createElementBlock)(l.Fragment,null,[(0,l.createVNode)(yt,{props:bt.loading},null,8,["props"]),(0,l.createElementVNode)("div",n,[(0,l.createElementVNode)("form",{onSubmit:e[9]||(e[9]=(0,l.withModifiers)(function(){return qt.save&&qt.save.apply(qt,arguments)},["prevent"])),class:"block w-full"},[(0,l.createElementVNode)("div",i,[(0,l.createElementVNode)("div",r,[(0,l.createElementVNode)("h3",a,(0,l.toDisplayString)(t.$t("menu.return_orders")),1)]),(0,l.createElementVNode)("div",s,[(0,l.createElementVNode)("div",c,[(0,l.createElementVNode)("div",u,[(0,l.createElementVNode)("label",d,(0,l.toDisplayString)(t.$t("label.date")),1),(0,l.createVNode)(vt,{hideInputIcon:"",autoApply:"",modelValue:bt.props.form.date,"onUpdate:modelValue":e[0]||(e[0]=function(t){return bt.props.form.date=t}),enableTimePicker:!0,is24:!1,monthChangeOnScroll:!1,utc:"false","input-class-name":bt.errors.date?"invalid":""},{"am-pm-button":(0,l.withCtx)(function(t){var e=t.toggle,o=t.value;return[(0,l.createElementVNode)("button",{onClick:e},(0,l.toDisplayString)(o),9,f)]}),_:1},8,["modelValue","input-class-name"]),bt.errors.date?((0,l.openBlock)(),(0,l.createElementBlock)("small",p,(0,l.toDisplayString)(bt.errors.date[0]),1)):(0,l.createCommentVNode)("",!0)]),(0,l.createElementVNode)("div",h,[(0,l.createElementVNode)("label",b,(0,l.toDisplayString)(t.$t("label.reference_no")),1),(0,l.withDirectives)((0,l.createElementVNode)("input",{name:"reference_no","onUpdate:modelValue":e[1]||(e[1]=function(t){return bt.props.form.reference_no=t}),type:"text",class:"db-field-control"},null,512),[[l.vModelText,bt.props.form.reference_no]]),bt.errors.reference_no?((0,l.openBlock)(),(0,l.createElementBlock)("small",q,(0,l.toDisplayString)(bt.errors.reference_no[0]),1)):(0,l.createCommentVNode)("",!0)]),(0,l.createElementVNode)("div",y,[(0,l.createElementVNode)("label",v,(0,l.toDisplayString)(t.$t("label.customer")),1),(0,l.createVNode)(mt,{modelValue:bt.props.form.user_id,"onUpdate:modelValue":e[2]||(e[2]=function(t){return bt.props.form.user_id=t}),class:"db-field-control f-b-custom-select",options:qt.users,"label-by":"name","value-by":"id",closeOnSelect:!0,searchable:!0,clearOnClose:!0,placeholder:"--","search-placeholder":"--"},null,8,["modelValue","options"]),bt.errors.user_id?((0,l.openBlock)(),(0,l.createElementBlock)("small",m,(0,l.toDisplayString)(bt.errors.user_id[0]),1)):(0,l.createCommentVNode)("",!0)]),(0,l.createElementVNode)("div",g,[(0,l.createElementVNode)("label",k,(0,l.toDisplayString)(t.$t("label.attachments")),1),(0,l.createElementVNode)("input",{onChange:e[3]||(e[3]=function(){return qt.changeFile&&qt.changeFile.apply(qt,arguments)}),class:(0,l.normalizeClass)([bt.errors.file?"invalid":"","db-field-control cursor-pointer"]),type:"file",ref:"fileProperty",accept:"image/png , image/jpeg, image/jpg , application/pdf ",id:"image"},null,34),bt.errors.file?((0,l.openBlock)(),(0,l.createElementBlock)("small",_,(0,l.toDisplayString)(bt.errors.file[0]),1)):(0,l.createCommentVNode)("",!0)]),(0,l.createElementVNode)("div",w,[(0,l.createElementVNode)("div",x,[(0,l.createElementVNode)("h4",O,(0,l.toDisplayString)(t.$t("message.selection_message")),1),(0,l.createElementVNode)("div",E,[(0,l.createElementVNode)("div",N,[(0,l.createElementVNode)("label",A,(0,l.toDisplayString)(t.$t("label.add_products")),1),(0,l.createElementVNode)("div",j,[e[10]||(e[10]=(0,l.createElementVNode)("button",{type:"button",class:"lab-line-qrcode absolute top-1/2 -translate-y-1/2 left-4 z-10 cursor-pointer"},null,-1)),(0,l.createVNode)(mt,{class:"h-full pr-4 pl-11",modelValue:bt.productId,"onUpdate:modelValue":[e[4]||(e[4]=function(t){return bt.productId=t}),e[5]||(e[5]=function(t){return qt.selectProduct(t)})],options:qt.products,"label-by":"name","value-by":"id",closeOnSelect:!0,searchable:!0,clearOnClose:!0,placeholder:t.$t("label.select_one"),"search-placeholder":"--"},null,8,["modelValue","options","placeholder"])]),bt.errors.products?((0,l.openBlock)(),(0,l.createElementBlock)("small",S,(0,l.toDisplayString)(bt.errors.products[0]),1)):(0,l.createCommentVNode)("",!0)])])])]),(0,l.createElementVNode)("div",T,[(0,l.createElementVNode)("label",P,(0,l.toDisplayString)(t.$t("label.products")),1),(0,l.createElementVNode)("div",C,[(0,l.createElementVNode)("table",L,[(0,l.createElementVNode)("thead",M,[(0,l.createElementVNode)("tr",I,[(0,l.createElementVNode)("th",R,(0,l.toDisplayString)(t.$t("label.product")),1),(0,l.createElementVNode)("th",V,(0,l.toDisplayString)(t.$t("label.unit_cost")),1),(0,l.createElementVNode)("th",B,(0,l.toDisplayString)(t.$t("label.quantity")),1),(0,l.createElementVNode)("th",D,(0,l.toDisplayString)(t.$t("label.discount")),1),(0,l.createElementVNode)("th",z,(0,l.toDisplayString)(t.$t("label.taxes")),1),(0,l.createElementVNode)("th",F,(0,l.toDisplayString)(t.$t("label.sub_total")),1),(0,l.createElementVNode)("th",U,(0,l.toDisplayString)(t.$t("label.actions")),1)])]),(0,l.createElementVNode)("tbody",H,[((0,l.openBlock)(!0),(0,l.createElementBlock)(l.Fragment,null,(0,l.renderList)(bt.datatable,function(o,n){return(0,l.openBlock)(),(0,l.createElementBlock)("tr",{key:n,class:"db-table-body-tr"},[(0,l.createElementVNode)("td",K,[(0,l.createTextVNode)((0,l.toDisplayString)(o.name)+" ",1),o.variation_names?((0,l.openBlock)(),(0,l.createElementBlock)("span",$," ( "+(0,l.toDisplayString)(t.$t("label.variation"))+" : "+(0,l.toDisplayString)(o.variation_names)+") ",1)):(0,l.createCommentVNode)("",!0)]),(0,l.createElementVNode)("td",Z,(0,l.toDisplayString)(qt.floatFormat(o.price)),1),(0,l.createElementVNode)("td",W,[(0,l.withDirectives)((0,l.createElementVNode)("input",{onKeypress:e[6]||(e[6]=function(t){return qt.onlyNumber(t)}),onKeyup:function(t){return qt.updateQuantity(n)},"onUpdate:modelValue":function(t){return o.quantity=t},onClick:e[7]||(e[7]=function(t){return t.target.select()}),type:"number",min:"1",class:"db-field-control"},null,40,G),[[l.vModelText,o.quantity]])]),(0,l.createElementVNode)("td",Y,(0,l.toDisplayString)(0===Number(o.total_discount)?"":"-")+" "+(0,l.toDisplayString)(qt.floatFormat(o.total_discount)),1),(0,l.createElementVNode)("td",Q,(0,l.toDisplayString)(qt.floatFormat(o.total_tax))+" ("+(0,l.toDisplayString)(qt.floatFormat(o.total_tax_rate))+" %) ",1),(0,l.createElementVNode)("td",X,(0,l.toDisplayString)(qt.floatFormat(o.total)),1),(0,l.createElementVNode)("td",J,[(0,l.createVNode)(gt,{onClick:(0,l.withModifiers)(function(t){return qt.editDatatable(n)},["prevent"])},null,8,["onClick"]),(0,l.createVNode)(kt,{onClick:(0,l.withModifiers)(function(t){return qt.removeProduct(n)},["prevent"])},null,8,["onClick"])])])}),128)),(0,l.createElementVNode)("tr",null,[(0,l.createElementVNode)("th",tt,(0,l.toDisplayString)(t.$t("label.total")),1),(0,l.createElementVNode)("th",et,[(0,l.createElementVNode)("span",ot,(0,l.toDisplayString)(Number.isInteger(qt.totalQuantity)?qt.totalQuantity:0),1)]),(0,l.createElementVNode)("th",lt,(0,l.toDisplayString)(qt.floatFormat(qt.totalDiscount)),1),(0,l.createElementVNode)("th",nt,(0,l.toDisplayString)(qt.floatFormat(qt.totalTax)),1),(0,l.createElementVNode)("th",it,(0,l.toDisplayString)(qt.floatFormat(qt.totalPrice)),1),e[11]||(e[11]=(0,l.createElementVNode)("th",{class:"db-table-body-td"},null,-1))])])])])]),(0,l.createVNode)(_t,{item:bt.selectedProduct,modal:bt.modal,onSubmitItem:qt.modalSubmit,ref:"productModal"},null,8,["item","modal","onSubmitItem"])]),(0,l.createElementVNode)("div",rt,[(0,l.createElementVNode)("div",at,[(0,l.createElementVNode)("div",{class:(0,l.normalizeClass)(bt.errors.note?"invalid textarea-error-box-style":"")},[(0,l.createElementVNode)("label",st,(0,l.toDisplayString)(t.$t("label.reason")),1),(0,l.createVNode)(wt,{value:bt.props.form.reason,"onUpdate:value":e[8]||(e[8]=function(t){return bt.props.form.reason=t}),class:"!h-40 textarea-border-radius"},null,8,["value"]),bt.errors.reason?((0,l.openBlock)(),(0,l.createElementBlock)("small",ct,(0,l.toDisplayString)(bt.errors.reason[0]),1)):(0,l.createCommentVNode)("",!0)],2)]),(0,l.createElementVNode)("div",ut,[(0,l.createElementVNode)("div",dt,[qt.permissionChecker("return_order_create")?((0,l.openBlock)(),(0,l.createElementBlock)("button",ft,[e[12]||(e[12]=(0,l.createElementVNode)("i",{class:"fa-solid fa-circle-check"},null,-1)),(0,l.createElementVNode)("span",pt,(0,l.toDisplayString)(t.$t("button.save")),1)])):(0,l.createCommentVNode)("",!0)])])])])])],32)])],64)}]])},1574:function(t,e,o){var l,n=o(8287).hp;"undefined"!=typeof self&&self,l=function(){return function(t){var e={};function o(l){if(e[l])return e[l].exports;var n=e[l]={i:l,l:!1,exports:{}};return t[l].call(n.exports,n,n.exports,o),n.l=!0,n.exports}return o.m=t,o.c=e,o.d=function(t,e,l){o.o(t,e)||Object.defineProperty(t,e,{configurable:!1,enumerable:!0,get:l})},o.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return o.d(e,"a",e),e},o.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},o.p="",o(o.s=109)}([function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var l=o(17),n=o(18),i=o(19),r=o(45),a=o(46),s=o(47),c=o(48),u=o(49),d=o(12),f=o(32),p=o(33),h=o(31),b=o(1),q={Scope:b.Scope,create:b.create,find:b.find,query:b.query,register:b.register,Container:l.default,Format:n.default,Leaf:i.default,Embed:c.default,Scroll:r.default,Block:s.default,Inline:a.default,Text:u.default,Attributor:{Attribute:d.default,Class:f.default,Style:p.default,Store:h.default}};e.default=q},function(t,e,o){"use strict";var l,n=this&&this.__extends||(l=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)e.hasOwnProperty(o)&&(t[o]=e[o])},function(t,e){function o(){this.constructor=t}l(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)});Object.defineProperty(e,"__esModule",{value:!0});var i=function(t){function e(e){var o=this;return e="[Parchment] "+e,(o=t.call(this,e)||this).message=e,o.name=o.constructor.name,o}return n(e,t),e}(Error);e.ParchmentError=i;var r,a={},s={},c={},u={};function d(t,e){var o;if(void 0===e&&(e=r.ANY),"string"==typeof t)o=u[t]||a[t];else if(t instanceof Text||t.nodeType===Node.TEXT_NODE)o=u.text;else if("number"==typeof t)t&r.LEVEL&r.BLOCK?o=u.block:t&r.LEVEL&r.INLINE&&(o=u.inline);else if(t instanceof HTMLElement){var l=(t.getAttribute("class")||"").split(/\s+/);for(var n in l)if(o=s[l[n]])break;o=o||c[t.tagName]}return null==o?null:e&r.LEVEL&o.scope&&e&r.TYPE&o.scope?o:null}e.DATA_KEY="__blot",function(t){t[t.TYPE=3]="TYPE",t[t.LEVEL=12]="LEVEL",t[t.ATTRIBUTE=13]="ATTRIBUTE",t[t.BLOT=14]="BLOT",t[t.INLINE=7]="INLINE",t[t.BLOCK=11]="BLOCK",t[t.BLOCK_BLOT=10]="BLOCK_BLOT",t[t.INLINE_BLOT=6]="INLINE_BLOT",t[t.BLOCK_ATTRIBUTE=9]="BLOCK_ATTRIBUTE",t[t.INLINE_ATTRIBUTE=5]="INLINE_ATTRIBUTE",t[t.ANY=15]="ANY"}(r=e.Scope||(e.Scope={})),e.create=function(t,e){var o=d(t);if(null==o)throw new i("Unable to create "+t+" blot");var l=o,n=t instanceof Node||t.nodeType===Node.TEXT_NODE?t:l.create(e);return new l(n,e)},e.find=function t(o,l){return void 0===l&&(l=!1),null==o?null:null!=o[e.DATA_KEY]?o[e.DATA_KEY].blot:l?t(o.parentNode,l):null},e.query=d,e.register=function t(){for(var e=[],o=0;o<arguments.length;o++)e[o]=arguments[o];if(e.length>1)return e.map(function(e){return t(e)});var l=e[0];if("string"!=typeof l.blotName&&"string"!=typeof l.attrName)throw new i("Invalid definition");if("abstract"===l.blotName)throw new i("Cannot register abstract class");return u[l.blotName||l.attrName]=l,"string"==typeof l.keyName?a[l.keyName]=l:(null!=l.className&&(s[l.className]=l),null!=l.tagName&&(Array.isArray(l.tagName)?l.tagName=l.tagName.map(function(t){return t.toUpperCase()}):l.tagName=l.tagName.toUpperCase(),(Array.isArray(l.tagName)?l.tagName:[l.tagName]).forEach(function(t){null!=c[t]&&null!=l.className||(c[t]=l)}))),l}},function(t,e,o){var l=o(51),n=o(11),i=o(3),r=o(20),a=String.fromCharCode(0),s=function(t){Array.isArray(t)?this.ops=t:null!=t&&Array.isArray(t.ops)?this.ops=t.ops:this.ops=[]};s.prototype.insert=function(t,e){var o={};return 0===t.length?this:(o.insert=t,null!=e&&"object"==typeof e&&Object.keys(e).length>0&&(o.attributes=e),this.push(o))},s.prototype.delete=function(t){return t<=0?this:this.push({delete:t})},s.prototype.retain=function(t,e){if(t<=0)return this;var o={retain:t};return null!=e&&"object"==typeof e&&Object.keys(e).length>0&&(o.attributes=e),this.push(o)},s.prototype.push=function(t){var e=this.ops.length,o=this.ops[e-1];if(t=i(!0,{},t),"object"==typeof o){if("number"==typeof t.delete&&"number"==typeof o.delete)return this.ops[e-1]={delete:o.delete+t.delete},this;if("number"==typeof o.delete&&null!=t.insert&&(e-=1,"object"!=typeof(o=this.ops[e-1])))return this.ops.unshift(t),this;if(n(t.attributes,o.attributes)){if("string"==typeof t.insert&&"string"==typeof o.insert)return this.ops[e-1]={insert:o.insert+t.insert},"object"==typeof t.attributes&&(this.ops[e-1].attributes=t.attributes),this;if("number"==typeof t.retain&&"number"==typeof o.retain)return this.ops[e-1]={retain:o.retain+t.retain},"object"==typeof t.attributes&&(this.ops[e-1].attributes=t.attributes),this}}return e===this.ops.length?this.ops.push(t):this.ops.splice(e,0,t),this},s.prototype.chop=function(){var t=this.ops[this.ops.length-1];return t&&t.retain&&!t.attributes&&this.ops.pop(),this},s.prototype.filter=function(t){return this.ops.filter(t)},s.prototype.forEach=function(t){this.ops.forEach(t)},s.prototype.map=function(t){return this.ops.map(t)},s.prototype.partition=function(t){var e=[],o=[];return this.forEach(function(l){(t(l)?e:o).push(l)}),[e,o]},s.prototype.reduce=function(t,e){return this.ops.reduce(t,e)},s.prototype.changeLength=function(){return this.reduce(function(t,e){return e.insert?t+r.length(e):e.delete?t-e.delete:t},0)},s.prototype.length=function(){return this.reduce(function(t,e){return t+r.length(e)},0)},s.prototype.slice=function(t,e){t=t||0,"number"!=typeof e&&(e=1/0);for(var o=[],l=r.iterator(this.ops),n=0;n<e&&l.hasNext();){var i;n<t?i=l.next(t-n):(i=l.next(e-n),o.push(i)),n+=r.length(i)}return new s(o)},s.prototype.compose=function(t){var e=r.iterator(this.ops),o=r.iterator(t.ops),l=[],i=o.peek();if(null!=i&&"number"==typeof i.retain&&null==i.attributes){for(var a=i.retain;"insert"===e.peekType()&&e.peekLength()<=a;)a-=e.peekLength(),l.push(e.next());i.retain-a>0&&o.next(i.retain-a)}for(var c=new s(l);e.hasNext()||o.hasNext();)if("insert"===o.peekType())c.push(o.next());else if("delete"===e.peekType())c.push(e.next());else{var u=Math.min(e.peekLength(),o.peekLength()),d=e.next(u),f=o.next(u);if("number"==typeof f.retain){var p={};"number"==typeof d.retain?p.retain=u:p.insert=d.insert;var h=r.attributes.compose(d.attributes,f.attributes,"number"==typeof d.retain);if(h&&(p.attributes=h),c.push(p),!o.hasNext()&&n(c.ops[c.ops.length-1],p)){var b=new s(e.rest());return c.concat(b).chop()}}else"number"==typeof f.delete&&"number"==typeof d.retain&&c.push(f)}return c.chop()},s.prototype.concat=function(t){var e=new s(this.ops.slice());return t.ops.length>0&&(e.push(t.ops[0]),e.ops=e.ops.concat(t.ops.slice(1))),e},s.prototype.diff=function(t,e){if(this.ops===t.ops)return new s;var o=[this,t].map(function(e){return e.map(function(o){if(null!=o.insert)return"string"==typeof o.insert?o.insert:a;throw new Error("diff() called "+(e===t?"on":"with")+" non-document")}).join("")}),i=new s,c=l(o[0],o[1],e),u=r.iterator(this.ops),d=r.iterator(t.ops);return c.forEach(function(t){for(var e=t[1].length;e>0;){var o=0;switch(t[0]){case l.INSERT:o=Math.min(d.peekLength(),e),i.push(d.next(o));break;case l.DELETE:o=Math.min(e,u.peekLength()),u.next(o),i.delete(o);break;case l.EQUAL:o=Math.min(u.peekLength(),d.peekLength(),e);var a=u.next(o),s=d.next(o);n(a.insert,s.insert)?i.retain(o,r.attributes.diff(a.attributes,s.attributes)):i.push(s).delete(o)}e-=o}}),i.chop()},s.prototype.eachLine=function(t,e){e=e||"\n";for(var o=r.iterator(this.ops),l=new s,n=0;o.hasNext();){if("insert"!==o.peekType())return;var i=o.peek(),a=r.length(i)-o.peekLength(),c="string"==typeof i.insert?i.insert.indexOf(e,a)-a:-1;if(c<0)l.push(o.next());else if(c>0)l.push(o.next(c));else{if(!1===t(l,o.next(1).attributes||{},n))return;n+=1,l=new s}}l.length()>0&&t(l,{},n)},s.prototype.transform=function(t,e){if(e=!!e,"number"==typeof t)return this.transformPosition(t,e);for(var o=r.iterator(this.ops),l=r.iterator(t.ops),n=new s;o.hasNext()||l.hasNext();)if("insert"!==o.peekType()||!e&&"insert"===l.peekType())if("insert"===l.peekType())n.push(l.next());else{var i=Math.min(o.peekLength(),l.peekLength()),a=o.next(i),c=l.next(i);if(a.delete)continue;c.delete?n.push(c):n.retain(i,r.attributes.transform(a.attributes,c.attributes,e))}else n.retain(r.length(o.next()));return n.chop()},s.prototype.transformPosition=function(t,e){e=!!e;for(var o=r.iterator(this.ops),l=0;o.hasNext()&&l<=t;){var n=o.peekLength(),i=o.peekType();o.next(),"delete"!==i?("insert"===i&&(l<t||!e)&&(t+=n),l+=n):t-=Math.min(n,t-l)}return t},t.exports=s},function(t,e){"use strict";var o=Object.prototype.hasOwnProperty,l=Object.prototype.toString,n=Object.defineProperty,i=Object.getOwnPropertyDescriptor,r=function(t){return"function"==typeof Array.isArray?Array.isArray(t):"[object Array]"===l.call(t)},a=function(t){if(!t||"[object Object]"!==l.call(t))return!1;var e,n=o.call(t,"constructor"),i=t.constructor&&t.constructor.prototype&&o.call(t.constructor.prototype,"isPrototypeOf");if(t.constructor&&!n&&!i)return!1;for(e in t);return void 0===e||o.call(t,e)},s=function(t,e){n&&"__proto__"===e.name?n(t,e.name,{enumerable:!0,configurable:!0,value:e.newValue,writable:!0}):t[e.name]=e.newValue},c=function(t,e){if("__proto__"===e){if(!o.call(t,e))return;if(i)return i(t,e).value}return t[e]};t.exports=function t(){var e,o,l,n,i,u,d=arguments[0],f=1,p=arguments.length,h=!1;for("boolean"==typeof d&&(h=d,d=arguments[1]||{},f=2),(null==d||"object"!=typeof d&&"function"!=typeof d)&&(d={});f<p;++f)if(null!=(e=arguments[f]))for(o in e)l=c(d,o),d!==(n=c(e,o))&&(h&&n&&(a(n)||(i=r(n)))?(i?(i=!1,u=l&&r(l)?l:[]):u=l&&a(l)?l:{},s(d,{name:o,newValue:t(h,u,n)})):void 0!==n&&s(d,{name:o,newValue:n}));return d}},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=e.BlockEmbed=e.bubbleFormats=void 0;var l=function(){function t(t,e){for(var o=0;o<e.length;o++){var l=e[o];l.enumerable=l.enumerable||!1,l.configurable=!0,"value"in l&&(l.writable=!0),Object.defineProperty(t,l.key,l)}}return function(e,o,l){return o&&t(e.prototype,o),l&&t(e,l),e}}(),n=function t(e,o,l){null===e&&(e=Function.prototype);var n=Object.getOwnPropertyDescriptor(e,o);if(void 0===n){var i=Object.getPrototypeOf(e);return null===i?void 0:t(i,o,l)}if("value"in n)return n.value;var r=n.get;return void 0!==r?r.call(l):void 0},i=d(o(3)),r=d(o(2)),a=d(o(0)),s=d(o(16)),c=d(o(6)),u=d(o(7));function d(t){return t&&t.__esModule?t:{default:t}}function f(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function p(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}function h(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var b=function(t){function e(){return f(this,e),p(this,(e.__proto__||Object.getPrototypeOf(e)).apply(this,arguments))}return h(e,t),l(e,[{key:"attach",value:function(){n(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"attach",this).call(this),this.attributes=new a.default.Attributor.Store(this.domNode)}},{key:"delta",value:function(){return(new r.default).insert(this.value(),(0,i.default)(this.formats(),this.attributes.values()))}},{key:"format",value:function(t,e){var o=a.default.query(t,a.default.Scope.BLOCK_ATTRIBUTE);null!=o&&this.attributes.attribute(o,e)}},{key:"formatAt",value:function(t,e,o,l){this.format(o,l)}},{key:"insertAt",value:function(t,o,l){if("string"==typeof o&&o.endsWith("\n")){var i=a.default.create(q.blotName);this.parent.insertBefore(i,0===t?this:this.next),i.insertAt(0,o.slice(0,-1))}else n(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"insertAt",this).call(this,t,o,l)}}]),e}(a.default.Embed);b.scope=a.default.Scope.BLOCK_BLOT;var q=function(t){function e(t){f(this,e);var o=p(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,t));return o.cache={},o}return h(e,t),l(e,[{key:"delta",value:function(){return null==this.cache.delta&&(this.cache.delta=this.descendants(a.default.Leaf).reduce(function(t,e){return 0===e.length()?t:t.insert(e.value(),y(e))},new r.default).insert("\n",y(this))),this.cache.delta}},{key:"deleteAt",value:function(t,o){n(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"deleteAt",this).call(this,t,o),this.cache={}}},{key:"formatAt",value:function(t,o,l,i){o<=0||(a.default.query(l,a.default.Scope.BLOCK)?t+o===this.length()&&this.format(l,i):n(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"formatAt",this).call(this,t,Math.min(o,this.length()-t-1),l,i),this.cache={})}},{key:"insertAt",value:function(t,o,l){if(null!=l)return n(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"insertAt",this).call(this,t,o,l);if(0!==o.length){var i=o.split("\n"),r=i.shift();r.length>0&&(t<this.length()-1||null==this.children.tail?n(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"insertAt",this).call(this,Math.min(t,this.length()-1),r):this.children.tail.insertAt(this.children.tail.length(),r),this.cache={});var a=this;i.reduce(function(t,e){return(a=a.split(t,!0)).insertAt(0,e),e.length},t+r.length)}}},{key:"insertBefore",value:function(t,o){var l=this.children.head;n(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"insertBefore",this).call(this,t,o),l instanceof s.default&&l.remove(),this.cache={}}},{key:"length",value:function(){return null==this.cache.length&&(this.cache.length=n(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"length",this).call(this)+1),this.cache.length}},{key:"moveChildren",value:function(t,o){n(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"moveChildren",this).call(this,t,o),this.cache={}}},{key:"optimize",value:function(t){n(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"optimize",this).call(this,t),this.cache={}}},{key:"path",value:function(t){return n(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"path",this).call(this,t,!0)}},{key:"removeChild",value:function(t){n(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"removeChild",this).call(this,t),this.cache={}}},{key:"split",value:function(t){var o=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(o&&(0===t||t>=this.length()-1)){var l=this.clone();return 0===t?(this.parent.insertBefore(l,this),this):(this.parent.insertBefore(l,this.next),l)}var i=n(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"split",this).call(this,t,o);return this.cache={},i}}]),e}(a.default.Block);function y(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return null==t?e:("function"==typeof t.formats&&(e=(0,i.default)(e,t.formats())),null==t.parent||"scroll"==t.parent.blotName||t.parent.statics.scope!==t.statics.scope?e:y(t.parent,e))}q.blotName="block",q.tagName="P",q.defaultChild="break",q.allowedChildren=[c.default,a.default.Embed,u.default],e.bubbleFormats=y,e.BlockEmbed=b,e.default=q},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=e.overload=e.expandConfig=void 0;var l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},n=function(t,e){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return function(t,e){var o=[],l=!0,n=!1,i=void 0;try{for(var r,a=t[Symbol.iterator]();!(l=(r=a.next()).done)&&(o.push(r.value),!e||o.length!==e);l=!0);}catch(t){n=!0,i=t}finally{try{!l&&a.return&&a.return()}finally{if(n)throw i}}return o}(t,e);throw new TypeError("Invalid attempt to destructure non-iterable instance")},i=function(){function t(t,e){for(var o=0;o<e.length;o++){var l=e[o];l.enumerable=l.enumerable||!1,l.configurable=!0,"value"in l&&(l.writable=!0),Object.defineProperty(t,l.key,l)}}return function(e,o,l){return o&&t(e.prototype,o),l&&t(e,l),e}}();o(50);var r=q(o(2)),a=q(o(14)),s=q(o(8)),c=q(o(9)),u=q(o(0)),d=o(15),f=q(d),p=q(o(3)),h=q(o(10)),b=q(o(34));function q(t){return t&&t.__esModule?t:{default:t}}function y(t,e,o){return e in t?Object.defineProperty(t,e,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[e]=o,t}var v=(0,h.default)("quill"),m=function(){function t(e){var o=this,l=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.options=g(e,l),this.container=this.options.container,null==this.container)return v.error("Invalid Quill container",e);this.options.debug&&t.debug(this.options.debug);var n=this.container.innerHTML.trim();this.container.classList.add("ql-container"),this.container.innerHTML="",this.container.__quill=this,this.root=this.addContainer("ql-editor"),this.root.classList.add("ql-blank"),this.root.setAttribute("data-gramm",!1),this.scrollingContainer=this.options.scrollingContainer||this.root,this.emitter=new s.default,this.scroll=u.default.create(this.root,{emitter:this.emitter,whitelist:this.options.formats}),this.editor=new a.default(this.scroll),this.selection=new f.default(this.scroll,this.emitter),this.theme=new this.options.theme(this,this.options),this.keyboard=this.theme.addModule("keyboard"),this.clipboard=this.theme.addModule("clipboard"),this.history=this.theme.addModule("history"),this.theme.init(),this.emitter.on(s.default.events.EDITOR_CHANGE,function(t){t===s.default.events.TEXT_CHANGE&&o.root.classList.toggle("ql-blank",o.editor.isBlank())}),this.emitter.on(s.default.events.SCROLL_UPDATE,function(t,e){var l=o.selection.lastRange,n=l&&0===l.length?l.index:void 0;k.call(o,function(){return o.editor.update(null,e,n)},t)});var i=this.clipboard.convert("<div class='ql-editor' style=\"white-space: normal;\">"+n+"<p><br></p></div>");this.setContents(i),this.history.clear(),this.options.placeholder&&this.root.setAttribute("data-placeholder",this.options.placeholder),this.options.readOnly&&this.disable()}return i(t,null,[{key:"debug",value:function(t){!0===t&&(t="log"),h.default.level(t)}},{key:"find",value:function(t){return t.__quill||u.default.find(t)}},{key:"import",value:function(t){return null==this.imports[t]&&v.error("Cannot import "+t+". Are you sure it was registered?"),this.imports[t]}},{key:"register",value:function(t,e){var o=this,l=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if("string"!=typeof t){var n=t.attrName||t.blotName;"string"==typeof n?this.register("formats/"+n,t,e):Object.keys(t).forEach(function(l){o.register(l,t[l],e)})}else null==this.imports[t]||l||v.warn("Overwriting "+t+" with",e),this.imports[t]=e,(t.startsWith("blots/")||t.startsWith("formats/"))&&"abstract"!==e.blotName?u.default.register(e):t.startsWith("modules")&&"function"==typeof e.register&&e.register()}}]),i(t,[{key:"addContainer",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if("string"==typeof t){var o=t;(t=document.createElement("div")).classList.add(o)}return this.container.insertBefore(t,e),t}},{key:"blur",value:function(){this.selection.setRange(null)}},{key:"deleteText",value:function(t,e,o){var l=this,i=_(t,e,o),r=n(i,4);return t=r[0],e=r[1],o=r[3],k.call(this,function(){return l.editor.deleteText(t,e)},o,t,-1*e)}},{key:"disable",value:function(){this.enable(!1)}},{key:"enable",value:function(){var t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];this.scroll.enable(t),this.container.classList.toggle("ql-disabled",!t)}},{key:"focus",value:function(){var t=this.scrollingContainer.scrollTop;this.selection.focus(),this.scrollingContainer.scrollTop=t,this.scrollIntoView()}},{key:"format",value:function(t,e){var o=this,l=arguments.length>2&&void 0!==arguments[2]?arguments[2]:s.default.sources.API;return k.call(this,function(){var l=o.getSelection(!0),n=new r.default;if(null==l)return n;if(u.default.query(t,u.default.Scope.BLOCK))n=o.editor.formatLine(l.index,l.length,y({},t,e));else{if(0===l.length)return o.selection.format(t,e),n;n=o.editor.formatText(l.index,l.length,y({},t,e))}return o.setSelection(l,s.default.sources.SILENT),n},l)}},{key:"formatLine",value:function(t,e,o,l,i){var r,a=this,s=_(t,e,o,l,i),c=n(s,4);return t=c[0],e=c[1],r=c[2],i=c[3],k.call(this,function(){return a.editor.formatLine(t,e,r)},i,t,0)}},{key:"formatText",value:function(t,e,o,l,i){var r,a=this,s=_(t,e,o,l,i),c=n(s,4);return t=c[0],e=c[1],r=c[2],i=c[3],k.call(this,function(){return a.editor.formatText(t,e,r)},i,t,0)}},{key:"getBounds",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,o=void 0;o="number"==typeof t?this.selection.getBounds(t,e):this.selection.getBounds(t.index,t.length);var l=this.container.getBoundingClientRect();return{bottom:o.bottom-l.top,height:o.height,left:o.left-l.left,right:o.right-l.left,top:o.top-l.top,width:o.width}}},{key:"getContents",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.getLength()-t,o=_(t,e),l=n(o,2);return t=l[0],e=l[1],this.editor.getContents(t,e)}},{key:"getFormat",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.getSelection(!0),e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return"number"==typeof t?this.editor.getFormat(t,e):this.editor.getFormat(t.index,t.length)}},{key:"getIndex",value:function(t){return t.offset(this.scroll)}},{key:"getLength",value:function(){return this.scroll.length()}},{key:"getLeaf",value:function(t){return this.scroll.leaf(t)}},{key:"getLine",value:function(t){return this.scroll.line(t)}},{key:"getLines",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Number.MAX_VALUE;return"number"!=typeof t?this.scroll.lines(t.index,t.length):this.scroll.lines(t,e)}},{key:"getModule",value:function(t){return this.theme.modules[t]}},{key:"getSelection",value:function(){return arguments.length>0&&void 0!==arguments[0]&&arguments[0]&&this.focus(),this.update(),this.selection.getRange()[0]}},{key:"getText",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.getLength()-t,o=_(t,e),l=n(o,2);return t=l[0],e=l[1],this.editor.getText(t,e)}},{key:"hasFocus",value:function(){return this.selection.hasFocus()}},{key:"insertEmbed",value:function(e,o,l){var n=this,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:t.sources.API;return k.call(this,function(){return n.editor.insertEmbed(e,o,l)},i,e)}},{key:"insertText",value:function(t,e,o,l,i){var r,a=this,s=_(t,0,o,l,i),c=n(s,4);return t=c[0],r=c[2],i=c[3],k.call(this,function(){return a.editor.insertText(t,e,r)},i,t,e.length)}},{key:"isEnabled",value:function(){return!this.container.classList.contains("ql-disabled")}},{key:"off",value:function(){return this.emitter.off.apply(this.emitter,arguments)}},{key:"on",value:function(){return this.emitter.on.apply(this.emitter,arguments)}},{key:"once",value:function(){return this.emitter.once.apply(this.emitter,arguments)}},{key:"pasteHTML",value:function(t,e,o){this.clipboard.dangerouslyPasteHTML(t,e,o)}},{key:"removeFormat",value:function(t,e,o){var l=this,i=_(t,e,o),r=n(i,4);return t=r[0],e=r[1],o=r[3],k.call(this,function(){return l.editor.removeFormat(t,e)},o,t)}},{key:"scrollIntoView",value:function(){this.selection.scrollIntoView(this.scrollingContainer)}},{key:"setContents",value:function(t){var e=this,o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:s.default.sources.API;return k.call(this,function(){t=new r.default(t);var o=e.getLength(),l=e.editor.deleteText(0,o),n=e.editor.applyDelta(t),i=n.ops[n.ops.length-1];return null!=i&&"string"==typeof i.insert&&"\n"===i.insert[i.insert.length-1]&&(e.editor.deleteText(e.getLength()-1,1),n.delete(1)),l.compose(n)},o)}},{key:"setSelection",value:function(e,o,l){if(null==e)this.selection.setRange(null,o||t.sources.API);else{var i=_(e,o,l),r=n(i,4);e=r[0],o=r[1],l=r[3],this.selection.setRange(new d.Range(e,o),l),l!==s.default.sources.SILENT&&this.selection.scrollIntoView(this.scrollingContainer)}}},{key:"setText",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:s.default.sources.API,o=(new r.default).insert(t);return this.setContents(o,e)}},{key:"update",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:s.default.sources.USER,e=this.scroll.update(t);return this.selection.update(t),e}},{key:"updateContents",value:function(t){var e=this,o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:s.default.sources.API;return k.call(this,function(){return t=new r.default(t),e.editor.applyDelta(t,o)},o,!0)}}]),t}();function g(t,e){if((e=(0,p.default)(!0,{container:t,modules:{clipboard:!0,keyboard:!0,history:!0}},e)).theme&&e.theme!==m.DEFAULTS.theme){if(e.theme=m.import("themes/"+e.theme),null==e.theme)throw new Error("Invalid theme "+e.theme+". Did you register it?")}else e.theme=b.default;var o=(0,p.default)(!0,{},e.theme.DEFAULTS);[o,e].forEach(function(t){t.modules=t.modules||{},Object.keys(t.modules).forEach(function(e){!0===t.modules[e]&&(t.modules[e]={})})});var l=Object.keys(o.modules).concat(Object.keys(e.modules)).reduce(function(t,e){var o=m.import("modules/"+e);return null==o?v.error("Cannot load "+e+" module. Are you sure you registered it?"):t[e]=o.DEFAULTS||{},t},{});return null!=e.modules&&e.modules.toolbar&&e.modules.toolbar.constructor!==Object&&(e.modules.toolbar={container:e.modules.toolbar}),e=(0,p.default)(!0,{},m.DEFAULTS,{modules:l},o,e),["bounds","container","scrollingContainer"].forEach(function(t){"string"==typeof e[t]&&(e[t]=document.querySelector(e[t]))}),e.modules=Object.keys(e.modules).reduce(function(t,o){return e.modules[o]&&(t[o]=e.modules[o]),t},{}),e}function k(t,e,o,l){if(this.options.strict&&!this.isEnabled()&&e===s.default.sources.USER)return new r.default;var n=null==o?null:this.getSelection(),i=this.editor.delta,a=t();if(null!=n&&(!0===o&&(o=n.index),null==l?n=w(n,a,e):0!==l&&(n=w(n,o,l,e)),this.setSelection(n,s.default.sources.SILENT)),a.length()>0){var c,u,d=[s.default.events.TEXT_CHANGE,a,i,e];(c=this.emitter).emit.apply(c,[s.default.events.EDITOR_CHANGE].concat(d)),e!==s.default.sources.SILENT&&(u=this.emitter).emit.apply(u,d)}return a}function _(t,e,o,n,i){var r={};return"number"==typeof t.index&&"number"==typeof t.length?"number"!=typeof e?(i=n,n=o,o=e,e=t.length,t=t.index):(e=t.length,t=t.index):"number"!=typeof e&&(i=n,n=o,o=e,e=0),"object"===(void 0===o?"undefined":l(o))?(r=o,i=n):"string"==typeof o&&(null!=n?r[o]=n:i=o),[t,e,r,i=i||s.default.sources.API]}function w(t,e,o,l){if(null==t)return null;var i=void 0,a=void 0;if(e instanceof r.default){var c=[t.index,t.index+t.length].map(function(t){return e.transformPosition(t,l!==s.default.sources.USER)}),u=n(c,2);i=u[0],a=u[1]}else{var f=[t.index,t.index+t.length].map(function(t){return t<e||t===e&&l===s.default.sources.USER?t:o>=0?t+o:Math.max(e,t+o)}),p=n(f,2);i=p[0],a=p[1]}return new d.Range(i,a-i)}m.DEFAULTS={bounds:null,formats:null,modules:{},placeholder:"",readOnly:!1,scrollingContainer:null,strict:!0,theme:"default"},m.events=s.default.events,m.sources=s.default.sources,m.version="1.3.7",m.imports={delta:r.default,parchment:u.default,"core/module":c.default,"core/theme":b.default},e.expandConfig=g,e.overload=_,e.default=m},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var l=function(){function t(t,e){for(var o=0;o<e.length;o++){var l=e[o];l.enumerable=l.enumerable||!1,l.configurable=!0,"value"in l&&(l.writable=!0),Object.defineProperty(t,l.key,l)}}return function(e,o,l){return o&&t(e.prototype,o),l&&t(e,l),e}}(),n=function t(e,o,l){null===e&&(e=Function.prototype);var n=Object.getOwnPropertyDescriptor(e,o);if(void 0===n){var i=Object.getPrototypeOf(e);return null===i?void 0:t(i,o,l)}if("value"in n)return n.value;var r=n.get;return void 0!==r?r.call(l):void 0},i=a(o(7)),r=a(o(0));function a(t){return t&&t.__esModule?t:{default:t}}var s=function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(e.__proto__||Object.getPrototypeOf(e)).apply(this,arguments))}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),l(e,[{key:"formatAt",value:function(t,o,l,i){if(e.compare(this.statics.blotName,l)<0&&r.default.query(l,r.default.Scope.BLOT)){var a=this.isolate(t,o);i&&a.wrap(l,i)}else n(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"formatAt",this).call(this,t,o,l,i)}},{key:"optimize",value:function(t){if(n(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"optimize",this).call(this,t),this.parent instanceof e&&e.compare(this.statics.blotName,this.parent.statics.blotName)>0){var o=this.parent.isolate(this.offset(),this.length());this.moveChildren(o),o.wrap(this)}}}],[{key:"compare",value:function(t,o){var l=e.order.indexOf(t),n=e.order.indexOf(o);return l>=0||n>=0?l-n:t===o?0:t<o?-1:1}}]),e}(r.default.Inline);s.allowedChildren=[s,r.default.Embed,i.default],s.order=["cursor","inline","underline","strike","italic","bold","script","link","code"],e.default=s},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var l,n=o(0),i=function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(e.__proto__||Object.getPrototypeOf(e)).apply(this,arguments))}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),e}(((l=n)&&l.__esModule?l:{default:l}).default.Text);e.default=i},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var l=function(){function t(t,e){for(var o=0;o<e.length;o++){var l=e[o];l.enumerable=l.enumerable||!1,l.configurable=!0,"value"in l&&(l.writable=!0),Object.defineProperty(t,l.key,l)}}return function(e,o,l){return o&&t(e.prototype,o),l&&t(e,l),e}}(),n=function t(e,o,l){null===e&&(e=Function.prototype);var n=Object.getOwnPropertyDescriptor(e,o);if(void 0===n){var i=Object.getPrototypeOf(e);return null===i?void 0:t(i,o,l)}if("value"in n)return n.value;var r=n.get;return void 0!==r?r.call(l):void 0},i=r(o(54));function r(t){return t&&t.__esModule?t:{default:t}}var a=(0,r(o(10)).default)("quill:events");["selectionchange","mousedown","mouseup","click"].forEach(function(t){document.addEventListener(t,function(){for(var t=arguments.length,e=Array(t),o=0;o<t;o++)e[o]=arguments[o];[].slice.call(document.querySelectorAll(".ql-container")).forEach(function(t){var o;t.__quill&&t.__quill.emitter&&(o=t.__quill.emitter).handleDOM.apply(o,e)})})});var s=function(t){function e(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);var t=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(e.__proto__||Object.getPrototypeOf(e)).call(this));return t.listeners={},t.on("error",a.error),t}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),l(e,[{key:"emit",value:function(){a.log.apply(a,arguments),n(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"emit",this).apply(this,arguments)}},{key:"handleDOM",value:function(t){for(var e=arguments.length,o=Array(e>1?e-1:0),l=1;l<e;l++)o[l-1]=arguments[l];(this.listeners[t.type]||[]).forEach(function(e){var l=e.node,n=e.handler;(t.target===l||l.contains(t.target))&&n.apply(void 0,[t].concat(o))})}},{key:"listenDOM",value:function(t,e,o){this.listeners[t]||(this.listeners[t]=[]),this.listeners[t].push({node:e,handler:o})}}]),e}(i.default);s.events={EDITOR_CHANGE:"editor-change",SCROLL_BEFORE_UPDATE:"scroll-before-update",SCROLL_OPTIMIZE:"scroll-optimize",SCROLL_UPDATE:"scroll-update",SELECTION_CHANGE:"selection-change",TEXT_CHANGE:"text-change"},s.sources={API:"api",SILENT:"silent",USER:"user"},e.default=s},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var l=function t(e){var o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.quill=e,this.options=o};l.DEFAULTS={},e.default=l},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var l=["error","warn","log","info"],n="warn";function i(t){if(l.indexOf(t)<=l.indexOf(n)){for(var e,o=arguments.length,i=Array(o>1?o-1:0),r=1;r<o;r++)i[r-1]=arguments[r];(e=console)[t].apply(e,i)}}function r(t){return l.reduce(function(e,o){return e[o]=i.bind(console,o,t),e},{})}i.level=r.level=function(t){n=t},e.default=r},function(t,e,o){var l=Array.prototype.slice,n=o(52),i=o(53),r=t.exports=function(t,e,o){return o||(o={}),t===e||(t instanceof Date&&e instanceof Date?t.getTime()===e.getTime():!t||!e||"object"!=typeof t&&"object"!=typeof e?o.strict?t===e:t==e:function(t,e,o){var c,u;if(a(t)||a(e))return!1;if(t.prototype!==e.prototype)return!1;if(i(t))return!!i(e)&&(t=l.call(t),e=l.call(e),r(t,e,o));if(s(t)){if(!s(e))return!1;if(t.length!==e.length)return!1;for(c=0;c<t.length;c++)if(t[c]!==e[c])return!1;return!0}try{var d=n(t),f=n(e)}catch(t){return!1}if(d.length!=f.length)return!1;for(d.sort(),f.sort(),c=d.length-1;c>=0;c--)if(d[c]!=f[c])return!1;for(c=d.length-1;c>=0;c--)if(u=d[c],!r(t[u],e[u],o))return!1;return typeof t==typeof e}(t,e,o))};function a(t){return null==t}function s(t){return!(!t||"object"!=typeof t||"number"!=typeof t.length||"function"!=typeof t.copy||"function"!=typeof t.slice||t.length>0&&"number"!=typeof t[0])}},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var l=o(1),n=function(){function t(t,e,o){void 0===o&&(o={}),this.attrName=t,this.keyName=e;var n=l.Scope.TYPE&l.Scope.ATTRIBUTE;null!=o.scope?this.scope=o.scope&l.Scope.LEVEL|n:this.scope=l.Scope.ATTRIBUTE,null!=o.whitelist&&(this.whitelist=o.whitelist)}return t.keys=function(t){return[].map.call(t.attributes,function(t){return t.name})},t.prototype.add=function(t,e){return!!this.canAdd(t,e)&&(t.setAttribute(this.keyName,e),!0)},t.prototype.canAdd=function(t,e){return null!=l.query(t,l.Scope.BLOT&(this.scope|l.Scope.TYPE))&&(null==this.whitelist||("string"==typeof e?this.whitelist.indexOf(e.replace(/["']/g,""))>-1:this.whitelist.indexOf(e)>-1))},t.prototype.remove=function(t){t.removeAttribute(this.keyName)},t.prototype.value=function(t){var e=t.getAttribute(this.keyName);return this.canAdd(t,e)&&e?e:""},t}();e.default=n},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=e.Code=void 0;var l=function(t,e){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return function(t,e){var o=[],l=!0,n=!1,i=void 0;try{for(var r,a=t[Symbol.iterator]();!(l=(r=a.next()).done)&&(o.push(r.value),!e||o.length!==e);l=!0);}catch(t){n=!0,i=t}finally{try{!l&&a.return&&a.return()}finally{if(n)throw i}}return o}(t,e);throw new TypeError("Invalid attempt to destructure non-iterable instance")},n=function(){function t(t,e){for(var o=0;o<e.length;o++){var l=e[o];l.enumerable=l.enumerable||!1,l.configurable=!0,"value"in l&&(l.writable=!0),Object.defineProperty(t,l.key,l)}}return function(e,o,l){return o&&t(e.prototype,o),l&&t(e,l),e}}(),i=function t(e,o,l){null===e&&(e=Function.prototype);var n=Object.getOwnPropertyDescriptor(e,o);if(void 0===n){var i=Object.getPrototypeOf(e);return null===i?void 0:t(i,o,l)}if("value"in n)return n.value;var r=n.get;return void 0!==r?r.call(l):void 0},r=d(o(2)),a=d(o(0)),s=d(o(4)),c=d(o(6)),u=d(o(7));function d(t){return t&&t.__esModule?t:{default:t}}function f(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function p(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}function h(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var b=function(t){function e(){return f(this,e),p(this,(e.__proto__||Object.getPrototypeOf(e)).apply(this,arguments))}return h(e,t),e}(c.default);b.blotName="code",b.tagName="CODE";var q=function(t){function e(){return f(this,e),p(this,(e.__proto__||Object.getPrototypeOf(e)).apply(this,arguments))}return h(e,t),n(e,[{key:"delta",value:function(){var t=this,e=this.domNode.textContent;return e.endsWith("\n")&&(e=e.slice(0,-1)),e.split("\n").reduce(function(e,o){return e.insert(o).insert("\n",t.formats())},new r.default)}},{key:"format",value:function(t,o){if(t!==this.statics.blotName||!o){var n=this.descendant(u.default,this.length()-1),r=l(n,1)[0];null!=r&&r.deleteAt(r.length()-1,1),i(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"format",this).call(this,t,o)}}},{key:"formatAt",value:function(t,o,l,n){if(0!==o&&null!=a.default.query(l,a.default.Scope.BLOCK)&&(l!==this.statics.blotName||n!==this.statics.formats(this.domNode))){var i=this.newlineIndex(t);if(!(i<0||i>=t+o)){var r=this.newlineIndex(t,!0)+1,s=i-r+1,c=this.isolate(r,s),u=c.next;c.format(l,n),u instanceof e&&u.formatAt(0,t-r+o-s,l,n)}}}},{key:"insertAt",value:function(t,e,o){if(null==o){var n=this.descendant(u.default,t),i=l(n,2),r=i[0],a=i[1];r.insertAt(a,e)}}},{key:"length",value:function(){var t=this.domNode.textContent.length;return this.domNode.textContent.endsWith("\n")?t:t+1}},{key:"newlineIndex",value:function(t){if(arguments.length>1&&void 0!==arguments[1]&&arguments[1])return this.domNode.textContent.slice(0,t).lastIndexOf("\n");var e=this.domNode.textContent.slice(t).indexOf("\n");return e>-1?t+e:-1}},{key:"optimize",value:function(t){this.domNode.textContent.endsWith("\n")||this.appendChild(a.default.create("text","\n")),i(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"optimize",this).call(this,t);var o=this.next;null!=o&&o.prev===this&&o.statics.blotName===this.statics.blotName&&this.statics.formats(this.domNode)===o.statics.formats(o.domNode)&&(o.optimize(t),o.moveChildren(this),o.remove())}},{key:"replace",value:function(t){i(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"replace",this).call(this,t),[].slice.call(this.domNode.querySelectorAll("*")).forEach(function(t){var e=a.default.find(t);null==e?t.parentNode.removeChild(t):e instanceof a.default.Embed?e.remove():e.unwrap()})}}],[{key:"create",value:function(t){var o=i(e.__proto__||Object.getPrototypeOf(e),"create",this).call(this,t);return o.setAttribute("spellcheck",!1),o}},{key:"formats",value:function(){return!0}}]),e}(s.default);q.blotName="code-block",q.tagName="PRE",q.TAB="  ",e.Code=b,e.default=q},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},n=function(t,e){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return function(t,e){var o=[],l=!0,n=!1,i=void 0;try{for(var r,a=t[Symbol.iterator]();!(l=(r=a.next()).done)&&(o.push(r.value),!e||o.length!==e);l=!0);}catch(t){n=!0,i=t}finally{try{!l&&a.return&&a.return()}finally{if(n)throw i}}return o}(t,e);throw new TypeError("Invalid attempt to destructure non-iterable instance")},i=function(){function t(t,e){for(var o=0;o<e.length;o++){var l=e[o];l.enumerable=l.enumerable||!1,l.configurable=!0,"value"in l&&(l.writable=!0),Object.defineProperty(t,l.key,l)}}return function(e,o,l){return o&&t(e.prototype,o),l&&t(e,l),e}}(),r=y(o(2)),a=y(o(20)),s=y(o(0)),c=y(o(13)),u=y(o(24)),d=o(4),f=y(d),p=y(o(16)),h=y(o(21)),b=y(o(11)),q=y(o(3));function y(t){return t&&t.__esModule?t:{default:t}}var v=/^[ -~]*$/,m=function(){function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.scroll=e,this.delta=this.getDelta()}return i(t,[{key:"applyDelta",value:function(t){var e=this,o=!1;this.scroll.update();var i=this.scroll.length();return this.scroll.batchStart(),(t=function(t){return t.reduce(function(t,e){if(1===e.insert){var o=(0,h.default)(e.attributes);return delete o.image,t.insert({image:e.attributes.image},o)}if(null==e.attributes||!0!==e.attributes.list&&!0!==e.attributes.bullet||((e=(0,h.default)(e)).attributes.list?e.attributes.list="ordered":(e.attributes.list="bullet",delete e.attributes.bullet)),"string"==typeof e.insert){var l=e.insert.replace(/\r\n/g,"\n").replace(/\r/g,"\n");return t.insert(l,e.attributes)}return t.push(e)},new r.default)}(t)).reduce(function(t,r){var c=r.retain||r.delete||r.insert.length||1,u=r.attributes||{};if(null!=r.insert){if("string"==typeof r.insert){var p=r.insert;p.endsWith("\n")&&o&&(o=!1,p=p.slice(0,-1)),t>=i&&!p.endsWith("\n")&&(o=!0),e.scroll.insertAt(t,p);var h=e.scroll.line(t),b=n(h,2),y=b[0],v=b[1],m=(0,q.default)({},(0,d.bubbleFormats)(y));if(y instanceof f.default){var g=y.descendant(s.default.Leaf,v),k=n(g,1)[0];m=(0,q.default)(m,(0,d.bubbleFormats)(k))}u=a.default.attributes.diff(m,u)||{}}else if("object"===l(r.insert)){var _=Object.keys(r.insert)[0];if(null==_)return t;e.scroll.insertAt(t,_,r.insert[_])}i+=c}return Object.keys(u).forEach(function(o){e.scroll.formatAt(t,c,o,u[o])}),t+c},0),t.reduce(function(t,o){return"number"==typeof o.delete?(e.scroll.deleteAt(t,o.delete),t):t+(o.retain||o.insert.length||1)},0),this.scroll.batchEnd(),this.update(t)}},{key:"deleteText",value:function(t,e){return this.scroll.deleteAt(t,e),this.update((new r.default).retain(t).delete(e))}},{key:"formatLine",value:function(t,e){var o=this,l=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return this.scroll.update(),Object.keys(l).forEach(function(n){if(null==o.scroll.whitelist||o.scroll.whitelist[n]){var i=o.scroll.lines(t,Math.max(e,1)),r=e;i.forEach(function(e){var i=e.length();if(e instanceof c.default){var a=t-e.offset(o.scroll),s=e.newlineIndex(a+r)-a+1;e.formatAt(a,s,n,l[n])}else e.format(n,l[n]);r-=i})}}),this.scroll.optimize(),this.update((new r.default).retain(t).retain(e,(0,h.default)(l)))}},{key:"formatText",value:function(t,e){var o=this,l=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return Object.keys(l).forEach(function(n){o.scroll.formatAt(t,e,n,l[n])}),this.update((new r.default).retain(t).retain(e,(0,h.default)(l)))}},{key:"getContents",value:function(t,e){return this.delta.slice(t,t+e)}},{key:"getDelta",value:function(){return this.scroll.lines().reduce(function(t,e){return t.concat(e.delta())},new r.default)}},{key:"getFormat",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,o=[],l=[];0===e?this.scroll.path(t).forEach(function(t){var e=n(t,1)[0];e instanceof f.default?o.push(e):e instanceof s.default.Leaf&&l.push(e)}):(o=this.scroll.lines(t,e),l=this.scroll.descendants(s.default.Leaf,t,e));var i=[o,l].map(function(t){if(0===t.length)return{};for(var e=(0,d.bubbleFormats)(t.shift());Object.keys(e).length>0;){var o=t.shift();if(null==o)return e;e=g((0,d.bubbleFormats)(o),e)}return e});return q.default.apply(q.default,i)}},{key:"getText",value:function(t,e){return this.getContents(t,e).filter(function(t){return"string"==typeof t.insert}).map(function(t){return t.insert}).join("")}},{key:"insertEmbed",value:function(t,e,o){return this.scroll.insertAt(t,e,o),this.update((new r.default).retain(t).insert(function(t,e,o){return e in t?Object.defineProperty(t,e,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[e]=o,t}({},e,o)))}},{key:"insertText",value:function(t,e){var o=this,l=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return e=e.replace(/\r\n/g,"\n").replace(/\r/g,"\n"),this.scroll.insertAt(t,e),Object.keys(l).forEach(function(n){o.scroll.formatAt(t,e.length,n,l[n])}),this.update((new r.default).retain(t).insert(e,(0,h.default)(l)))}},{key:"isBlank",value:function(){if(0==this.scroll.children.length)return!0;if(this.scroll.children.length>1)return!1;var t=this.scroll.children.head;return t.statics.blotName===f.default.blotName&&!(t.children.length>1)&&t.children.head instanceof p.default}},{key:"removeFormat",value:function(t,e){var o=this.getText(t,e),l=this.scroll.line(t+e),i=n(l,2),a=i[0],s=i[1],u=0,d=new r.default;null!=a&&(u=a instanceof c.default?a.newlineIndex(s)-s+1:a.length()-s,d=a.delta().slice(s,s+u-1).insert("\n"));var f=this.getContents(t,e+u).diff((new r.default).insert(o).concat(d)),p=(new r.default).retain(t).concat(f);return this.applyDelta(p)}},{key:"update",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0,l=this.delta;if(1===e.length&&"characterData"===e[0].type&&e[0].target.data.match(v)&&s.default.find(e[0].target)){var n=s.default.find(e[0].target),i=(0,d.bubbleFormats)(n),a=n.offset(this.scroll),c=e[0].oldValue.replace(u.default.CONTENTS,""),f=(new r.default).insert(c),p=(new r.default).insert(n.value());t=(new r.default).retain(a).concat(f.diff(p,o)).reduce(function(t,e){return e.insert?t.insert(e.insert,i):t.push(e)},new r.default),this.delta=l.compose(t)}else this.delta=this.getDelta(),t&&(0,b.default)(l.compose(t),this.delta)||(t=l.diff(this.delta,o));return t}}]),t}();function g(t,e){return Object.keys(e).reduce(function(o,l){return null==t[l]||(e[l]===t[l]?o[l]=e[l]:Array.isArray(e[l])?e[l].indexOf(t[l])<0&&(o[l]=e[l].concat([t[l]])):o[l]=[e[l],t[l]]),o},{})}e.default=m},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=e.Range=void 0;var l=function(t,e){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return function(t,e){var o=[],l=!0,n=!1,i=void 0;try{for(var r,a=t[Symbol.iterator]();!(l=(r=a.next()).done)&&(o.push(r.value),!e||o.length!==e);l=!0);}catch(t){n=!0,i=t}finally{try{!l&&a.return&&a.return()}finally{if(n)throw i}}return o}(t,e);throw new TypeError("Invalid attempt to destructure non-iterable instance")},n=function(){function t(t,e){for(var o=0;o<e.length;o++){var l=e[o];l.enumerable=l.enumerable||!1,l.configurable=!0,"value"in l&&(l.writable=!0),Object.defineProperty(t,l.key,l)}}return function(e,o,l){return o&&t(e.prototype,o),l&&t(e,l),e}}(),i=c(o(0)),r=c(o(21)),a=c(o(11)),s=c(o(8));function c(t){return t&&t.__esModule?t:{default:t}}function u(t){if(Array.isArray(t)){for(var e=0,o=Array(t.length);e<t.length;e++)o[e]=t[e];return o}return Array.from(t)}function d(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}var f=(0,c(o(10)).default)("quill:selection"),p=function t(e){var o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;d(this,t),this.index=e,this.length=o},h=function(){function t(e,o){var l=this;d(this,t),this.emitter=o,this.scroll=e,this.composing=!1,this.mouseDown=!1,this.root=this.scroll.domNode,this.cursor=i.default.create("cursor",this),this.lastRange=this.savedRange=new p(0,0),this.handleComposition(),this.handleDragging(),this.emitter.listenDOM("selectionchange",document,function(){l.mouseDown||setTimeout(l.update.bind(l,s.default.sources.USER),1)}),this.emitter.on(s.default.events.EDITOR_CHANGE,function(t,e){t===s.default.events.TEXT_CHANGE&&e.length()>0&&l.update(s.default.sources.SILENT)}),this.emitter.on(s.default.events.SCROLL_BEFORE_UPDATE,function(){if(l.hasFocus()){var t=l.getNativeRange();null!=t&&t.start.node!==l.cursor.textNode&&l.emitter.once(s.default.events.SCROLL_UPDATE,function(){try{l.setNativeRange(t.start.node,t.start.offset,t.end.node,t.end.offset)}catch(t){}})}}),this.emitter.on(s.default.events.SCROLL_OPTIMIZE,function(t,e){if(e.range){var o=e.range,n=o.startNode,i=o.startOffset,r=o.endNode,a=o.endOffset;l.setNativeRange(n,i,r,a)}}),this.update(s.default.sources.SILENT)}return n(t,[{key:"handleComposition",value:function(){var t=this;this.root.addEventListener("compositionstart",function(){t.composing=!0}),this.root.addEventListener("compositionend",function(){if(t.composing=!1,t.cursor.parent){var e=t.cursor.restore();if(!e)return;setTimeout(function(){t.setNativeRange(e.startNode,e.startOffset,e.endNode,e.endOffset)},1)}})}},{key:"handleDragging",value:function(){var t=this;this.emitter.listenDOM("mousedown",document.body,function(){t.mouseDown=!0}),this.emitter.listenDOM("mouseup",document.body,function(){t.mouseDown=!1,t.update(s.default.sources.USER)})}},{key:"focus",value:function(){this.hasFocus()||(this.root.focus(),this.setRange(this.savedRange))}},{key:"format",value:function(t,e){if(null==this.scroll.whitelist||this.scroll.whitelist[t]){this.scroll.update();var o=this.getNativeRange();if(null!=o&&o.native.collapsed&&!i.default.query(t,i.default.Scope.BLOCK)){if(o.start.node!==this.cursor.textNode){var l=i.default.find(o.start.node,!1);if(null==l)return;if(l instanceof i.default.Leaf){var n=l.split(o.start.offset);l.parent.insertBefore(this.cursor,n)}else l.insertBefore(this.cursor,o.start.node);this.cursor.attach()}this.cursor.format(t,e),this.scroll.optimize(),this.setNativeRange(this.cursor.textNode,this.cursor.textNode.data.length),this.update()}}}},{key:"getBounds",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,o=this.scroll.length();t=Math.min(t,o-1),e=Math.min(t+e,o-1)-t;var n=void 0,i=this.scroll.leaf(t),r=l(i,2),a=r[0],s=r[1];if(null==a)return null;var c=a.position(s,!0),u=l(c,2);n=u[0],s=u[1];var d=document.createRange();if(e>0){d.setStart(n,s);var f=this.scroll.leaf(t+e),p=l(f,2);if(a=p[0],s=p[1],null==a)return null;var h=a.position(s,!0),b=l(h,2);return n=b[0],s=b[1],d.setEnd(n,s),d.getBoundingClientRect()}var q="left",y=void 0;return n instanceof Text?(s<n.data.length?(d.setStart(n,s),d.setEnd(n,s+1)):(d.setStart(n,s-1),d.setEnd(n,s),q="right"),y=d.getBoundingClientRect()):(y=a.domNode.getBoundingClientRect(),s>0&&(q="right")),{bottom:y.top+y.height,height:y.height,left:y[q],right:y[q],top:y.top,width:0}}},{key:"getNativeRange",value:function(){var t=document.getSelection();if(null==t||t.rangeCount<=0)return null;var e=t.getRangeAt(0);if(null==e)return null;var o=this.normalizeNative(e);return f.info("getNativeRange",o),o}},{key:"getRange",value:function(){var t=this.getNativeRange();return null==t?[null,null]:[this.normalizedToRange(t),t]}},{key:"hasFocus",value:function(){return document.activeElement===this.root}},{key:"normalizedToRange",value:function(t){var e=this,o=[[t.start.node,t.start.offset]];t.native.collapsed||o.push([t.end.node,t.end.offset]);var n=o.map(function(t){var o=l(t,2),n=o[0],r=o[1],a=i.default.find(n,!0),s=a.offset(e.scroll);return 0===r?s:a instanceof i.default.Container?s+a.length():s+a.index(n,r)}),r=Math.min(Math.max.apply(Math,u(n)),this.scroll.length()-1),a=Math.min.apply(Math,[r].concat(u(n)));return new p(a,r-a)}},{key:"normalizeNative",value:function(t){if(!b(this.root,t.startContainer)||!t.collapsed&&!b(this.root,t.endContainer))return null;var e={start:{node:t.startContainer,offset:t.startOffset},end:{node:t.endContainer,offset:t.endOffset},native:t};return[e.start,e.end].forEach(function(t){for(var e=t.node,o=t.offset;!(e instanceof Text)&&e.childNodes.length>0;)if(e.childNodes.length>o)e=e.childNodes[o],o=0;else{if(e.childNodes.length!==o)break;o=(e=e.lastChild)instanceof Text?e.data.length:e.childNodes.length+1}t.node=e,t.offset=o}),e}},{key:"rangeToNative",value:function(t){var e=this,o=t.collapsed?[t.index]:[t.index,t.index+t.length],n=[],i=this.scroll.length();return o.forEach(function(t,o){t=Math.min(i-1,t);var r,a=e.scroll.leaf(t),s=l(a,2),c=s[0],u=s[1],d=c.position(u,0!==o),f=l(d,2);r=f[0],u=f[1],n.push(r,u)}),n.length<2&&(n=n.concat(n)),n}},{key:"scrollIntoView",value:function(t){var e=this.lastRange;if(null!=e){var o=this.getBounds(e.index,e.length);if(null!=o){var n=this.scroll.length()-1,i=this.scroll.line(Math.min(e.index,n)),r=l(i,1)[0],a=r;if(e.length>0){var s=this.scroll.line(Math.min(e.index+e.length,n));a=l(s,1)[0]}if(null!=r&&null!=a){var c=t.getBoundingClientRect();o.top<c.top?t.scrollTop-=c.top-o.top:o.bottom>c.bottom&&(t.scrollTop+=o.bottom-c.bottom)}}}}},{key:"setNativeRange",value:function(t,e){var o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:t,l=arguments.length>3&&void 0!==arguments[3]?arguments[3]:e,n=arguments.length>4&&void 0!==arguments[4]&&arguments[4];if(f.info("setNativeRange",t,e,o,l),null==t||null!=this.root.parentNode&&null!=t.parentNode&&null!=o.parentNode){var i=document.getSelection();if(null!=i)if(null!=t){this.hasFocus()||this.root.focus();var r=(this.getNativeRange()||{}).native;if(null==r||n||t!==r.startContainer||e!==r.startOffset||o!==r.endContainer||l!==r.endOffset){"BR"==t.tagName&&(e=[].indexOf.call(t.parentNode.childNodes,t),t=t.parentNode),"BR"==o.tagName&&(l=[].indexOf.call(o.parentNode.childNodes,o),o=o.parentNode);var a=document.createRange();a.setStart(t,e),a.setEnd(o,l),i.removeAllRanges(),i.addRange(a)}}else i.removeAllRanges(),this.root.blur(),document.body.focus()}}},{key:"setRange",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:s.default.sources.API;if("string"==typeof e&&(o=e,e=!1),f.info("setRange",t),null!=t){var l=this.rangeToNative(t);this.setNativeRange.apply(this,u(l).concat([e]))}else this.setNativeRange(null);this.update(o)}},{key:"update",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:s.default.sources.USER,e=this.lastRange,o=this.getRange(),n=l(o,2),i=n[0],c=n[1];if(this.lastRange=i,null!=this.lastRange&&(this.savedRange=this.lastRange),!(0,a.default)(e,this.lastRange)){var u;!this.composing&&null!=c&&c.native.collapsed&&c.start.node!==this.cursor.textNode&&this.cursor.restore();var d,f=[s.default.events.SELECTION_CHANGE,(0,r.default)(this.lastRange),(0,r.default)(e),t];(u=this.emitter).emit.apply(u,[s.default.events.EDITOR_CHANGE].concat(f)),t!==s.default.sources.SILENT&&(d=this.emitter).emit.apply(d,f)}}}]),t}();function b(t,e){try{e.parentNode}catch(t){return!1}return e instanceof Text&&(e=e.parentNode),t.contains(e)}e.Range=p,e.default=h},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var l,n=function(){function t(t,e){for(var o=0;o<e.length;o++){var l=e[o];l.enumerable=l.enumerable||!1,l.configurable=!0,"value"in l&&(l.writable=!0),Object.defineProperty(t,l.key,l)}}return function(e,o,l){return o&&t(e.prototype,o),l&&t(e,l),e}}(),i=function t(e,o,l){null===e&&(e=Function.prototype);var n=Object.getOwnPropertyDescriptor(e,o);if(void 0===n){var i=Object.getPrototypeOf(e);return null===i?void 0:t(i,o,l)}if("value"in n)return n.value;var r=n.get;return void 0!==r?r.call(l):void 0},r=o(0),a=function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(e.__proto__||Object.getPrototypeOf(e)).apply(this,arguments))}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),n(e,[{key:"insertInto",value:function(t,o){0===t.children.length?i(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"insertInto",this).call(this,t,o):this.remove()}},{key:"length",value:function(){return 0}},{key:"value",value:function(){return""}}],[{key:"value",value:function(){}}]),e}(((l=r)&&l.__esModule?l:{default:l}).default.Embed);a.blotName="break",a.tagName="BR",e.default=a},function(t,e,o){"use strict";var l,n=this&&this.__extends||(l=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)e.hasOwnProperty(o)&&(t[o]=e[o])},function(t,e){function o(){this.constructor=t}l(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)});Object.defineProperty(e,"__esModule",{value:!0});var i=o(44),r=o(30),a=o(1),s=function(t){function e(e){var o=t.call(this,e)||this;return o.build(),o}return n(e,t),e.prototype.appendChild=function(t){this.insertBefore(t)},e.prototype.attach=function(){t.prototype.attach.call(this),this.children.forEach(function(t){t.attach()})},e.prototype.build=function(){var t=this;this.children=new i.default,[].slice.call(this.domNode.childNodes).reverse().forEach(function(e){try{var o=c(e);t.insertBefore(o,t.children.head||void 0)}catch(t){if(t instanceof a.ParchmentError)return;throw t}})},e.prototype.deleteAt=function(t,e){if(0===t&&e===this.length())return this.remove();this.children.forEachAt(t,e,function(t,e,o){t.deleteAt(e,o)})},e.prototype.descendant=function(t,o){var l=this.children.find(o),n=l[0],i=l[1];return null==t.blotName&&t(n)||null!=t.blotName&&n instanceof t?[n,i]:n instanceof e?n.descendant(t,i):[null,-1]},e.prototype.descendants=function(t,o,l){void 0===o&&(o=0),void 0===l&&(l=Number.MAX_VALUE);var n=[],i=l;return this.children.forEachAt(o,l,function(o,l,r){(null==t.blotName&&t(o)||null!=t.blotName&&o instanceof t)&&n.push(o),o instanceof e&&(n=n.concat(o.descendants(t,l,i))),i-=r}),n},e.prototype.detach=function(){this.children.forEach(function(t){t.detach()}),t.prototype.detach.call(this)},e.prototype.formatAt=function(t,e,o,l){this.children.forEachAt(t,e,function(t,e,n){t.formatAt(e,n,o,l)})},e.prototype.insertAt=function(t,e,o){var l=this.children.find(t),n=l[0],i=l[1];if(n)n.insertAt(i,e,o);else{var r=null==o?a.create("text",e):a.create(e,o);this.appendChild(r)}},e.prototype.insertBefore=function(t,e){if(null!=this.statics.allowedChildren&&!this.statics.allowedChildren.some(function(e){return t instanceof e}))throw new a.ParchmentError("Cannot insert "+t.statics.blotName+" into "+this.statics.blotName);t.insertInto(this,e)},e.prototype.length=function(){return this.children.reduce(function(t,e){return t+e.length()},0)},e.prototype.moveChildren=function(t,e){this.children.forEach(function(o){t.insertBefore(o,e)})},e.prototype.optimize=function(e){if(t.prototype.optimize.call(this,e),0===this.children.length)if(null!=this.statics.defaultChild){var o=a.create(this.statics.defaultChild);this.appendChild(o),o.optimize(e)}else this.remove()},e.prototype.path=function(t,o){void 0===o&&(o=!1);var l=this.children.find(t,o),n=l[0],i=l[1],r=[[this,t]];return n instanceof e?r.concat(n.path(i,o)):(null!=n&&r.push([n,i]),r)},e.prototype.removeChild=function(t){this.children.remove(t)},e.prototype.replace=function(o){o instanceof e&&o.moveChildren(this),t.prototype.replace.call(this,o)},e.prototype.split=function(t,e){if(void 0===e&&(e=!1),!e){if(0===t)return this;if(t===this.length())return this.next}var o=this.clone();return this.parent.insertBefore(o,this.next),this.children.forEachAt(t,this.length(),function(t,l,n){t=t.split(l,e),o.appendChild(t)}),o},e.prototype.unwrap=function(){this.moveChildren(this.parent,this.next),this.remove()},e.prototype.update=function(t,e){var o=this,l=[],n=[];t.forEach(function(t){t.target===o.domNode&&"childList"===t.type&&(l.push.apply(l,t.addedNodes),n.push.apply(n,t.removedNodes))}),n.forEach(function(t){if(!(null!=t.parentNode&&"IFRAME"!==t.tagName&&document.body.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_CONTAINED_BY)){var e=a.find(t);null!=e&&(null!=e.domNode.parentNode&&e.domNode.parentNode!==o.domNode||e.detach())}}),l.filter(function(t){return t.parentNode==o.domNode}).sort(function(t,e){return t===e?0:t.compareDocumentPosition(e)&Node.DOCUMENT_POSITION_FOLLOWING?1:-1}).forEach(function(t){var e=null;null!=t.nextSibling&&(e=a.find(t.nextSibling));var l=c(t);l.next==e&&null!=l.next||(null!=l.parent&&l.parent.removeChild(o),o.insertBefore(l,e||void 0))})},e}(r.default);function c(t){var e=a.find(t);if(null==e)try{e=a.create(t)}catch(o){e=a.create(a.Scope.INLINE),[].slice.call(t.childNodes).forEach(function(t){e.domNode.appendChild(t)}),t.parentNode&&t.parentNode.replaceChild(e.domNode,t),e.attach()}return e}e.default=s},function(t,e,o){"use strict";var l,n=this&&this.__extends||(l=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)e.hasOwnProperty(o)&&(t[o]=e[o])},function(t,e){function o(){this.constructor=t}l(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)});Object.defineProperty(e,"__esModule",{value:!0});var i=o(12),r=o(31),a=o(17),s=o(1),c=function(t){function e(e){var o=t.call(this,e)||this;return o.attributes=new r.default(o.domNode),o}return n(e,t),e.formats=function(t){return"string"==typeof this.tagName||(Array.isArray(this.tagName)?t.tagName.toLowerCase():void 0)},e.prototype.format=function(t,e){var o=s.query(t);o instanceof i.default?this.attributes.attribute(o,e):e&&(null==o||t===this.statics.blotName&&this.formats()[t]===e||this.replaceWith(t,e))},e.prototype.formats=function(){var t=this.attributes.values(),e=this.statics.formats(this.domNode);return null!=e&&(t[this.statics.blotName]=e),t},e.prototype.replaceWith=function(e,o){var l=t.prototype.replaceWith.call(this,e,o);return this.attributes.copy(l),l},e.prototype.update=function(e,o){var l=this;t.prototype.update.call(this,e,o),e.some(function(t){return t.target===l.domNode&&"attributes"===t.type})&&this.attributes.build()},e.prototype.wrap=function(o,l){var n=t.prototype.wrap.call(this,o,l);return n instanceof e&&n.statics.scope===this.statics.scope&&this.attributes.move(n),n},e}(a.default);e.default=c},function(t,e,o){"use strict";var l,n=this&&this.__extends||(l=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)e.hasOwnProperty(o)&&(t[o]=e[o])},function(t,e){function o(){this.constructor=t}l(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)});Object.defineProperty(e,"__esModule",{value:!0});var i=o(30),r=o(1),a=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.value=function(t){return!0},e.prototype.index=function(t,e){return this.domNode===t||this.domNode.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_CONTAINED_BY?Math.min(e,1):-1},e.prototype.position=function(t,e){var o=[].indexOf.call(this.parent.domNode.childNodes,this.domNode);return t>0&&(o+=1),[this.parent.domNode,o]},e.prototype.value=function(){var t;return(t={})[this.statics.blotName]=this.statics.value(this.domNode)||!0,t},e.scope=r.Scope.INLINE_BLOT,e}(i.default);e.default=a},function(t,e,o){var l=o(11),n=o(3),i={attributes:{compose:function(t,e,o){"object"!=typeof t&&(t={}),"object"!=typeof e&&(e={});var l=n(!0,{},e);for(var i in o||(l=Object.keys(l).reduce(function(t,e){return null!=l[e]&&(t[e]=l[e]),t},{})),t)void 0!==t[i]&&void 0===e[i]&&(l[i]=t[i]);return Object.keys(l).length>0?l:void 0},diff:function(t,e){"object"!=typeof t&&(t={}),"object"!=typeof e&&(e={});var o=Object.keys(t).concat(Object.keys(e)).reduce(function(o,n){return l(t[n],e[n])||(o[n]=void 0===e[n]?null:e[n]),o},{});return Object.keys(o).length>0?o:void 0},transform:function(t,e,o){if("object"!=typeof t)return e;if("object"==typeof e){if(!o)return e;var l=Object.keys(e).reduce(function(o,l){return void 0===t[l]&&(o[l]=e[l]),o},{});return Object.keys(l).length>0?l:void 0}}},iterator:function(t){return new r(t)},length:function(t){return"number"==typeof t.delete?t.delete:"number"==typeof t.retain?t.retain:"string"==typeof t.insert?t.insert.length:1}};function r(t){this.ops=t,this.index=0,this.offset=0}r.prototype.hasNext=function(){return this.peekLength()<1/0},r.prototype.next=function(t){t||(t=1/0);var e=this.ops[this.index];if(e){var o=this.offset,l=i.length(e);if(t>=l-o?(t=l-o,this.index+=1,this.offset=0):this.offset+=t,"number"==typeof e.delete)return{delete:t};var n={};return e.attributes&&(n.attributes=e.attributes),"number"==typeof e.retain?n.retain=t:"string"==typeof e.insert?n.insert=e.insert.substr(o,t):n.insert=e.insert,n}return{retain:1/0}},r.prototype.peek=function(){return this.ops[this.index]},r.prototype.peekLength=function(){return this.ops[this.index]?i.length(this.ops[this.index])-this.offset:1/0},r.prototype.peekType=function(){return this.ops[this.index]?"number"==typeof this.ops[this.index].delete?"delete":"number"==typeof this.ops[this.index].retain?"retain":"insert":"retain"},r.prototype.rest=function(){if(this.hasNext()){if(0===this.offset)return this.ops.slice(this.index);var t=this.offset,e=this.index,o=this.next(),l=this.ops.slice(this.index);return this.offset=t,this.index=e,[o].concat(l)}return[]},t.exports=i},function(t,e){var o=function(){"use strict";function t(t,e){return null!=e&&t instanceof e}var e,o,l;try{e=Map}catch(t){e=function(){}}try{o=Set}catch(t){o=function(){}}try{l=Promise}catch(t){l=function(){}}function i(r,s,c,u,d){"object"==typeof s&&(c=s.depth,u=s.prototype,d=s.includeNonEnumerable,s=s.circular);var f=[],p=[],h=void 0!==n;return void 0===s&&(s=!0),void 0===c&&(c=1/0),function r(c,b){if(null===c)return null;if(0===b)return c;var q,y;if("object"!=typeof c)return c;if(t(c,e))q=new e;else if(t(c,o))q=new o;else if(t(c,l))q=new l(function(t,e){c.then(function(e){t(r(e,b-1))},function(t){e(r(t,b-1))})});else if(i.__isArray(c))q=[];else if(i.__isRegExp(c))q=new RegExp(c.source,a(c)),c.lastIndex&&(q.lastIndex=c.lastIndex);else if(i.__isDate(c))q=new Date(c.getTime());else{if(h&&n.isBuffer(c))return q=n.allocUnsafe?n.allocUnsafe(c.length):new n(c.length),c.copy(q),q;t(c,Error)?q=Object.create(c):void 0===u?(y=Object.getPrototypeOf(c),q=Object.create(y)):(q=Object.create(u),y=u)}if(s){var v=f.indexOf(c);if(-1!=v)return p[v];f.push(c),p.push(q)}for(var m in t(c,e)&&c.forEach(function(t,e){var o=r(e,b-1),l=r(t,b-1);q.set(o,l)}),t(c,o)&&c.forEach(function(t){var e=r(t,b-1);q.add(e)}),c){var g;y&&(g=Object.getOwnPropertyDescriptor(y,m)),g&&null==g.set||(q[m]=r(c[m],b-1))}if(Object.getOwnPropertySymbols){var k=Object.getOwnPropertySymbols(c);for(m=0;m<k.length;m++){var _=k[m];(!(x=Object.getOwnPropertyDescriptor(c,_))||x.enumerable||d)&&(q[_]=r(c[_],b-1),x.enumerable||Object.defineProperty(q,_,{enumerable:!1}))}}if(d){var w=Object.getOwnPropertyNames(c);for(m=0;m<w.length;m++){var x,O=w[m];(x=Object.getOwnPropertyDescriptor(c,O))&&x.enumerable||(q[O]=r(c[O],b-1),Object.defineProperty(q,O,{enumerable:!1}))}}return q}(r,c)}function r(t){return Object.prototype.toString.call(t)}function a(t){var e="";return t.global&&(e+="g"),t.ignoreCase&&(e+="i"),t.multiline&&(e+="m"),e}return i.clonePrototype=function(t){if(null===t)return null;var e=function(){};return e.prototype=t,new e},i.__objToStr=r,i.__isDate=function(t){return"object"==typeof t&&"[object Date]"===r(t)},i.__isArray=function(t){return"object"==typeof t&&"[object Array]"===r(t)},i.__isRegExp=function(t){return"object"==typeof t&&"[object RegExp]"===r(t)},i.__getRegExpFlags=a,i}();"object"==typeof t&&t.exports&&(t.exports=o)},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var l=function(t,e){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return function(t,e){var o=[],l=!0,n=!1,i=void 0;try{for(var r,a=t[Symbol.iterator]();!(l=(r=a.next()).done)&&(o.push(r.value),!e||o.length!==e);l=!0);}catch(t){n=!0,i=t}finally{try{!l&&a.return&&a.return()}finally{if(n)throw i}}return o}(t,e);throw new TypeError("Invalid attempt to destructure non-iterable instance")},n=function(){function t(t,e){for(var o=0;o<e.length;o++){var l=e[o];l.enumerable=l.enumerable||!1,l.configurable=!0,"value"in l&&(l.writable=!0),Object.defineProperty(t,l.key,l)}}return function(e,o,l){return o&&t(e.prototype,o),l&&t(e,l),e}}(),i=function t(e,o,l){null===e&&(e=Function.prototype);var n=Object.getOwnPropertyDescriptor(e,o);if(void 0===n){var i=Object.getPrototypeOf(e);return null===i?void 0:t(i,o,l)}if("value"in n)return n.value;var r=n.get;return void 0!==r?r.call(l):void 0},r=p(o(0)),a=p(o(8)),s=o(4),c=p(s),u=p(o(16)),d=p(o(13)),f=p(o(25));function p(t){return t&&t.__esModule?t:{default:t}}function h(t){return t instanceof c.default||t instanceof s.BlockEmbed}var b=function(t){function e(t,o){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);var l=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,t));return l.emitter=o.emitter,Array.isArray(o.whitelist)&&(l.whitelist=o.whitelist.reduce(function(t,e){return t[e]=!0,t},{})),l.domNode.addEventListener("DOMNodeInserted",function(){}),l.optimize(),l.enable(),l}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),n(e,[{key:"batchStart",value:function(){this.batch=!0}},{key:"batchEnd",value:function(){this.batch=!1,this.optimize()}},{key:"deleteAt",value:function(t,o){var n=this.line(t),r=l(n,2),a=r[0],c=r[1],f=this.line(t+o),p=l(f,1)[0];if(i(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"deleteAt",this).call(this,t,o),null!=p&&a!==p&&c>0){if(a instanceof s.BlockEmbed||p instanceof s.BlockEmbed)return void this.optimize();if(a instanceof d.default){var h=a.newlineIndex(a.length(),!0);if(h>-1&&(a=a.split(h+1))===p)return void this.optimize()}else if(p instanceof d.default){var b=p.newlineIndex(0);b>-1&&p.split(b+1)}var q=p.children.head instanceof u.default?null:p.children.head;a.moveChildren(p,q),a.remove()}this.optimize()}},{key:"enable",value:function(){var t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];this.domNode.setAttribute("contenteditable",t)}},{key:"formatAt",value:function(t,o,l,n){(null==this.whitelist||this.whitelist[l])&&(i(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"formatAt",this).call(this,t,o,l,n),this.optimize())}},{key:"insertAt",value:function(t,o,l){if(null==l||null==this.whitelist||this.whitelist[o]){if(t>=this.length())if(null==l||null==r.default.query(o,r.default.Scope.BLOCK)){var n=r.default.create(this.statics.defaultChild);this.appendChild(n),null==l&&o.endsWith("\n")&&(o=o.slice(0,-1)),n.insertAt(0,o,l)}else{var a=r.default.create(o,l);this.appendChild(a)}else i(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"insertAt",this).call(this,t,o,l);this.optimize()}}},{key:"insertBefore",value:function(t,o){if(t.statics.scope===r.default.Scope.INLINE_BLOT){var l=r.default.create(this.statics.defaultChild);l.appendChild(t),t=l}i(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"insertBefore",this).call(this,t,o)}},{key:"leaf",value:function(t){return this.path(t).pop()||[null,-1]}},{key:"line",value:function(t){return t===this.length()?this.line(t-1):this.descendant(h,t)}},{key:"lines",value:function(){return function t(e,o,l){var n=[],i=l;return e.children.forEachAt(o,l,function(e,o,l){h(e)?n.push(e):e instanceof r.default.Container&&(n=n.concat(t(e,o,i))),i-=l}),n}(this,arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,arguments.length>1&&void 0!==arguments[1]?arguments[1]:Number.MAX_VALUE)}},{key:"optimize",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};!0!==this.batch&&(i(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"optimize",this).call(this,t,o),t.length>0&&this.emitter.emit(a.default.events.SCROLL_OPTIMIZE,t,o))}},{key:"path",value:function(t){return i(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"path",this).call(this,t).slice(1)}},{key:"update",value:function(t){if(!0!==this.batch){var o=a.default.sources.USER;"string"==typeof t&&(o=t),Array.isArray(t)||(t=this.observer.takeRecords()),t.length>0&&this.emitter.emit(a.default.events.SCROLL_BEFORE_UPDATE,o,t),i(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"update",this).call(this,t.concat([])),t.length>0&&this.emitter.emit(a.default.events.SCROLL_UPDATE,o,t)}}}]),e}(r.default.Scroll);b.blotName="scroll",b.className="ql-editor",b.tagName="DIV",b.defaultChild="block",b.allowedChildren=[c.default,s.BlockEmbed,f.default],e.default=b},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.SHORTKEY=e.default=void 0;var l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},n=function(t,e){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return function(t,e){var o=[],l=!0,n=!1,i=void 0;try{for(var r,a=t[Symbol.iterator]();!(l=(r=a.next()).done)&&(o.push(r.value),!e||o.length!==e);l=!0);}catch(t){n=!0,i=t}finally{try{!l&&a.return&&a.return()}finally{if(n)throw i}}return o}(t,e);throw new TypeError("Invalid attempt to destructure non-iterable instance")},i=function(){function t(t,e){for(var o=0;o<e.length;o++){var l=e[o];l.enumerable=l.enumerable||!1,l.configurable=!0,"value"in l&&(l.writable=!0),Object.defineProperty(t,l.key,l)}}return function(e,o,l){return o&&t(e.prototype,o),l&&t(e,l),e}}(),r=b(o(21)),a=b(o(11)),s=b(o(3)),c=b(o(2)),u=b(o(20)),d=b(o(0)),f=b(o(5)),p=b(o(10)),h=b(o(9));function b(t){return t&&t.__esModule?t:{default:t}}function q(t,e,o){return e in t?Object.defineProperty(t,e,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[e]=o,t}var y=(0,p.default)("quill:keyboard"),v=/Mac/i.test(navigator.platform)?"metaKey":"ctrlKey",m=function(t){function e(t,o){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);var l=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,t,o));return l.bindings={},Object.keys(l.options.bindings).forEach(function(e){("list autofill"!==e||null==t.scroll.whitelist||t.scroll.whitelist.list)&&l.options.bindings[e]&&l.addBinding(l.options.bindings[e])}),l.addBinding({key:e.keys.ENTER,shiftKey:null},x),l.addBinding({key:e.keys.ENTER,metaKey:null,ctrlKey:null,altKey:null},function(){}),/Firefox/i.test(navigator.userAgent)?(l.addBinding({key:e.keys.BACKSPACE},{collapsed:!0},k),l.addBinding({key:e.keys.DELETE},{collapsed:!0},_)):(l.addBinding({key:e.keys.BACKSPACE},{collapsed:!0,prefix:/^.?$/},k),l.addBinding({key:e.keys.DELETE},{collapsed:!0,suffix:/^.?$/},_)),l.addBinding({key:e.keys.BACKSPACE},{collapsed:!1},w),l.addBinding({key:e.keys.DELETE},{collapsed:!1},w),l.addBinding({key:e.keys.BACKSPACE,altKey:null,ctrlKey:null,metaKey:null,shiftKey:null},{collapsed:!0,offset:0},k),l.listen(),l}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),i(e,null,[{key:"match",value:function(t,e){return e=N(e),!["altKey","ctrlKey","metaKey","shiftKey"].some(function(o){return!!e[o]!==t[o]&&null!==e[o]})&&e.key===(t.which||t.keyCode)}}]),i(e,[{key:"addBinding",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},l=N(t);if(null==l||null==l.key)return y.warn("Attempted to add invalid keyboard binding",l);"function"==typeof e&&(e={handler:e}),"function"==typeof o&&(o={handler:o}),l=(0,s.default)(l,e,o),this.bindings[l.key]=this.bindings[l.key]||[],this.bindings[l.key].push(l)}},{key:"listen",value:function(){var t=this;this.quill.root.addEventListener("keydown",function(o){if(!o.defaultPrevented){var i=o.which||o.keyCode,r=(t.bindings[i]||[]).filter(function(t){return e.match(o,t)});if(0!==r.length){var s=t.quill.getSelection();if(null!=s&&t.quill.hasFocus()){var c=t.quill.getLine(s.index),u=n(c,2),f=u[0],p=u[1],h=t.quill.getLeaf(s.index),b=n(h,2),q=b[0],y=b[1],v=0===s.length?[q,y]:t.quill.getLeaf(s.index+s.length),m=n(v,2),g=m[0],k=m[1],_=q instanceof d.default.Text?q.value().slice(0,y):"",w=g instanceof d.default.Text?g.value().slice(k):"",x={collapsed:0===s.length,empty:0===s.length&&f.length()<=1,format:t.quill.getFormat(s),offset:p,prefix:_,suffix:w};r.some(function(e){if(null!=e.collapsed&&e.collapsed!==x.collapsed)return!1;if(null!=e.empty&&e.empty!==x.empty)return!1;if(null!=e.offset&&e.offset!==x.offset)return!1;if(Array.isArray(e.format)){if(e.format.every(function(t){return null==x.format[t]}))return!1}else if("object"===l(e.format)&&!Object.keys(e.format).every(function(t){return!0===e.format[t]?null!=x.format[t]:!1===e.format[t]?null==x.format[t]:(0,a.default)(e.format[t],x.format[t])}))return!1;return!(null!=e.prefix&&!e.prefix.test(x.prefix)||null!=e.suffix&&!e.suffix.test(x.suffix)||!0===e.handler.call(t,s,x))})&&o.preventDefault()}}}})}}]),e}(h.default);function g(t,e){var o,l=t===m.keys.LEFT?"prefix":"suffix";return q(o={key:t,shiftKey:e,altKey:null},l,/^$/),q(o,"handler",function(o){var l=o.index;t===m.keys.RIGHT&&(l+=o.length+1);var i=this.quill.getLeaf(l);return!(n(i,1)[0]instanceof d.default.Embed&&(t===m.keys.LEFT?e?this.quill.setSelection(o.index-1,o.length+1,f.default.sources.USER):this.quill.setSelection(o.index-1,f.default.sources.USER):e?this.quill.setSelection(o.index,o.length+1,f.default.sources.USER):this.quill.setSelection(o.index+o.length+1,f.default.sources.USER),1))}),o}function k(t,e){if(!(0===t.index||this.quill.getLength()<=1)){var o=this.quill.getLine(t.index),l=n(o,1)[0],i={};if(0===e.offset){var r=this.quill.getLine(t.index-1),a=n(r,1)[0];if(null!=a&&a.length()>1){var s=l.formats(),c=this.quill.getFormat(t.index-1,1);i=u.default.attributes.diff(s,c)||{}}}var d=/[\uD800-\uDBFF][\uDC00-\uDFFF]$/.test(e.prefix)?2:1;this.quill.deleteText(t.index-d,d,f.default.sources.USER),Object.keys(i).length>0&&this.quill.formatLine(t.index-d,d,i,f.default.sources.USER),this.quill.focus()}}function _(t,e){var o=/^[\uD800-\uDBFF][\uDC00-\uDFFF]/.test(e.suffix)?2:1;if(!(t.index>=this.quill.getLength()-o)){var l={},i=0,r=this.quill.getLine(t.index),a=n(r,1)[0];if(e.offset>=a.length()-1){var s=this.quill.getLine(t.index+1),c=n(s,1)[0];if(c){var d=a.formats(),p=this.quill.getFormat(t.index,1);l=u.default.attributes.diff(d,p)||{},i=c.length()}}this.quill.deleteText(t.index,o,f.default.sources.USER),Object.keys(l).length>0&&this.quill.formatLine(t.index+i-1,o,l,f.default.sources.USER)}}function w(t){var e=this.quill.getLines(t),o={};if(e.length>1){var l=e[0].formats(),n=e[e.length-1].formats();o=u.default.attributes.diff(n,l)||{}}this.quill.deleteText(t,f.default.sources.USER),Object.keys(o).length>0&&this.quill.formatLine(t.index,1,o,f.default.sources.USER),this.quill.setSelection(t.index,f.default.sources.SILENT),this.quill.focus()}function x(t,e){var o=this;t.length>0&&this.quill.scroll.deleteAt(t.index,t.length);var l=Object.keys(e.format).reduce(function(t,o){return d.default.query(o,d.default.Scope.BLOCK)&&!Array.isArray(e.format[o])&&(t[o]=e.format[o]),t},{});this.quill.insertText(t.index,"\n",l,f.default.sources.USER),this.quill.setSelection(t.index+1,f.default.sources.SILENT),this.quill.focus(),Object.keys(e.format).forEach(function(t){null==l[t]&&(Array.isArray(e.format[t])||"link"!==t&&o.quill.format(t,e.format[t],f.default.sources.USER))})}function O(t){return{key:m.keys.TAB,shiftKey:!t,format:{"code-block":!0},handler:function(e){var o=d.default.query("code-block"),l=e.index,i=e.length,r=this.quill.scroll.descendant(o,l),a=n(r,2),s=a[0],c=a[1];if(null!=s){var u=this.quill.getIndex(s),p=s.newlineIndex(c,!0)+1,h=s.newlineIndex(u+c+i),b=s.domNode.textContent.slice(p,h).split("\n");c=0,b.forEach(function(e,n){t?(s.insertAt(p+c,o.TAB),c+=o.TAB.length,0===n?l+=o.TAB.length:i+=o.TAB.length):e.startsWith(o.TAB)&&(s.deleteAt(p+c,o.TAB.length),c-=o.TAB.length,0===n?l-=o.TAB.length:i-=o.TAB.length),c+=e.length+1}),this.quill.update(f.default.sources.USER),this.quill.setSelection(l,i,f.default.sources.SILENT)}}}}function E(t){return{key:t[0].toUpperCase(),shortKey:!0,handler:function(e,o){this.quill.format(t,!o.format[t],f.default.sources.USER)}}}function N(t){if("string"==typeof t||"number"==typeof t)return N({key:t});if("object"===(void 0===t?"undefined":l(t))&&(t=(0,r.default)(t,!1)),"string"==typeof t.key)if(null!=m.keys[t.key.toUpperCase()])t.key=m.keys[t.key.toUpperCase()];else{if(1!==t.key.length)return null;t.key=t.key.toUpperCase().charCodeAt(0)}return t.shortKey&&(t[v]=t.shortKey,delete t.shortKey),t}m.keys={BACKSPACE:8,TAB:9,ENTER:13,ESCAPE:27,LEFT:37,UP:38,RIGHT:39,DOWN:40,DELETE:46},m.DEFAULTS={bindings:{bold:E("bold"),italic:E("italic"),underline:E("underline"),indent:{key:m.keys.TAB,format:["blockquote","indent","list"],handler:function(t,e){if(e.collapsed&&0!==e.offset)return!0;this.quill.format("indent","+1",f.default.sources.USER)}},outdent:{key:m.keys.TAB,shiftKey:!0,format:["blockquote","indent","list"],handler:function(t,e){if(e.collapsed&&0!==e.offset)return!0;this.quill.format("indent","-1",f.default.sources.USER)}},"outdent backspace":{key:m.keys.BACKSPACE,collapsed:!0,shiftKey:null,metaKey:null,ctrlKey:null,altKey:null,format:["indent","list"],offset:0,handler:function(t,e){null!=e.format.indent?this.quill.format("indent","-1",f.default.sources.USER):null!=e.format.list&&this.quill.format("list",!1,f.default.sources.USER)}},"indent code-block":O(!0),"outdent code-block":O(!1),"remove tab":{key:m.keys.TAB,shiftKey:!0,collapsed:!0,prefix:/\t$/,handler:function(t){this.quill.deleteText(t.index-1,1,f.default.sources.USER)}},tab:{key:m.keys.TAB,handler:function(t){this.quill.history.cutoff();var e=(new c.default).retain(t.index).delete(t.length).insert("\t");this.quill.updateContents(e,f.default.sources.USER),this.quill.history.cutoff(),this.quill.setSelection(t.index+1,f.default.sources.SILENT)}},"list empty enter":{key:m.keys.ENTER,collapsed:!0,format:["list"],empty:!0,handler:function(t,e){this.quill.format("list",!1,f.default.sources.USER),e.format.indent&&this.quill.format("indent",!1,f.default.sources.USER)}},"checklist enter":{key:m.keys.ENTER,collapsed:!0,format:{list:"checked"},handler:function(t){var e=this.quill.getLine(t.index),o=n(e,2),l=o[0],i=o[1],r=(0,s.default)({},l.formats(),{list:"checked"}),a=(new c.default).retain(t.index).insert("\n",r).retain(l.length()-i-1).retain(1,{list:"unchecked"});this.quill.updateContents(a,f.default.sources.USER),this.quill.setSelection(t.index+1,f.default.sources.SILENT),this.quill.scrollIntoView()}},"header enter":{key:m.keys.ENTER,collapsed:!0,format:["header"],suffix:/^$/,handler:function(t,e){var o=this.quill.getLine(t.index),l=n(o,2),i=l[0],r=l[1],a=(new c.default).retain(t.index).insert("\n",e.format).retain(i.length()-r-1).retain(1,{header:null});this.quill.updateContents(a,f.default.sources.USER),this.quill.setSelection(t.index+1,f.default.sources.SILENT),this.quill.scrollIntoView()}},"list autofill":{key:" ",collapsed:!0,format:{list:!1},prefix:/^\s*?(\d+\.|-|\*|\[ ?\]|\[x\])$/,handler:function(t,e){var o=e.prefix.length,l=this.quill.getLine(t.index),i=n(l,2),r=i[0],a=i[1];if(a>o)return!0;var s=void 0;switch(e.prefix.trim()){case"[]":case"[ ]":s="unchecked";break;case"[x]":s="checked";break;case"-":case"*":s="bullet";break;default:s="ordered"}this.quill.insertText(t.index," ",f.default.sources.USER),this.quill.history.cutoff();var u=(new c.default).retain(t.index-a).delete(o+1).retain(r.length()-2-a).retain(1,{list:s});this.quill.updateContents(u,f.default.sources.USER),this.quill.history.cutoff(),this.quill.setSelection(t.index-o,f.default.sources.SILENT)}},"code exit":{key:m.keys.ENTER,collapsed:!0,format:["code-block"],prefix:/\n\n$/,suffix:/^\s+$/,handler:function(t){var e=this.quill.getLine(t.index),o=n(e,2),l=o[0],i=o[1],r=(new c.default).retain(t.index+l.length()-i-2).retain(1,{"code-block":null}).delete(1);this.quill.updateContents(r,f.default.sources.USER)}},"embed left":g(m.keys.LEFT,!1),"embed left shift":g(m.keys.LEFT,!0),"embed right":g(m.keys.RIGHT,!1),"embed right shift":g(m.keys.RIGHT,!0)}},e.default=m,e.SHORTKEY=v},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var l=function(t,e){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return function(t,e){var o=[],l=!0,n=!1,i=void 0;try{for(var r,a=t[Symbol.iterator]();!(l=(r=a.next()).done)&&(o.push(r.value),!e||o.length!==e);l=!0);}catch(t){n=!0,i=t}finally{try{!l&&a.return&&a.return()}finally{if(n)throw i}}return o}(t,e);throw new TypeError("Invalid attempt to destructure non-iterable instance")},n=function t(e,o,l){null===e&&(e=Function.prototype);var n=Object.getOwnPropertyDescriptor(e,o);if(void 0===n){var i=Object.getPrototypeOf(e);return null===i?void 0:t(i,o,l)}if("value"in n)return n.value;var r=n.get;return void 0!==r?r.call(l):void 0},i=function(){function t(t,e){for(var o=0;o<e.length;o++){var l=e[o];l.enumerable=l.enumerable||!1,l.configurable=!0,"value"in l&&(l.writable=!0),Object.defineProperty(t,l.key,l)}}return function(e,o,l){return o&&t(e.prototype,o),l&&t(e,l),e}}(),r=s(o(0)),a=s(o(7));function s(t){return t&&t.__esModule?t:{default:t}}var c=function(t){function e(t,o){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);var l=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,t));return l.selection=o,l.textNode=document.createTextNode(e.CONTENTS),l.domNode.appendChild(l.textNode),l._length=0,l}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),i(e,null,[{key:"value",value:function(){}}]),i(e,[{key:"detach",value:function(){null!=this.parent&&this.parent.removeChild(this)}},{key:"format",value:function(t,o){if(0!==this._length)return n(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"format",this).call(this,t,o);for(var l=this,i=0;null!=l&&l.statics.scope!==r.default.Scope.BLOCK_BLOT;)i+=l.offset(l.parent),l=l.parent;null!=l&&(this._length=e.CONTENTS.length,l.optimize(),l.formatAt(i,e.CONTENTS.length,t,o),this._length=0)}},{key:"index",value:function(t,o){return t===this.textNode?0:n(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"index",this).call(this,t,o)}},{key:"length",value:function(){return this._length}},{key:"position",value:function(){return[this.textNode,this.textNode.data.length]}},{key:"remove",value:function(){n(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"remove",this).call(this),this.parent=null}},{key:"restore",value:function(){if(!this.selection.composing&&null!=this.parent){var t=this.textNode,o=this.selection.getNativeRange(),n=void 0,i=void 0,s=void 0;if(null!=o&&o.start.node===t&&o.end.node===t){var c=[t,o.start.offset,o.end.offset];n=c[0],i=c[1],s=c[2]}for(;null!=this.domNode.lastChild&&this.domNode.lastChild!==this.textNode;)this.domNode.parentNode.insertBefore(this.domNode.lastChild,this.domNode);if(this.textNode.data!==e.CONTENTS){var u=this.textNode.data.split(e.CONTENTS).join("");this.next instanceof a.default?(n=this.next.domNode,this.next.insertAt(0,u),this.textNode.data=e.CONTENTS):(this.textNode.data=u,this.parent.insertBefore(r.default.create(this.textNode),this),this.textNode=document.createTextNode(e.CONTENTS),this.domNode.appendChild(this.textNode))}if(this.remove(),null!=i){var d=[i,s].map(function(t){return Math.max(0,Math.min(n.data.length,t-1))}),f=l(d,2);return i=f[0],s=f[1],{startNode:n,startOffset:i,endNode:n,endOffset:s}}}}},{key:"update",value:function(t,e){var o=this;if(t.some(function(t){return"characterData"===t.type&&t.target===o.textNode})){var l=this.restore();l&&(e.range=l)}}},{key:"value",value:function(){return""}}]),e}(r.default.Embed);c.blotName="cursor",c.className="ql-cursor",c.tagName="span",c.CONTENTS="\ufeff",e.default=c},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var l=r(o(0)),n=o(4),i=r(n);function r(t){return t&&t.__esModule?t:{default:t}}var a=function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(e.__proto__||Object.getPrototypeOf(e)).apply(this,arguments))}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),e}(l.default.Container);a.allowedChildren=[i.default,n.BlockEmbed,a],e.default=a},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.ColorStyle=e.ColorClass=e.ColorAttributor=void 0;var l,n=function(){function t(t,e){for(var o=0;o<e.length;o++){var l=e[o];l.enumerable=l.enumerable||!1,l.configurable=!0,"value"in l&&(l.writable=!0),Object.defineProperty(t,l.key,l)}}return function(e,o,l){return o&&t(e.prototype,o),l&&t(e,l),e}}(),i=function t(e,o,l){null===e&&(e=Function.prototype);var n=Object.getOwnPropertyDescriptor(e,o);if(void 0===n){var i=Object.getPrototypeOf(e);return null===i?void 0:t(i,o,l)}if("value"in n)return n.value;var r=n.get;return void 0!==r?r.call(l):void 0},r=o(0),a=(l=r)&&l.__esModule?l:{default:l},s=function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(e.__proto__||Object.getPrototypeOf(e)).apply(this,arguments))}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),n(e,[{key:"value",value:function(t){var o=i(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"value",this).call(this,t);return o.startsWith("rgb(")?(o=o.replace(/^[^\d]+/,"").replace(/[^\d]+$/,""),"#"+o.split(",").map(function(t){return("00"+parseInt(t).toString(16)).slice(-2)}).join("")):o}}]),e}(a.default.Attributor.Style),c=new a.default.Attributor.Class("color","ql-color",{scope:a.default.Scope.INLINE}),u=new s("color","color",{scope:a.default.Scope.INLINE});e.ColorAttributor=s,e.ColorClass=c,e.ColorStyle=u},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.sanitize=e.default=void 0;var l,n=function(){function t(t,e){for(var o=0;o<e.length;o++){var l=e[o];l.enumerable=l.enumerable||!1,l.configurable=!0,"value"in l&&(l.writable=!0),Object.defineProperty(t,l.key,l)}}return function(e,o,l){return o&&t(e.prototype,o),l&&t(e,l),e}}(),i=function t(e,o,l){null===e&&(e=Function.prototype);var n=Object.getOwnPropertyDescriptor(e,o);if(void 0===n){var i=Object.getPrototypeOf(e);return null===i?void 0:t(i,o,l)}if("value"in n)return n.value;var r=n.get;return void 0!==r?r.call(l):void 0},r=o(6),a=function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(e.__proto__||Object.getPrototypeOf(e)).apply(this,arguments))}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),n(e,[{key:"format",value:function(t,o){if(t!==this.statics.blotName||!o)return i(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"format",this).call(this,t,o);o=this.constructor.sanitize(o),this.domNode.setAttribute("href",o)}}],[{key:"create",value:function(t){var o=i(e.__proto__||Object.getPrototypeOf(e),"create",this).call(this,t);return t=this.sanitize(t),o.setAttribute("href",t),o.setAttribute("rel","noopener noreferrer"),o.setAttribute("target","_blank"),o}},{key:"formats",value:function(t){return t.getAttribute("href")}},{key:"sanitize",value:function(t){return s(t,this.PROTOCOL_WHITELIST)?t:this.SANITIZED_URL}}]),e}(((l=r)&&l.__esModule?l:{default:l}).default);function s(t,e){var o=document.createElement("a");o.href=t;var l=o.href.slice(0,o.href.indexOf(":"));return e.indexOf(l)>-1}a.blotName="link",a.tagName="A",a.SANITIZED_URL="about:blank",a.PROTOCOL_WHITELIST=["http","https","mailto","tel"],e.default=a,e.sanitize=s},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},n=function(){function t(t,e){for(var o=0;o<e.length;o++){var l=e[o];l.enumerable=l.enumerable||!1,l.configurable=!0,"value"in l&&(l.writable=!0),Object.defineProperty(t,l.key,l)}}return function(e,o,l){return o&&t(e.prototype,o),l&&t(e,l),e}}(),i=a(o(23)),r=a(o(107));function a(t){return t&&t.__esModule?t:{default:t}}var s=0;function c(t,e){t.setAttribute(e,!("true"===t.getAttribute(e)))}var u=function(){function t(e){var o=this;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.select=e,this.container=document.createElement("span"),this.buildPicker(),this.select.style.display="none",this.select.parentNode.insertBefore(this.container,this.select),this.label.addEventListener("mousedown",function(){o.togglePicker()}),this.label.addEventListener("keydown",function(t){switch(t.keyCode){case i.default.keys.ENTER:o.togglePicker();break;case i.default.keys.ESCAPE:o.escape(),t.preventDefault()}}),this.select.addEventListener("change",this.update.bind(this))}return n(t,[{key:"togglePicker",value:function(){this.container.classList.toggle("ql-expanded"),c(this.label,"aria-expanded"),c(this.options,"aria-hidden")}},{key:"buildItem",value:function(t){var e=this,o=document.createElement("span");return o.tabIndex="0",o.setAttribute("role","button"),o.classList.add("ql-picker-item"),t.hasAttribute("value")&&o.setAttribute("data-value",t.getAttribute("value")),t.textContent&&o.setAttribute("data-label",t.textContent),o.addEventListener("click",function(){e.selectItem(o,!0)}),o.addEventListener("keydown",function(t){switch(t.keyCode){case i.default.keys.ENTER:e.selectItem(o,!0),t.preventDefault();break;case i.default.keys.ESCAPE:e.escape(),t.preventDefault()}}),o}},{key:"buildLabel",value:function(){var t=document.createElement("span");return t.classList.add("ql-picker-label"),t.innerHTML=r.default,t.tabIndex="0",t.setAttribute("role","button"),t.setAttribute("aria-expanded","false"),this.container.appendChild(t),t}},{key:"buildOptions",value:function(){var t=this,e=document.createElement("span");e.classList.add("ql-picker-options"),e.setAttribute("aria-hidden","true"),e.tabIndex="-1",e.id="ql-picker-options-"+s,s+=1,this.label.setAttribute("aria-controls",e.id),this.options=e,[].slice.call(this.select.options).forEach(function(o){var l=t.buildItem(o);e.appendChild(l),!0===o.selected&&t.selectItem(l)}),this.container.appendChild(e)}},{key:"buildPicker",value:function(){var t=this;[].slice.call(this.select.attributes).forEach(function(e){t.container.setAttribute(e.name,e.value)}),this.container.classList.add("ql-picker"),this.label=this.buildLabel(),this.buildOptions()}},{key:"escape",value:function(){var t=this;this.close(),setTimeout(function(){return t.label.focus()},1)}},{key:"close",value:function(){this.container.classList.remove("ql-expanded"),this.label.setAttribute("aria-expanded","false"),this.options.setAttribute("aria-hidden","true")}},{key:"selectItem",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],o=this.container.querySelector(".ql-selected");if(t!==o&&(null!=o&&o.classList.remove("ql-selected"),null!=t&&(t.classList.add("ql-selected"),this.select.selectedIndex=[].indexOf.call(t.parentNode.children,t),t.hasAttribute("data-value")?this.label.setAttribute("data-value",t.getAttribute("data-value")):this.label.removeAttribute("data-value"),t.hasAttribute("data-label")?this.label.setAttribute("data-label",t.getAttribute("data-label")):this.label.removeAttribute("data-label"),e))){if("function"==typeof Event)this.select.dispatchEvent(new Event("change"));else if("object"===("undefined"==typeof Event?"undefined":l(Event))){var n=document.createEvent("Event");n.initEvent("change",!0,!0),this.select.dispatchEvent(n)}this.close()}}},{key:"update",value:function(){var t=void 0;if(this.select.selectedIndex>-1){var e=this.container.querySelector(".ql-picker-options").children[this.select.selectedIndex];t=this.select.options[this.select.selectedIndex],this.selectItem(e)}else this.selectItem(null);var o=null!=t&&t!==this.select.querySelector("option[selected]");this.label.classList.toggle("ql-active",o)}}]),t}();e.default=u},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var l=y(o(0)),n=y(o(5)),i=o(4),r=y(i),a=y(o(16)),s=y(o(25)),c=y(o(24)),u=y(o(35)),d=y(o(6)),f=y(o(22)),p=y(o(7)),h=y(o(55)),b=y(o(42)),q=y(o(23));function y(t){return t&&t.__esModule?t:{default:t}}n.default.register({"blots/block":r.default,"blots/block/embed":i.BlockEmbed,"blots/break":a.default,"blots/container":s.default,"blots/cursor":c.default,"blots/embed":u.default,"blots/inline":d.default,"blots/scroll":f.default,"blots/text":p.default,"modules/clipboard":h.default,"modules/history":b.default,"modules/keyboard":q.default}),l.default.register(r.default,a.default,c.default,d.default,f.default,p.default),e.default=n.default},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var l=o(1),n=function(){function t(t){this.domNode=t,this.domNode[l.DATA_KEY]={blot:this}}return Object.defineProperty(t.prototype,"statics",{get:function(){return this.constructor},enumerable:!0,configurable:!0}),t.create=function(t){if(null==this.tagName)throw new l.ParchmentError("Blot definition missing tagName");var e;return Array.isArray(this.tagName)?("string"==typeof t&&(t=t.toUpperCase(),parseInt(t).toString()===t&&(t=parseInt(t))),e="number"==typeof t?document.createElement(this.tagName[t-1]):this.tagName.indexOf(t)>-1?document.createElement(t):document.createElement(this.tagName[0])):e=document.createElement(this.tagName),this.className&&e.classList.add(this.className),e},t.prototype.attach=function(){null!=this.parent&&(this.scroll=this.parent.scroll)},t.prototype.clone=function(){var t=this.domNode.cloneNode(!1);return l.create(t)},t.prototype.detach=function(){null!=this.parent&&this.parent.removeChild(this),delete this.domNode[l.DATA_KEY]},t.prototype.deleteAt=function(t,e){this.isolate(t,e).remove()},t.prototype.formatAt=function(t,e,o,n){var i=this.isolate(t,e);if(null!=l.query(o,l.Scope.BLOT)&&n)i.wrap(o,n);else if(null!=l.query(o,l.Scope.ATTRIBUTE)){var r=l.create(this.statics.scope);i.wrap(r),r.format(o,n)}},t.prototype.insertAt=function(t,e,o){var n=null==o?l.create("text",e):l.create(e,o),i=this.split(t);this.parent.insertBefore(n,i)},t.prototype.insertInto=function(t,e){void 0===e&&(e=null),null!=this.parent&&this.parent.children.remove(this);var o=null;t.children.insertBefore(this,e),null!=e&&(o=e.domNode),this.domNode.parentNode==t.domNode&&this.domNode.nextSibling==o||t.domNode.insertBefore(this.domNode,o),this.parent=t,this.attach()},t.prototype.isolate=function(t,e){var o=this.split(t);return o.split(e),o},t.prototype.length=function(){return 1},t.prototype.offset=function(t){return void 0===t&&(t=this.parent),null==this.parent||this==t?0:this.parent.children.offset(this)+this.parent.offset(t)},t.prototype.optimize=function(t){null!=this.domNode[l.DATA_KEY]&&delete this.domNode[l.DATA_KEY].mutations},t.prototype.remove=function(){null!=this.domNode.parentNode&&this.domNode.parentNode.removeChild(this.domNode),this.detach()},t.prototype.replace=function(t){null!=t.parent&&(t.parent.insertBefore(this,t.next),t.remove())},t.prototype.replaceWith=function(t,e){var o="string"==typeof t?l.create(t,e):t;return o.replace(this),o},t.prototype.split=function(t,e){return 0===t?this:this.next},t.prototype.update=function(t,e){},t.prototype.wrap=function(t,e){var o="string"==typeof t?l.create(t,e):t;return null!=this.parent&&this.parent.insertBefore(o,this.next),o.appendChild(this),o},t.blotName="abstract",t}();e.default=n},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var l=o(12),n=o(32),i=o(33),r=o(1),a=function(){function t(t){this.attributes={},this.domNode=t,this.build()}return t.prototype.attribute=function(t,e){e?t.add(this.domNode,e)&&(null!=t.value(this.domNode)?this.attributes[t.attrName]=t:delete this.attributes[t.attrName]):(t.remove(this.domNode),delete this.attributes[t.attrName])},t.prototype.build=function(){var t=this;this.attributes={};var e=l.default.keys(this.domNode),o=n.default.keys(this.domNode),a=i.default.keys(this.domNode);e.concat(o).concat(a).forEach(function(e){var o=r.query(e,r.Scope.ATTRIBUTE);o instanceof l.default&&(t.attributes[o.attrName]=o)})},t.prototype.copy=function(t){var e=this;Object.keys(this.attributes).forEach(function(o){var l=e.attributes[o].value(e.domNode);t.format(o,l)})},t.prototype.move=function(t){var e=this;this.copy(t),Object.keys(this.attributes).forEach(function(t){e.attributes[t].remove(e.domNode)}),this.attributes={}},t.prototype.values=function(){var t=this;return Object.keys(this.attributes).reduce(function(e,o){return e[o]=t.attributes[o].value(t.domNode),e},{})},t}();e.default=a},function(t,e,o){"use strict";var l,n=this&&this.__extends||(l=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)e.hasOwnProperty(o)&&(t[o]=e[o])},function(t,e){function o(){this.constructor=t}l(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)});function i(t,e){return(t.getAttribute("class")||"").split(/\s+/).filter(function(t){return 0===t.indexOf(e+"-")})}Object.defineProperty(e,"__esModule",{value:!0});var r=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.keys=function(t){return(t.getAttribute("class")||"").split(/\s+/).map(function(t){return t.split("-").slice(0,-1).join("-")})},e.prototype.add=function(t,e){return!!this.canAdd(t,e)&&(this.remove(t),t.classList.add(this.keyName+"-"+e),!0)},e.prototype.remove=function(t){i(t,this.keyName).forEach(function(e){t.classList.remove(e)}),0===t.classList.length&&t.removeAttribute("class")},e.prototype.value=function(t){var e=(i(t,this.keyName)[0]||"").slice(this.keyName.length+1);return this.canAdd(t,e)?e:""},e}(o(12).default);e.default=r},function(t,e,o){"use strict";var l,n=this&&this.__extends||(l=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)e.hasOwnProperty(o)&&(t[o]=e[o])},function(t,e){function o(){this.constructor=t}l(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)});function i(t){var e=t.split("-"),o=e.slice(1).map(function(t){return t[0].toUpperCase()+t.slice(1)}).join("");return e[0]+o}Object.defineProperty(e,"__esModule",{value:!0});var r=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.keys=function(t){return(t.getAttribute("style")||"").split(";").map(function(t){return t.split(":")[0].trim()})},e.prototype.add=function(t,e){return!!this.canAdd(t,e)&&(t.style[i(this.keyName)]=e,!0)},e.prototype.remove=function(t){t.style[i(this.keyName)]="",t.getAttribute("style")||t.removeAttribute("style")},e.prototype.value=function(t){var e=t.style[i(this.keyName)];return this.canAdd(t,e)?e:""},e}(o(12).default);e.default=r},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var l=function(){function t(t,e){for(var o=0;o<e.length;o++){var l=e[o];l.enumerable=l.enumerable||!1,l.configurable=!0,"value"in l&&(l.writable=!0),Object.defineProperty(t,l.key,l)}}return function(e,o,l){return o&&t(e.prototype,o),l&&t(e,l),e}}(),n=function(){function t(e,o){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.quill=e,this.options=o,this.modules={}}return l(t,[{key:"init",value:function(){var t=this;Object.keys(this.options.modules).forEach(function(e){null==t.modules[e]&&t.addModule(e)})}},{key:"addModule",value:function(t){var e=this.quill.constructor.import("modules/"+t);return this.modules[t]=new e(this.quill,this.options.modules[t]||{}),this.modules[t]}}]),t}();n.DEFAULTS={modules:{}},n.themes={default:n},e.default=n},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var l=function(){function t(t,e){for(var o=0;o<e.length;o++){var l=e[o];l.enumerable=l.enumerable||!1,l.configurable=!0,"value"in l&&(l.writable=!0),Object.defineProperty(t,l.key,l)}}return function(e,o,l){return o&&t(e.prototype,o),l&&t(e,l),e}}(),n=function t(e,o,l){null===e&&(e=Function.prototype);var n=Object.getOwnPropertyDescriptor(e,o);if(void 0===n){var i=Object.getPrototypeOf(e);return null===i?void 0:t(i,o,l)}if("value"in n)return n.value;var r=n.get;return void 0!==r?r.call(l):void 0},i=a(o(0)),r=a(o(7));function a(t){return t&&t.__esModule?t:{default:t}}var s="\ufeff",c=function(t){function e(t){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);var o=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,t));return o.contentNode=document.createElement("span"),o.contentNode.setAttribute("contenteditable",!1),[].slice.call(o.domNode.childNodes).forEach(function(t){o.contentNode.appendChild(t)}),o.leftGuard=document.createTextNode(s),o.rightGuard=document.createTextNode(s),o.domNode.appendChild(o.leftGuard),o.domNode.appendChild(o.contentNode),o.domNode.appendChild(o.rightGuard),o}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),l(e,[{key:"index",value:function(t,o){return t===this.leftGuard?0:t===this.rightGuard?1:n(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"index",this).call(this,t,o)}},{key:"restore",value:function(t){var e=void 0,o=void 0,l=t.data.split(s).join("");if(t===this.leftGuard)if(this.prev instanceof r.default){var n=this.prev.length();this.prev.insertAt(n,l),e={startNode:this.prev.domNode,startOffset:n+l.length}}else o=document.createTextNode(l),this.parent.insertBefore(i.default.create(o),this),e={startNode:o,startOffset:l.length};else t===this.rightGuard&&(this.next instanceof r.default?(this.next.insertAt(0,l),e={startNode:this.next.domNode,startOffset:l.length}):(o=document.createTextNode(l),this.parent.insertBefore(i.default.create(o),this.next),e={startNode:o,startOffset:l.length}));return t.data=s,e}},{key:"update",value:function(t,e){var o=this;t.forEach(function(t){if("characterData"===t.type&&(t.target===o.leftGuard||t.target===o.rightGuard)){var l=o.restore(t.target);l&&(e.range=l)}})}}]),e}(i.default.Embed);e.default=c},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.AlignStyle=e.AlignClass=e.AlignAttribute=void 0;var l,n=o(0),i=(l=n)&&l.__esModule?l:{default:l},r={scope:i.default.Scope.BLOCK,whitelist:["right","center","justify"]},a=new i.default.Attributor.Attribute("align","align",r),s=new i.default.Attributor.Class("align","ql-align",r),c=new i.default.Attributor.Style("align","text-align",r);e.AlignAttribute=a,e.AlignClass=s,e.AlignStyle=c},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.BackgroundStyle=e.BackgroundClass=void 0;var l,n=o(0),i=(l=n)&&l.__esModule?l:{default:l},r=o(26),a=new i.default.Attributor.Class("background","ql-bg",{scope:i.default.Scope.INLINE}),s=new r.ColorAttributor("background","background-color",{scope:i.default.Scope.INLINE});e.BackgroundClass=a,e.BackgroundStyle=s},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.DirectionStyle=e.DirectionClass=e.DirectionAttribute=void 0;var l,n=o(0),i=(l=n)&&l.__esModule?l:{default:l},r={scope:i.default.Scope.BLOCK,whitelist:["rtl"]},a=new i.default.Attributor.Attribute("direction","dir",r),s=new i.default.Attributor.Class("direction","ql-direction",r),c=new i.default.Attributor.Style("direction","direction",r);e.DirectionAttribute=a,e.DirectionClass=s,e.DirectionStyle=c},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.FontClass=e.FontStyle=void 0;var l,n=function(){function t(t,e){for(var o=0;o<e.length;o++){var l=e[o];l.enumerable=l.enumerable||!1,l.configurable=!0,"value"in l&&(l.writable=!0),Object.defineProperty(t,l.key,l)}}return function(e,o,l){return o&&t(e.prototype,o),l&&t(e,l),e}}(),i=function t(e,o,l){null===e&&(e=Function.prototype);var n=Object.getOwnPropertyDescriptor(e,o);if(void 0===n){var i=Object.getPrototypeOf(e);return null===i?void 0:t(i,o,l)}if("value"in n)return n.value;var r=n.get;return void 0!==r?r.call(l):void 0},r=o(0),a=(l=r)&&l.__esModule?l:{default:l},s={scope:a.default.Scope.INLINE,whitelist:["serif","monospace"]},c=new a.default.Attributor.Class("font","ql-font",s),u=function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(e.__proto__||Object.getPrototypeOf(e)).apply(this,arguments))}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),n(e,[{key:"value",value:function(t){return i(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"value",this).call(this,t).replace(/["']/g,"")}}]),e}(a.default.Attributor.Style),d=new u("font","font-family",s);e.FontStyle=d,e.FontClass=c},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.SizeStyle=e.SizeClass=void 0;var l,n=o(0),i=(l=n)&&l.__esModule?l:{default:l},r=new i.default.Attributor.Class("size","ql-size",{scope:i.default.Scope.INLINE,whitelist:["small","large","huge"]}),a=new i.default.Attributor.Style("size","font-size",{scope:i.default.Scope.INLINE,whitelist:["10px","18px","32px"]});e.SizeClass=r,e.SizeStyle=a},function(t,e,o){"use strict";t.exports={align:{"":o(76),center:o(77),right:o(78),justify:o(79)},background:o(80),blockquote:o(81),bold:o(82),clean:o(83),code:o(58),"code-block":o(58),color:o(84),direction:{"":o(85),rtl:o(86)},float:{center:o(87),full:o(88),left:o(89),right:o(90)},formula:o(91),header:{1:o(92),2:o(93)},italic:o(94),image:o(95),indent:{"+1":o(96),"-1":o(97)},link:o(98),list:{ordered:o(99),bullet:o(100),check:o(101)},script:{sub:o(102),super:o(103)},strike:o(104),underline:o(105),video:o(106)}},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.getLastChangeIndex=e.default=void 0;var l=function(){function t(t,e){for(var o=0;o<e.length;o++){var l=e[o];l.enumerable=l.enumerable||!1,l.configurable=!0,"value"in l&&(l.writable=!0),Object.defineProperty(t,l.key,l)}}return function(e,o,l){return o&&t(e.prototype,o),l&&t(e,l),e}}(),n=r(o(0)),i=r(o(5));function r(t){return t&&t.__esModule?t:{default:t}}var a=function(t){function e(t,o){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);var l=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,t,o));return l.lastRecorded=0,l.ignoreChange=!1,l.clear(),l.quill.on(i.default.events.EDITOR_CHANGE,function(t,e,o,n){t!==i.default.events.TEXT_CHANGE||l.ignoreChange||(l.options.userOnly&&n!==i.default.sources.USER?l.transform(e):l.record(e,o))}),l.quill.keyboard.addBinding({key:"Z",shortKey:!0},l.undo.bind(l)),l.quill.keyboard.addBinding({key:"Z",shortKey:!0,shiftKey:!0},l.redo.bind(l)),/Win/i.test(navigator.platform)&&l.quill.keyboard.addBinding({key:"Y",shortKey:!0},l.redo.bind(l)),l}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),l(e,[{key:"change",value:function(t,e){if(0!==this.stack[t].length){var o=this.stack[t].pop();this.stack[e].push(o),this.lastRecorded=0,this.ignoreChange=!0,this.quill.updateContents(o[t],i.default.sources.USER),this.ignoreChange=!1;var l=s(o[t]);this.quill.setSelection(l)}}},{key:"clear",value:function(){this.stack={undo:[],redo:[]}}},{key:"cutoff",value:function(){this.lastRecorded=0}},{key:"record",value:function(t,e){if(0!==t.ops.length){this.stack.redo=[];var o=this.quill.getContents().diff(e),l=Date.now();if(this.lastRecorded+this.options.delay>l&&this.stack.undo.length>0){var n=this.stack.undo.pop();o=o.compose(n.undo),t=n.redo.compose(t)}else this.lastRecorded=l;this.stack.undo.push({redo:t,undo:o}),this.stack.undo.length>this.options.maxStack&&this.stack.undo.shift()}}},{key:"redo",value:function(){this.change("redo","undo")}},{key:"transform",value:function(t){this.stack.undo.forEach(function(e){e.undo=t.transform(e.undo,!0),e.redo=t.transform(e.redo,!0)}),this.stack.redo.forEach(function(e){e.undo=t.transform(e.undo,!0),e.redo=t.transform(e.redo,!0)})}},{key:"undo",value:function(){this.change("undo","redo")}}]),e}(r(o(9)).default);function s(t){var e=t.reduce(function(t,e){return t+=e.delete||0},0),o=t.length()-e;return function(t){var e=t.ops[t.ops.length-1];return null!=e&&(null!=e.insert?"string"==typeof e.insert&&e.insert.endsWith("\n"):null!=e.attributes&&Object.keys(e.attributes).some(function(t){return null!=n.default.query(t,n.default.Scope.BLOCK)}))}(t)&&(o-=1),o}a.DEFAULTS={delay:1e3,maxStack:100,userOnly:!1},e.default=a,e.getLastChangeIndex=s},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=e.BaseTooltip=void 0;var l=function(){function t(t,e){for(var o=0;o<e.length;o++){var l=e[o];l.enumerable=l.enumerable||!1,l.configurable=!0,"value"in l&&(l.writable=!0),Object.defineProperty(t,l.key,l)}}return function(e,o,l){return o&&t(e.prototype,o),l&&t(e,l),e}}(),n=function t(e,o,l){null===e&&(e=Function.prototype);var n=Object.getOwnPropertyDescriptor(e,o);if(void 0===n){var i=Object.getPrototypeOf(e);return null===i?void 0:t(i,o,l)}if("value"in n)return n.value;var r=n.get;return void 0!==r?r.call(l):void 0},i=h(o(3)),r=h(o(2)),a=h(o(8)),s=h(o(23)),c=h(o(34)),u=h(o(59)),d=h(o(60)),f=h(o(28)),p=h(o(61));function h(t){return t&&t.__esModule?t:{default:t}}function b(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function q(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}function y(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var v=[!1,"center","right","justify"],m=["#000000","#e60000","#ff9900","#ffff00","#008a00","#0066cc","#9933ff","#ffffff","#facccc","#ffebcc","#ffffcc","#cce8cc","#cce0f5","#ebd6ff","#bbbbbb","#f06666","#ffc266","#ffff66","#66b966","#66a3e0","#c285ff","#888888","#a10000","#b26b00","#b2b200","#006100","#0047b2","#6b24b2","#444444","#5c0000","#663d00","#666600","#003700","#002966","#3d1466"],g=[!1,"serif","monospace"],k=["1","2","3",!1],_=["small",!1,"large","huge"],w=function(t){function e(t,o){b(this,e);var l=q(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,t,o));return t.emitter.listenDOM("click",document.body,function e(o){if(!document.body.contains(t.root))return document.body.removeEventListener("click",e);null==l.tooltip||l.tooltip.root.contains(o.target)||document.activeElement===l.tooltip.textbox||l.quill.hasFocus()||l.tooltip.hide(),null!=l.pickers&&l.pickers.forEach(function(t){t.container.contains(o.target)||t.close()})}),l}return y(e,t),l(e,[{key:"addModule",value:function(t){var o=n(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"addModule",this).call(this,t);return"toolbar"===t&&this.extendToolbar(o),o}},{key:"buildButtons",value:function(t,e){t.forEach(function(t){(t.getAttribute("class")||"").split(/\s+/).forEach(function(o){if(o.startsWith("ql-")&&(o=o.slice(3),null!=e[o]))if("direction"===o)t.innerHTML=e[o][""]+e[o].rtl;else if("string"==typeof e[o])t.innerHTML=e[o];else{var l=t.value||"";null!=l&&e[o][l]&&(t.innerHTML=e[o][l])}})})}},{key:"buildPickers",value:function(t,e){var o=this;this.pickers=t.map(function(t){if(t.classList.contains("ql-align"))return null==t.querySelector("option")&&O(t,v),new d.default(t,e.align);if(t.classList.contains("ql-background")||t.classList.contains("ql-color")){var o=t.classList.contains("ql-background")?"background":"color";return null==t.querySelector("option")&&O(t,m,"background"===o?"#ffffff":"#000000"),new u.default(t,e[o])}return null==t.querySelector("option")&&(t.classList.contains("ql-font")?O(t,g):t.classList.contains("ql-header")?O(t,k):t.classList.contains("ql-size")&&O(t,_)),new f.default(t)}),this.quill.on(a.default.events.EDITOR_CHANGE,function(){o.pickers.forEach(function(t){t.update()})})}}]),e}(c.default);w.DEFAULTS=(0,i.default)(!0,{},c.default.DEFAULTS,{modules:{toolbar:{handlers:{formula:function(){this.quill.theme.tooltip.edit("formula")},image:function(){var t=this,e=this.container.querySelector("input.ql-image[type=file]");null==e&&((e=document.createElement("input")).setAttribute("type","file"),e.setAttribute("accept","image/png, image/gif, image/jpeg, image/bmp, image/x-icon"),e.classList.add("ql-image"),e.addEventListener("change",function(){if(null!=e.files&&null!=e.files[0]){var o=new FileReader;o.onload=function(o){var l=t.quill.getSelection(!0);t.quill.updateContents((new r.default).retain(l.index).delete(l.length).insert({image:o.target.result}),a.default.sources.USER),t.quill.setSelection(l.index+1,a.default.sources.SILENT),e.value=""},o.readAsDataURL(e.files[0])}}),this.container.appendChild(e)),e.click()},video:function(){this.quill.theme.tooltip.edit("video")}}}}});var x=function(t){function e(t,o){b(this,e);var l=q(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,t,o));return l.textbox=l.root.querySelector('input[type="text"]'),l.listen(),l}return y(e,t),l(e,[{key:"listen",value:function(){var t=this;this.textbox.addEventListener("keydown",function(e){s.default.match(e,"enter")?(t.save(),e.preventDefault()):s.default.match(e,"escape")&&(t.cancel(),e.preventDefault())})}},{key:"cancel",value:function(){this.hide()}},{key:"edit",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"link",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;this.root.classList.remove("ql-hidden"),this.root.classList.add("ql-editing"),null!=e?this.textbox.value=e:t!==this.root.getAttribute("data-mode")&&(this.textbox.value=""),this.position(this.quill.getBounds(this.quill.selection.savedRange)),this.textbox.select(),this.textbox.setAttribute("placeholder",this.textbox.getAttribute("data-"+t)||""),this.root.setAttribute("data-mode",t)}},{key:"restoreFocus",value:function(){var t=this.quill.scrollingContainer.scrollTop;this.quill.focus(),this.quill.scrollingContainer.scrollTop=t}},{key:"save",value:function(){var t,e,o=this.textbox.value;switch(this.root.getAttribute("data-mode")){case"link":var l=this.quill.root.scrollTop;this.linkRange?(this.quill.formatText(this.linkRange,"link",o,a.default.sources.USER),delete this.linkRange):(this.restoreFocus(),this.quill.format("link",o,a.default.sources.USER)),this.quill.root.scrollTop=l;break;case"video":o=(e=(t=o).match(/^(?:(https?):\/\/)?(?:(?:www|m)\.)?youtube\.com\/watch.*v=([a-zA-Z0-9_-]+)/)||t.match(/^(?:(https?):\/\/)?(?:(?:www|m)\.)?youtu\.be\/([a-zA-Z0-9_-]+)/))?(e[1]||"https")+"://www.youtube.com/embed/"+e[2]+"?showinfo=0":(e=t.match(/^(?:(https?):\/\/)?(?:www\.)?vimeo\.com\/(\d+)/))?(e[1]||"https")+"://player.vimeo.com/video/"+e[2]+"/":t;case"formula":if(!o)break;var n=this.quill.getSelection(!0);if(null!=n){var i=n.index+n.length;this.quill.insertEmbed(i,this.root.getAttribute("data-mode"),o,a.default.sources.USER),"formula"===this.root.getAttribute("data-mode")&&this.quill.insertText(i+1," ",a.default.sources.USER),this.quill.setSelection(i+2,a.default.sources.USER)}}this.textbox.value="",this.hide()}}]),e}(p.default);function O(t,e){var o=arguments.length>2&&void 0!==arguments[2]&&arguments[2];e.forEach(function(e){var l=document.createElement("option");e===o?l.setAttribute("selected","selected"):l.setAttribute("value",e),t.appendChild(l)})}e.BaseTooltip=x,e.default=w},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var l=function(){function t(){this.head=this.tail=null,this.length=0}return t.prototype.append=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];this.insertBefore(t[0],null),t.length>1&&this.append.apply(this,t.slice(1))},t.prototype.contains=function(t){for(var e,o=this.iterator();e=o();)if(e===t)return!0;return!1},t.prototype.insertBefore=function(t,e){t&&(t.next=e,null!=e?(t.prev=e.prev,null!=e.prev&&(e.prev.next=t),e.prev=t,e===this.head&&(this.head=t)):null!=this.tail?(this.tail.next=t,t.prev=this.tail,this.tail=t):(t.prev=null,this.head=this.tail=t),this.length+=1)},t.prototype.offset=function(t){for(var e=0,o=this.head;null!=o;){if(o===t)return e;e+=o.length(),o=o.next}return-1},t.prototype.remove=function(t){this.contains(t)&&(null!=t.prev&&(t.prev.next=t.next),null!=t.next&&(t.next.prev=t.prev),t===this.head&&(this.head=t.next),t===this.tail&&(this.tail=t.prev),this.length-=1)},t.prototype.iterator=function(t){return void 0===t&&(t=this.head),function(){var e=t;return null!=t&&(t=t.next),e}},t.prototype.find=function(t,e){void 0===e&&(e=!1);for(var o,l=this.iterator();o=l();){var n=o.length();if(t<n||e&&t===n&&(null==o.next||0!==o.next.length()))return[o,t];t-=n}return[null,0]},t.prototype.forEach=function(t){for(var e,o=this.iterator();e=o();)t(e)},t.prototype.forEachAt=function(t,e,o){if(!(e<=0))for(var l,n=this.find(t),i=n[0],r=t-n[1],a=this.iterator(i);(l=a())&&r<t+e;){var s=l.length();t>r?o(l,t-r,Math.min(e,r+s-t)):o(l,0,Math.min(s,t+e-r)),r+=s}},t.prototype.map=function(t){return this.reduce(function(e,o){return e.push(t(o)),e},[])},t.prototype.reduce=function(t,e){for(var o,l=this.iterator();o=l();)e=t(e,o);return e},t}();e.default=l},function(t,e,o){"use strict";var l,n=this&&this.__extends||(l=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)e.hasOwnProperty(o)&&(t[o]=e[o])},function(t,e){function o(){this.constructor=t}l(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)});Object.defineProperty(e,"__esModule",{value:!0});var i=o(17),r=o(1),a={attributes:!0,characterData:!0,characterDataOldValue:!0,childList:!0,subtree:!0},s=function(t){function e(e){var o=t.call(this,e)||this;return o.scroll=o,o.observer=new MutationObserver(function(t){o.update(t)}),o.observer.observe(o.domNode,a),o.attach(),o}return n(e,t),e.prototype.detach=function(){t.prototype.detach.call(this),this.observer.disconnect()},e.prototype.deleteAt=function(e,o){this.update(),0===e&&o===this.length()?this.children.forEach(function(t){t.remove()}):t.prototype.deleteAt.call(this,e,o)},e.prototype.formatAt=function(e,o,l,n){this.update(),t.prototype.formatAt.call(this,e,o,l,n)},e.prototype.insertAt=function(e,o,l){this.update(),t.prototype.insertAt.call(this,e,o,l)},e.prototype.optimize=function(e,o){var l=this;void 0===e&&(e=[]),void 0===o&&(o={}),t.prototype.optimize.call(this,o);for(var n=[].slice.call(this.observer.takeRecords());n.length>0;)e.push(n.pop());for(var a=function(t,e){void 0===e&&(e=!0),null!=t&&t!==l&&null!=t.domNode.parentNode&&(null==t.domNode[r.DATA_KEY].mutations&&(t.domNode[r.DATA_KEY].mutations=[]),e&&a(t.parent))},s=function(t){null!=t.domNode[r.DATA_KEY]&&null!=t.domNode[r.DATA_KEY].mutations&&(t instanceof i.default&&t.children.forEach(s),t.optimize(o))},c=e,u=0;c.length>0;u+=1){if(u>=100)throw new Error("[Parchment] Maximum optimize iterations reached");for(c.forEach(function(t){var e=r.find(t.target,!0);null!=e&&(e.domNode===t.target&&("childList"===t.type?(a(r.find(t.previousSibling,!1)),[].forEach.call(t.addedNodes,function(t){var e=r.find(t,!1);a(e,!1),e instanceof i.default&&e.children.forEach(function(t){a(t,!1)})})):"attributes"===t.type&&a(e.prev)),a(e))}),this.children.forEach(s),n=(c=[].slice.call(this.observer.takeRecords())).slice();n.length>0;)e.push(n.pop())}},e.prototype.update=function(e,o){var l=this;void 0===o&&(o={}),(e=e||this.observer.takeRecords()).map(function(t){var e=r.find(t.target,!0);return null==e?null:null==e.domNode[r.DATA_KEY].mutations?(e.domNode[r.DATA_KEY].mutations=[t],e):(e.domNode[r.DATA_KEY].mutations.push(t),null)}).forEach(function(t){null!=t&&t!==l&&null!=t.domNode[r.DATA_KEY]&&t.update(t.domNode[r.DATA_KEY].mutations||[],o)}),null!=this.domNode[r.DATA_KEY].mutations&&t.prototype.update.call(this,this.domNode[r.DATA_KEY].mutations,o),this.optimize(e,o)},e.blotName="scroll",e.defaultChild="block",e.scope=r.Scope.BLOCK_BLOT,e.tagName="DIV",e}(i.default);e.default=s},function(t,e,o){"use strict";var l,n=this&&this.__extends||(l=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)e.hasOwnProperty(o)&&(t[o]=e[o])},function(t,e){function o(){this.constructor=t}l(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)});Object.defineProperty(e,"__esModule",{value:!0});var i=o(18),r=o(1),a=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.formats=function(o){if(o.tagName!==e.tagName)return t.formats.call(this,o)},e.prototype.format=function(o,l){var n=this;o!==this.statics.blotName||l?t.prototype.format.call(this,o,l):(this.children.forEach(function(t){t instanceof i.default||(t=t.wrap(e.blotName,!0)),n.attributes.copy(t)}),this.unwrap())},e.prototype.formatAt=function(e,o,l,n){null!=this.formats()[l]||r.query(l,r.Scope.ATTRIBUTE)?this.isolate(e,o).format(l,n):t.prototype.formatAt.call(this,e,o,l,n)},e.prototype.optimize=function(o){t.prototype.optimize.call(this,o);var l=this.formats();if(0===Object.keys(l).length)return this.unwrap();var n=this.next;n instanceof e&&n.prev===this&&function(t,e){if(Object.keys(t).length!==Object.keys(e).length)return!1;for(var o in t)if(t[o]!==e[o])return!1;return!0}(l,n.formats())&&(n.moveChildren(this),n.remove())},e.blotName="inline",e.scope=r.Scope.INLINE_BLOT,e.tagName="SPAN",e}(i.default);e.default=a},function(t,e,o){"use strict";var l,n=this&&this.__extends||(l=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)e.hasOwnProperty(o)&&(t[o]=e[o])},function(t,e){function o(){this.constructor=t}l(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)});Object.defineProperty(e,"__esModule",{value:!0});var i=o(18),r=o(1),a=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.formats=function(o){var l=r.query(e.blotName).tagName;if(o.tagName!==l)return t.formats.call(this,o)},e.prototype.format=function(o,l){null!=r.query(o,r.Scope.BLOCK)&&(o!==this.statics.blotName||l?t.prototype.format.call(this,o,l):this.replaceWith(e.blotName))},e.prototype.formatAt=function(e,o,l,n){null!=r.query(l,r.Scope.BLOCK)?this.format(l,n):t.prototype.formatAt.call(this,e,o,l,n)},e.prototype.insertAt=function(e,o,l){if(null==l||null!=r.query(o,r.Scope.INLINE))t.prototype.insertAt.call(this,e,o,l);else{var n=this.split(e),i=r.create(o,l);n.parent.insertBefore(i,n)}},e.prototype.update=function(e,o){navigator.userAgent.match(/Trident/)?this.build():t.prototype.update.call(this,e,o)},e.blotName="block",e.scope=r.Scope.BLOCK_BLOT,e.tagName="P",e}(i.default);e.default=a},function(t,e,o){"use strict";var l,n=this&&this.__extends||(l=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)e.hasOwnProperty(o)&&(t[o]=e[o])},function(t,e){function o(){this.constructor=t}l(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)});Object.defineProperty(e,"__esModule",{value:!0});var i=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.formats=function(t){},e.prototype.format=function(e,o){t.prototype.formatAt.call(this,0,this.length(),e,o)},e.prototype.formatAt=function(e,o,l,n){0===e&&o===this.length()?this.format(l,n):t.prototype.formatAt.call(this,e,o,l,n)},e.prototype.formats=function(){return this.statics.formats(this.domNode)},e}(o(19).default);e.default=i},function(t,e,o){"use strict";var l,n=this&&this.__extends||(l=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)e.hasOwnProperty(o)&&(t[o]=e[o])},function(t,e){function o(){this.constructor=t}l(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)});Object.defineProperty(e,"__esModule",{value:!0});var i=o(19),r=o(1),a=function(t){function e(e){var o=t.call(this,e)||this;return o.text=o.statics.value(o.domNode),o}return n(e,t),e.create=function(t){return document.createTextNode(t)},e.value=function(t){var e=t.data;return e.normalize&&(e=e.normalize()),e},e.prototype.deleteAt=function(t,e){this.domNode.data=this.text=this.text.slice(0,t)+this.text.slice(t+e)},e.prototype.index=function(t,e){return this.domNode===t?e:-1},e.prototype.insertAt=function(e,o,l){null==l?(this.text=this.text.slice(0,e)+o+this.text.slice(e),this.domNode.data=this.text):t.prototype.insertAt.call(this,e,o,l)},e.prototype.length=function(){return this.text.length},e.prototype.optimize=function(o){t.prototype.optimize.call(this,o),this.text=this.statics.value(this.domNode),0===this.text.length?this.remove():this.next instanceof e&&this.next.prev===this&&(this.insertAt(this.length(),this.next.value()),this.next.remove())},e.prototype.position=function(t,e){return void 0===e&&(e=!1),[this.domNode,t]},e.prototype.split=function(t,e){if(void 0===e&&(e=!1),!e){if(0===t)return this;if(t===this.length())return this.next}var o=r.create(this.domNode.splitText(t));return this.parent.insertBefore(o,this.next),this.text=this.statics.value(this.domNode),o},e.prototype.update=function(t,e){var o=this;t.some(function(t){return"characterData"===t.type&&t.target===o.domNode})&&(this.text=this.statics.value(this.domNode))},e.prototype.value=function(){return this.text},e.blotName="text",e.scope=r.Scope.INLINE_BLOT,e}(i.default);e.default=a},function(t,e,o){"use strict";var l=document.createElement("div");if(l.classList.toggle("test-class",!1),l.classList.contains("test-class")){var n=DOMTokenList.prototype.toggle;DOMTokenList.prototype.toggle=function(t,e){return arguments.length>1&&!this.contains(t)==!e?e:n.call(this,t)}}String.prototype.startsWith||(String.prototype.startsWith=function(t,e){return e=e||0,this.substr(e,t.length)===t}),String.prototype.endsWith||(String.prototype.endsWith=function(t,e){var o=this.toString();("number"!=typeof e||!isFinite(e)||Math.floor(e)!==e||e>o.length)&&(e=o.length),e-=t.length;var l=o.indexOf(t,e);return-1!==l&&l===e}),Array.prototype.find||Object.defineProperty(Array.prototype,"find",{value:function(t){if(null===this)throw new TypeError("Array.prototype.find called on null or undefined");if("function"!=typeof t)throw new TypeError("predicate must be a function");for(var e,o=Object(this),l=o.length>>>0,n=arguments[1],i=0;i<l;i++)if(e=o[i],t.call(n,e,i,o))return e}}),document.addEventListener("DOMContentLoaded",function(){document.execCommand("enableObjectResizing",!1,!1),document.execCommand("autoUrlDetect",!1,!1)})},function(t,e){var o=-1;function l(t,e,s){if(t==e)return t?[[0,t]]:[];(s<0||t.length<s)&&(s=null);var u=i(t,e),d=t.substring(0,u);u=r(t=t.substring(u),e=e.substring(u));var f=t.substring(t.length-u),p=function(t,e){var a;if(!t)return[[1,e]];if(!e)return[[o,t]];var s=t.length>e.length?t:e,c=t.length>e.length?e:t,u=s.indexOf(c);if(-1!=u)return a=[[1,s.substring(0,u)],[0,c],[1,s.substring(u+c.length)]],t.length>e.length&&(a[0][0]=a[2][0]=o),a;if(1==c.length)return[[o,t],[1,e]];var d=function(t,e){var o=t.length>e.length?t:e,l=t.length>e.length?e:t;if(o.length<4||2*l.length<o.length)return null;function n(t,e,o){for(var l,n,a,s,c=t.substring(o,o+Math.floor(t.length/4)),u=-1,d="";-1!=(u=e.indexOf(c,u+1));){var f=i(t.substring(o),e.substring(u)),p=r(t.substring(0,o),e.substring(0,u));d.length<p+f&&(d=e.substring(u-p,u)+e.substring(u,u+f),l=t.substring(0,o-p),n=t.substring(o+f),a=e.substring(0,u-p),s=e.substring(u+f))}return 2*d.length>=t.length?[l,n,a,s,d]:null}var a,s,c,u,d,f=n(o,l,Math.ceil(o.length/4)),p=n(o,l,Math.ceil(o.length/2));if(!f&&!p)return null;a=p?f&&f[4].length>p[4].length?f:p:f,t.length>e.length?(s=a[0],c=a[1],u=a[2],d=a[3]):(u=a[0],d=a[1],s=a[2],c=a[3]);var h=a[4];return[s,c,u,d,h]}(t,e);if(d){var f=d[0],p=d[1],h=d[2],b=d[3],q=d[4],y=l(f,h),v=l(p,b);return y.concat([[0,q]],v)}return function(t,e){for(var l=t.length,i=e.length,r=Math.ceil((l+i)/2),a=r,s=2*r,c=new Array(s),u=new Array(s),d=0;d<s;d++)c[d]=-1,u[d]=-1;c[a+1]=0,u[a+1]=0;for(var f=l-i,p=f%2!=0,h=0,b=0,q=0,y=0,v=0;v<r;v++){for(var m=-v+h;m<=v-b;m+=2){for(var g=a+m,k=(E=m==-v||m!=v&&c[g-1]<c[g+1]?c[g+1]:c[g-1]+1)-m;E<l&&k<i&&t.charAt(E)==e.charAt(k);)E++,k++;if(c[g]=E,E>l)b+=2;else if(k>i)h+=2;else if(p&&(x=a+f-m)>=0&&x<s&&-1!=u[x]&&E>=(w=l-u[x]))return n(t,e,E,k)}for(var _=-v+q;_<=v-y;_+=2){for(var w,x=a+_,O=(w=_==-v||_!=v&&u[x-1]<u[x+1]?u[x+1]:u[x-1]+1)-_;w<l&&O<i&&t.charAt(l-w-1)==e.charAt(i-O-1);)w++,O++;if(u[x]=w,w>l)y+=2;else if(O>i)q+=2;else if(!p){var E;if((g=a+f-_)>=0&&g<s&&-1!=c[g])if(k=a+(E=c[g])-g,E>=(w=l-w))return n(t,e,E,k)}}}return[[o,t],[1,e]]}(t,e)}(t=t.substring(0,t.length-u),e=e.substring(0,e.length-u));return d&&p.unshift([0,d]),f&&p.push([0,f]),a(p),null!=s&&(p=function(t,e){var l=function(t,e){if(0===e)return[0,t];for(var l=0,n=0;n<t.length;n++){var i=t[n];if(i[0]===o||0===i[0]){var r=l+i[1].length;if(e===r)return[n+1,t];if(e<r){t=t.slice();var a=e-l,s=[i[0],i[1].slice(0,a)],c=[i[0],i[1].slice(a)];return t.splice(n,1,s,c),[n+1,t]}l=r}}throw new Error("cursor_pos is out of bounds!")}(t,e),n=l[1],i=l[0],r=n[i],a=n[i+1];if(null==r)return t;if(0!==r[0])return t;if(null!=a&&r[1]+a[1]===a[1]+r[1])return n.splice(i,2,a,r),c(n,i,2);if(null!=a&&0===a[1].indexOf(r[1])){n.splice(i,2,[a[0],r[1]],[0,r[1]]);var s=a[1].slice(r[1].length);return s.length>0&&n.splice(i+2,0,[a[0],s]),c(n,i,3)}return t}(p,s)),p=function(t){for(var e=!1,l=function(t){return t.charCodeAt(0)>=56320&&t.charCodeAt(0)<=57343},n=function(t){return t.charCodeAt(t.length-1)>=55296&&t.charCodeAt(t.length-1)<=56319},i=2;i<t.length;i+=1)0===t[i-2][0]&&n(t[i-2][1])&&t[i-1][0]===o&&l(t[i-1][1])&&1===t[i][0]&&l(t[i][1])&&(e=!0,t[i-1][1]=t[i-2][1].slice(-1)+t[i-1][1],t[i][1]=t[i-2][1].slice(-1)+t[i][1],t[i-2][1]=t[i-2][1].slice(0,-1));if(!e)return t;var r=[];for(i=0;i<t.length;i+=1)t[i][1].length>0&&r.push(t[i]);return r}(p)}function n(t,e,o,n){var i=t.substring(0,o),r=e.substring(0,n),a=t.substring(o),s=e.substring(n),c=l(i,r),u=l(a,s);return c.concat(u)}function i(t,e){if(!t||!e||t.charAt(0)!=e.charAt(0))return 0;for(var o=0,l=Math.min(t.length,e.length),n=l,i=0;o<n;)t.substring(i,n)==e.substring(i,n)?i=o=n:l=n,n=Math.floor((l-o)/2+o);return n}function r(t,e){if(!t||!e||t.charAt(t.length-1)!=e.charAt(e.length-1))return 0;for(var o=0,l=Math.min(t.length,e.length),n=l,i=0;o<n;)t.substring(t.length-n,t.length-i)==e.substring(e.length-n,e.length-i)?i=o=n:l=n,n=Math.floor((l-o)/2+o);return n}function a(t){t.push([0,""]);for(var e,l=0,n=0,s=0,c="",u="";l<t.length;)switch(t[l][0]){case 1:s++,u+=t[l][1],l++;break;case o:n++,c+=t[l][1],l++;break;case 0:n+s>1?(0!==n&&0!==s&&(0!==(e=i(u,c))&&(l-n-s>0&&0==t[l-n-s-1][0]?t[l-n-s-1][1]+=u.substring(0,e):(t.splice(0,0,[0,u.substring(0,e)]),l++),u=u.substring(e),c=c.substring(e)),0!==(e=r(u,c))&&(t[l][1]=u.substring(u.length-e)+t[l][1],u=u.substring(0,u.length-e),c=c.substring(0,c.length-e))),0===n?t.splice(l-s,n+s,[1,u]):0===s?t.splice(l-n,n+s,[o,c]):t.splice(l-n-s,n+s,[o,c],[1,u]),l=l-n-s+(n?1:0)+(s?1:0)+1):0!==l&&0==t[l-1][0]?(t[l-1][1]+=t[l][1],t.splice(l,1)):l++,s=0,n=0,c="",u=""}""===t[t.length-1][1]&&t.pop();var d=!1;for(l=1;l<t.length-1;)0==t[l-1][0]&&0==t[l+1][0]&&(t[l][1].substring(t[l][1].length-t[l-1][1].length)==t[l-1][1]?(t[l][1]=t[l-1][1]+t[l][1].substring(0,t[l][1].length-t[l-1][1].length),t[l+1][1]=t[l-1][1]+t[l+1][1],t.splice(l-1,1),d=!0):t[l][1].substring(0,t[l+1][1].length)==t[l+1][1]&&(t[l-1][1]+=t[l+1][1],t[l][1]=t[l][1].substring(t[l+1][1].length)+t[l+1][1],t.splice(l+1,1),d=!0)),l++;d&&a(t)}var s=l;function c(t,e,o){for(var l=e+o-1;l>=0&&l>=e-1;l--)if(l+1<t.length){var n=t[l],i=t[l+1];n[0]===i[1]&&t.splice(l,2,[n[0],n[1]+i[1]])}return t}s.INSERT=1,s.DELETE=o,s.EQUAL=0,t.exports=s},function(t,e){function o(t){var e=[];for(var o in t)e.push(o);return e}(t.exports="function"==typeof Object.keys?Object.keys:o).shim=o},function(t,e){var o="[object Arguments]"==function(){return Object.prototype.toString.call(arguments)}();function l(t){return"[object Arguments]"==Object.prototype.toString.call(t)}function n(t){return t&&"object"==typeof t&&"number"==typeof t.length&&Object.prototype.hasOwnProperty.call(t,"callee")&&!Object.prototype.propertyIsEnumerable.call(t,"callee")||!1}(e=t.exports=o?l:n).supported=l,e.unsupported=n},function(t,e){"use strict";var o=Object.prototype.hasOwnProperty,l="~";function n(){}function i(t,e,o){this.fn=t,this.context=e,this.once=o||!1}function r(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),(new n).__proto__||(l=!1)),r.prototype.eventNames=function(){var t,e,n=[];if(0===this._eventsCount)return n;for(e in t=this._events)o.call(t,e)&&n.push(l?e.slice(1):e);return Object.getOwnPropertySymbols?n.concat(Object.getOwnPropertySymbols(t)):n},r.prototype.listeners=function(t,e){var o=l?l+t:t,n=this._events[o];if(e)return!!n;if(!n)return[];if(n.fn)return[n.fn];for(var i=0,r=n.length,a=new Array(r);i<r;i++)a[i]=n[i].fn;return a},r.prototype.emit=function(t,e,o,n,i,r){var a=l?l+t:t;if(!this._events[a])return!1;var s,c,u=this._events[a],d=arguments.length;if(u.fn){switch(u.once&&this.removeListener(t,u.fn,void 0,!0),d){case 1:return u.fn.call(u.context),!0;case 2:return u.fn.call(u.context,e),!0;case 3:return u.fn.call(u.context,e,o),!0;case 4:return u.fn.call(u.context,e,o,n),!0;case 5:return u.fn.call(u.context,e,o,n,i),!0;case 6:return u.fn.call(u.context,e,o,n,i,r),!0}for(c=1,s=new Array(d-1);c<d;c++)s[c-1]=arguments[c];u.fn.apply(u.context,s)}else{var f,p=u.length;for(c=0;c<p;c++)switch(u[c].once&&this.removeListener(t,u[c].fn,void 0,!0),d){case 1:u[c].fn.call(u[c].context);break;case 2:u[c].fn.call(u[c].context,e);break;case 3:u[c].fn.call(u[c].context,e,o);break;case 4:u[c].fn.call(u[c].context,e,o,n);break;default:if(!s)for(f=1,s=new Array(d-1);f<d;f++)s[f-1]=arguments[f];u[c].fn.apply(u[c].context,s)}}return!0},r.prototype.on=function(t,e,o){var n=new i(e,o||this),r=l?l+t:t;return this._events[r]?this._events[r].fn?this._events[r]=[this._events[r],n]:this._events[r].push(n):(this._events[r]=n,this._eventsCount++),this},r.prototype.once=function(t,e,o){var n=new i(e,o||this,!0),r=l?l+t:t;return this._events[r]?this._events[r].fn?this._events[r]=[this._events[r],n]:this._events[r].push(n):(this._events[r]=n,this._eventsCount++),this},r.prototype.removeListener=function(t,e,o,i){var r=l?l+t:t;if(!this._events[r])return this;if(!e)return 0===--this._eventsCount?this._events=new n:delete this._events[r],this;var a=this._events[r];if(a.fn)a.fn!==e||i&&!a.once||o&&a.context!==o||(0===--this._eventsCount?this._events=new n:delete this._events[r]);else{for(var s=0,c=[],u=a.length;s<u;s++)(a[s].fn!==e||i&&!a[s].once||o&&a[s].context!==o)&&c.push(a[s]);c.length?this._events[r]=1===c.length?c[0]:c:0===--this._eventsCount?this._events=new n:delete this._events[r]}return this},r.prototype.removeAllListeners=function(t){var e;return t?(e=l?l+t:t,this._events[e]&&(0===--this._eventsCount?this._events=new n:delete this._events[e])):(this._events=new n,this._eventsCount=0),this},r.prototype.off=r.prototype.removeListener,r.prototype.addListener=r.prototype.on,r.prototype.setMaxListeners=function(){return this},r.prefixed=l,r.EventEmitter=r,void 0!==t&&(t.exports=r)},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.matchText=e.matchSpacing=e.matchNewline=e.matchBlot=e.matchAttributor=e.default=void 0;var l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},n=function(t,e){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return function(t,e){var o=[],l=!0,n=!1,i=void 0;try{for(var r,a=t[Symbol.iterator]();!(l=(r=a.next()).done)&&(o.push(r.value),!e||o.length!==e);l=!0);}catch(t){n=!0,i=t}finally{try{!l&&a.return&&a.return()}finally{if(n)throw i}}return o}(t,e);throw new TypeError("Invalid attempt to destructure non-iterable instance")},i=function(){function t(t,e){for(var o=0;o<e.length;o++){var l=e[o];l.enumerable=l.enumerable||!1,l.configurable=!0,"value"in l&&(l.writable=!0),Object.defineProperty(t,l.key,l)}}return function(e,o,l){return o&&t(e.prototype,o),l&&t(e,l),e}}(),r=m(o(3)),a=m(o(2)),s=m(o(0)),c=m(o(5)),u=m(o(10)),d=m(o(9)),f=o(36),p=o(37),h=m(o(13)),b=o(26),q=o(38),y=o(39),v=o(40);function m(t){return t&&t.__esModule?t:{default:t}}function g(t,e,o){return e in t?Object.defineProperty(t,e,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[e]=o,t}var k=(0,u.default)("quill:clipboard"),_="__ql-matcher",w=[[Node.TEXT_NODE,R],[Node.TEXT_NODE,M],["br",function(t,e){return j(e,"\n")||e.insert("\n"),e}],[Node.ELEMENT_NODE,M],[Node.ELEMENT_NODE,L],[Node.ELEMENT_NODE,I],[Node.ELEMENT_NODE,C],[Node.ELEMENT_NODE,function(t,e){var o={},l=t.style||{};return l.fontStyle&&"italic"===A(t).fontStyle&&(o.italic=!0),l.fontWeight&&(A(t).fontWeight.startsWith("bold")||parseInt(A(t).fontWeight)>=700)&&(o.bold=!0),Object.keys(o).length>0&&(e=N(e,o)),parseFloat(l.textIndent||0)>0&&(e=(new a.default).insert("\t").concat(e)),e}],["li",function(t,e){var o=s.default.query(t);if(null==o||"list-item"!==o.blotName||!j(e,"\n"))return e;for(var l=-1,n=t.parentNode;!n.classList.contains("ql-clipboard");)"list"===(s.default.query(n)||{}).blotName&&(l+=1),n=n.parentNode;return l<=0?e:e.compose((new a.default).retain(e.length()-1).retain(1,{indent:l}))}],["b",P.bind(P,"bold")],["i",P.bind(P,"italic")],["style",function(){return new a.default}]],x=[f.AlignAttribute,q.DirectionAttribute].reduce(function(t,e){return t[e.keyName]=e,t},{}),O=[f.AlignStyle,p.BackgroundStyle,b.ColorStyle,q.DirectionStyle,y.FontStyle,v.SizeStyle].reduce(function(t,e){return t[e.keyName]=e,t},{}),E=function(t){function e(t,o){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);var l=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,t,o));return l.quill.root.addEventListener("paste",l.onPaste.bind(l)),l.container=l.quill.addContainer("ql-clipboard"),l.container.setAttribute("contenteditable",!0),l.container.setAttribute("tabindex",-1),l.matchers=[],w.concat(l.options.matchers).forEach(function(t){var e=n(t,2),i=e[0],r=e[1];(o.matchVisual||r!==I)&&l.addMatcher(i,r)}),l}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),i(e,[{key:"addMatcher",value:function(t,e){this.matchers.push([t,e])}},{key:"convert",value:function(t){if("string"==typeof t)return this.container.innerHTML=t.replace(/\>\r?\n +\</g,"><"),this.convert();var e=this.quill.getFormat(this.quill.selection.savedRange.index);if(e[h.default.blotName]){var o=this.container.innerText;return this.container.innerHTML="",(new a.default).insert(o,g({},h.default.blotName,e[h.default.blotName]))}var l=this.prepareMatching(),i=n(l,2),r=i[0],s=i[1],c=T(this.container,r,s);return j(c,"\n")&&null==c.ops[c.ops.length-1].attributes&&(c=c.compose((new a.default).retain(c.length()-1).delete(1))),k.log("convert",this.container.innerHTML,c),this.container.innerHTML="",c}},{key:"dangerouslyPasteHTML",value:function(t,e){var o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:c.default.sources.API;if("string"==typeof t)this.quill.setContents(this.convert(t),e),this.quill.setSelection(0,c.default.sources.SILENT);else{var l=this.convert(e);this.quill.updateContents((new a.default).retain(t).concat(l),o),this.quill.setSelection(t+l.length(),c.default.sources.SILENT)}}},{key:"onPaste",value:function(t){var e=this;if(!t.defaultPrevented&&this.quill.isEnabled()){var o=this.quill.getSelection(),l=(new a.default).retain(o.index),n=this.quill.scrollingContainer.scrollTop;this.container.focus(),this.quill.selection.update(c.default.sources.SILENT),setTimeout(function(){l=l.concat(e.convert()).delete(o.length),e.quill.updateContents(l,c.default.sources.USER),e.quill.setSelection(l.length()-o.length,c.default.sources.SILENT),e.quill.scrollingContainer.scrollTop=n,e.quill.focus()},1)}}},{key:"prepareMatching",value:function(){var t=this,e=[],o=[];return this.matchers.forEach(function(l){var i=n(l,2),r=i[0],a=i[1];switch(r){case Node.TEXT_NODE:o.push(a);break;case Node.ELEMENT_NODE:e.push(a);break;default:[].forEach.call(t.container.querySelectorAll(r),function(t){t[_]=t[_]||[],t[_].push(a)})}}),[e,o]}}]),e}(d.default);function N(t,e,o){return"object"===(void 0===e?"undefined":l(e))?Object.keys(e).reduce(function(t,o){return N(t,o,e[o])},t):t.reduce(function(t,l){return l.attributes&&l.attributes[e]?t.push(l):t.insert(l.insert,(0,r.default)({},g({},e,o),l.attributes))},new a.default)}function A(t){if(t.nodeType!==Node.ELEMENT_NODE)return{};var e="__ql-computed-style";return t[e]||(t[e]=window.getComputedStyle(t))}function j(t,e){for(var o="",l=t.ops.length-1;l>=0&&o.length<e.length;--l){var n=t.ops[l];if("string"!=typeof n.insert)break;o=n.insert+o}return o.slice(-1*e.length)===e}function S(t){if(0===t.childNodes.length)return!1;var e=A(t);return["block","list-item"].indexOf(e.display)>-1}function T(t,e,o){return t.nodeType===t.TEXT_NODE?o.reduce(function(e,o){return o(t,e)},new a.default):t.nodeType===t.ELEMENT_NODE?[].reduce.call(t.childNodes||[],function(l,n){var i=T(n,e,o);return n.nodeType===t.ELEMENT_NODE&&(i=e.reduce(function(t,e){return e(n,t)},i),i=(n[_]||[]).reduce(function(t,e){return e(n,t)},i)),l.concat(i)},new a.default):new a.default}function P(t,e,o){return N(o,t,!0)}function C(t,e){var o=s.default.Attributor.Attribute.keys(t),l=s.default.Attributor.Class.keys(t),n=s.default.Attributor.Style.keys(t),i={};return o.concat(l).concat(n).forEach(function(e){var o=s.default.query(e,s.default.Scope.ATTRIBUTE);null!=o&&(i[o.attrName]=o.value(t),i[o.attrName])||(null==(o=x[e])||o.attrName!==e&&o.keyName!==e||(i[o.attrName]=o.value(t)||void 0),null==(o=O[e])||o.attrName!==e&&o.keyName!==e||(o=O[e],i[o.attrName]=o.value(t)||void 0))}),Object.keys(i).length>0&&(e=N(e,i)),e}function L(t,e){var o=s.default.query(t);if(null==o)return e;if(o.prototype instanceof s.default.Embed){var l={},n=o.value(t);null!=n&&(l[o.blotName]=n,e=(new a.default).insert(l,o.formats(t)))}else"function"==typeof o.formats&&(e=N(e,o.blotName,o.formats(t)));return e}function M(t,e){return j(e,"\n")||(S(t)||e.length()>0&&t.nextSibling&&S(t.nextSibling))&&e.insert("\n"),e}function I(t,e){if(S(t)&&null!=t.nextElementSibling&&!j(e,"\n\n")){var o=t.offsetHeight+parseFloat(A(t).marginTop)+parseFloat(A(t).marginBottom);t.nextElementSibling.offsetTop>t.offsetTop*****o&&e.insert("\n")}return e}function R(t,e){var o=t.data;if("O:P"===t.parentNode.tagName)return e.insert(o.trim());if(0===o.trim().length&&t.parentNode.classList.contains("ql-clipboard"))return e;if(!A(t.parentNode).whiteSpace.startsWith("pre")){var l=function(t,e){return(e=e.replace(/[^\u00a0]/g,"")).length<1&&t?" ":e};o=(o=o.replace(/\r\n/g," ").replace(/\n/g," ")).replace(/\s\s+/g,l.bind(l,!0)),(null==t.previousSibling&&S(t.parentNode)||null!=t.previousSibling&&S(t.previousSibling))&&(o=o.replace(/^\s+/,l.bind(l,!1))),(null==t.nextSibling&&S(t.parentNode)||null!=t.nextSibling&&S(t.nextSibling))&&(o=o.replace(/\s+$/,l.bind(l,!1)))}return e.insert(o)}E.DEFAULTS={matchers:[],matchVisual:!0},e.default=E,e.matchAttributor=C,e.matchBlot=L,e.matchNewline=M,e.matchSpacing=I,e.matchText=R},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var l,n=function(){function t(t,e){for(var o=0;o<e.length;o++){var l=e[o];l.enumerable=l.enumerable||!1,l.configurable=!0,"value"in l&&(l.writable=!0),Object.defineProperty(t,l.key,l)}}return function(e,o,l){return o&&t(e.prototype,o),l&&t(e,l),e}}(),i=function t(e,o,l){null===e&&(e=Function.prototype);var n=Object.getOwnPropertyDescriptor(e,o);if(void 0===n){var i=Object.getPrototypeOf(e);return null===i?void 0:t(i,o,l)}if("value"in n)return n.value;var r=n.get;return void 0!==r?r.call(l):void 0},r=o(6),a=function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(e.__proto__||Object.getPrototypeOf(e)).apply(this,arguments))}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),n(e,[{key:"optimize",value:function(t){i(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"optimize",this).call(this,t),this.domNode.tagName!==this.statics.tagName[0]&&this.replaceWith(this.statics.blotName)}}],[{key:"create",value:function(){return i(e.__proto__||Object.getPrototypeOf(e),"create",this).call(this)}},{key:"formats",value:function(){return!0}}]),e}(((l=r)&&l.__esModule?l:{default:l}).default);a.blotName="bold",a.tagName=["STRONG","B"],e.default=a},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.addControls=e.default=void 0;var l=function(t,e){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return function(t,e){var o=[],l=!0,n=!1,i=void 0;try{for(var r,a=t[Symbol.iterator]();!(l=(r=a.next()).done)&&(o.push(r.value),!e||o.length!==e);l=!0);}catch(t){n=!0,i=t}finally{try{!l&&a.return&&a.return()}finally{if(n)throw i}}return o}(t,e);throw new TypeError("Invalid attempt to destructure non-iterable instance")},n=function(){function t(t,e){for(var o=0;o<e.length;o++){var l=e[o];l.enumerable=l.enumerable||!1,l.configurable=!0,"value"in l&&(l.writable=!0),Object.defineProperty(t,l.key,l)}}return function(e,o,l){return o&&t(e.prototype,o),l&&t(e,l),e}}(),i=u(o(2)),r=u(o(0)),a=u(o(5)),s=u(o(10)),c=u(o(9));function u(t){return t&&t.__esModule?t:{default:t}}function d(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}var f=(0,s.default)("quill:toolbar"),p=function(t){function e(t,o){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);var n,i=d(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,t,o));if(Array.isArray(i.options.container)){var r=document.createElement("div");b(r,i.options.container),t.container.parentNode.insertBefore(r,t.container),i.container=r}else"string"==typeof i.options.container?i.container=document.querySelector(i.options.container):i.container=i.options.container;return i.container instanceof HTMLElement?(i.container.classList.add("ql-toolbar"),i.controls=[],i.handlers={},Object.keys(i.options.handlers).forEach(function(t){i.addHandler(t,i.options.handlers[t])}),[].forEach.call(i.container.querySelectorAll("button, select"),function(t){i.attach(t)}),i.quill.on(a.default.events.EDITOR_CHANGE,function(t,e){t===a.default.events.SELECTION_CHANGE&&i.update(e)}),i.quill.on(a.default.events.SCROLL_OPTIMIZE,function(){var t=i.quill.selection.getRange(),e=l(t,1)[0];i.update(e)}),i):(n=f.error("Container required for toolbar",i.options),d(i,n))}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),n(e,[{key:"addHandler",value:function(t,e){this.handlers[t]=e}},{key:"attach",value:function(t){var e=this,o=[].find.call(t.classList,function(t){return 0===t.indexOf("ql-")});if(o){if(o=o.slice(3),"BUTTON"===t.tagName&&t.setAttribute("type","button"),null==this.handlers[o]){if(null!=this.quill.scroll.whitelist&&null==this.quill.scroll.whitelist[o])return void f.warn("ignoring attaching to disabled format",o,t);if(null==r.default.query(o))return void f.warn("ignoring attaching to nonexistent format",o,t)}var n="SELECT"===t.tagName?"change":"click";t.addEventListener(n,function(n){var s=void 0;if("SELECT"===t.tagName){if(t.selectedIndex<0)return;var c=t.options[t.selectedIndex];s=!c.hasAttribute("selected")&&(c.value||!1)}else s=!t.classList.contains("ql-active")&&(t.value||!t.hasAttribute("value")),n.preventDefault();e.quill.focus();var u=e.quill.selection.getRange(),d=l(u,1)[0];if(null!=e.handlers[o])e.handlers[o].call(e,s);else if(r.default.query(o).prototype instanceof r.default.Embed){if(!(s=prompt("Enter "+o)))return;e.quill.updateContents((new i.default).retain(d.index).delete(d.length).insert(function(t,e,o){return e in t?Object.defineProperty(t,e,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[e]=o,t}({},o,s)),a.default.sources.USER)}else e.quill.format(o,s,a.default.sources.USER);e.update(d)}),this.controls.push([o,t])}}},{key:"update",value:function(t){var e=null==t?{}:this.quill.getFormat(t);this.controls.forEach(function(o){var n=l(o,2),i=n[0],r=n[1];if("SELECT"===r.tagName){var a=void 0;if(null==t)a=null;else if(null==e[i])a=r.querySelector("option[selected]");else if(!Array.isArray(e[i])){var s=e[i];"string"==typeof s&&(s=s.replace(/\"/g,'\\"')),a=r.querySelector('option[value="'+s+'"]')}null==a?(r.value="",r.selectedIndex=-1):a.selected=!0}else if(null==t)r.classList.remove("ql-active");else if(r.hasAttribute("value")){var c=e[i]===r.getAttribute("value")||null!=e[i]&&e[i].toString()===r.getAttribute("value")||null==e[i]&&!r.getAttribute("value");r.classList.toggle("ql-active",c)}else r.classList.toggle("ql-active",null!=e[i])})}}]),e}(c.default);function h(t,e,o){var l=document.createElement("button");l.setAttribute("type","button"),l.classList.add("ql-"+e),null!=o&&(l.value=o),t.appendChild(l)}function b(t,e){Array.isArray(e[0])||(e=[e]),e.forEach(function(e){var o=document.createElement("span");o.classList.add("ql-formats"),e.forEach(function(t){if("string"==typeof t)h(o,t);else{var e=Object.keys(t)[0],l=t[e];Array.isArray(l)?function(t,e,o){var l=document.createElement("select");l.classList.add("ql-"+e),o.forEach(function(t){var e=document.createElement("option");!1!==t?e.setAttribute("value",t):e.setAttribute("selected","selected"),l.appendChild(e)}),t.appendChild(l)}(o,e,l):h(o,e,l)}}),t.appendChild(o)})}p.DEFAULTS={},p.DEFAULTS={container:null,handlers:{clean:function(){var t=this,e=this.quill.getSelection();if(null!=e)if(0==e.length){var o=this.quill.getFormat();Object.keys(o).forEach(function(e){null!=r.default.query(e,r.default.Scope.INLINE)&&t.quill.format(e,!1)})}else this.quill.removeFormat(e,a.default.sources.USER)},direction:function(t){var e=this.quill.getFormat().align;"rtl"===t&&null==e?this.quill.format("align","right",a.default.sources.USER):t||"right"!==e||this.quill.format("align",!1,a.default.sources.USER),this.quill.format("direction",t,a.default.sources.USER)},indent:function(t){var e=this.quill.getSelection(),o=this.quill.getFormat(e),l=parseInt(o.indent||0);if("+1"===t||"-1"===t){var n="+1"===t?1:-1;"rtl"===o.direction&&(n*=-1),this.quill.format("indent",l+n,a.default.sources.USER)}},link:function(t){!0===t&&(t=prompt("Enter link URL:")),this.quill.format("link",t,a.default.sources.USER)},list:function(t){var e=this.quill.getSelection(),o=this.quill.getFormat(e);"check"===t?"checked"===o.list||"unchecked"===o.list?this.quill.format("list",!1,a.default.sources.USER):this.quill.format("list","unchecked",a.default.sources.USER):this.quill.format("list",t,a.default.sources.USER)}}},e.default=p,e.addControls=b},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <polyline class="ql-even ql-stroke" points="5 7 3 9 5 11"></polyline> <polyline class="ql-even ql-stroke" points="13 7 15 9 13 11"></polyline> <line class=ql-stroke x1=10 x2=8 y1=5 y2=13></line> </svg>'},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var l,n=function(){function t(t,e){for(var o=0;o<e.length;o++){var l=e[o];l.enumerable=l.enumerable||!1,l.configurable=!0,"value"in l&&(l.writable=!0),Object.defineProperty(t,l.key,l)}}return function(e,o,l){return o&&t(e.prototype,o),l&&t(e,l),e}}(),i=function t(e,o,l){null===e&&(e=Function.prototype);var n=Object.getOwnPropertyDescriptor(e,o);if(void 0===n){var i=Object.getPrototypeOf(e);return null===i?void 0:t(i,o,l)}if("value"in n)return n.value;var r=n.get;return void 0!==r?r.call(l):void 0},r=o(28),a=function(t){function e(t,o){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);var l=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,t));return l.label.innerHTML=o,l.container.classList.add("ql-color-picker"),[].slice.call(l.container.querySelectorAll(".ql-picker-item"),0,7).forEach(function(t){t.classList.add("ql-primary")}),l}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),n(e,[{key:"buildItem",value:function(t){var o=i(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"buildItem",this).call(this,t);return o.style.backgroundColor=t.getAttribute("value")||"",o}},{key:"selectItem",value:function(t,o){i(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"selectItem",this).call(this,t,o);var l=this.label.querySelector(".ql-color-label"),n=t&&t.getAttribute("data-value")||"";l&&("line"===l.tagName?l.style.stroke=n:l.style.fill=n)}}]),e}(((l=r)&&l.__esModule?l:{default:l}).default);e.default=a},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var l,n=function(){function t(t,e){for(var o=0;o<e.length;o++){var l=e[o];l.enumerable=l.enumerable||!1,l.configurable=!0,"value"in l&&(l.writable=!0),Object.defineProperty(t,l.key,l)}}return function(e,o,l){return o&&t(e.prototype,o),l&&t(e,l),e}}(),i=function t(e,o,l){null===e&&(e=Function.prototype);var n=Object.getOwnPropertyDescriptor(e,o);if(void 0===n){var i=Object.getPrototypeOf(e);return null===i?void 0:t(i,o,l)}if("value"in n)return n.value;var r=n.get;return void 0!==r?r.call(l):void 0},r=o(28),a=function(t){function e(t,o){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);var l=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,t));return l.container.classList.add("ql-icon-picker"),[].forEach.call(l.container.querySelectorAll(".ql-picker-item"),function(t){t.innerHTML=o[t.getAttribute("data-value")||""]}),l.defaultItem=l.container.querySelector(".ql-selected"),l.selectItem(l.defaultItem),l}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),n(e,[{key:"selectItem",value:function(t,o){i(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"selectItem",this).call(this,t,o),t=t||this.defaultItem,this.label.innerHTML=t.innerHTML}}]),e}(((l=r)&&l.__esModule?l:{default:l}).default);e.default=a},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var l=function(){function t(t,e){for(var o=0;o<e.length;o++){var l=e[o];l.enumerable=l.enumerable||!1,l.configurable=!0,"value"in l&&(l.writable=!0),Object.defineProperty(t,l.key,l)}}return function(e,o,l){return o&&t(e.prototype,o),l&&t(e,l),e}}(),n=function(){function t(e,o){var l=this;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.quill=e,this.boundsContainer=o||document.body,this.root=e.addContainer("ql-tooltip"),this.root.innerHTML=this.constructor.TEMPLATE,this.quill.root===this.quill.scrollingContainer&&this.quill.root.addEventListener("scroll",function(){l.root.style.marginTop=-1*l.quill.root.scrollTop+"px"}),this.hide()}return l(t,[{key:"hide",value:function(){this.root.classList.add("ql-hidden")}},{key:"position",value:function(t){var e=t.left+t.width/2-this.root.offsetWidth/2,o=t.bottom+this.quill.root.scrollTop;this.root.style.left=e+"px",this.root.style.top=o+"px",this.root.classList.remove("ql-flip");var l=this.boundsContainer.getBoundingClientRect(),n=this.root.getBoundingClientRect(),i=0;if(n.right>l.right&&(i=l.right-n.right,this.root.style.left=e+i+"px"),n.left<l.left&&(i=l.left-n.left,this.root.style.left=e+i+"px"),n.bottom>l.bottom){var r=n.bottom-n.top,a=t.bottom-t.top+r;this.root.style.top=o-a+"px",this.root.classList.add("ql-flip")}return i}},{key:"show",value:function(){this.root.classList.remove("ql-editing"),this.root.classList.remove("ql-hidden")}}]),t}();e.default=n},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var l=function(t,e){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return function(t,e){var o=[],l=!0,n=!1,i=void 0;try{for(var r,a=t[Symbol.iterator]();!(l=(r=a.next()).done)&&(o.push(r.value),!e||o.length!==e);l=!0);}catch(t){n=!0,i=t}finally{try{!l&&a.return&&a.return()}finally{if(n)throw i}}return o}(t,e);throw new TypeError("Invalid attempt to destructure non-iterable instance")},n=function t(e,o,l){null===e&&(e=Function.prototype);var n=Object.getOwnPropertyDescriptor(e,o);if(void 0===n){var i=Object.getPrototypeOf(e);return null===i?void 0:t(i,o,l)}if("value"in n)return n.value;var r=n.get;return void 0!==r?r.call(l):void 0},i=function(){function t(t,e){for(var o=0;o<e.length;o++){var l=e[o];l.enumerable=l.enumerable||!1,l.configurable=!0,"value"in l&&(l.writable=!0),Object.defineProperty(t,l.key,l)}}return function(e,o,l){return o&&t(e.prototype,o),l&&t(e,l),e}}(),r=p(o(3)),a=p(o(8)),s=o(43),c=p(s),u=p(o(27)),d=o(15),f=p(o(41));function p(t){return t&&t.__esModule?t:{default:t}}function h(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function b(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}function q(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var y=[[{header:["1","2","3",!1]}],["bold","italic","underline","link"],[{list:"ordered"},{list:"bullet"}],["clean"]],v=function(t){function e(t,o){h(this,e),null!=o.modules.toolbar&&null==o.modules.toolbar.container&&(o.modules.toolbar.container=y);var l=b(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,t,o));return l.quill.container.classList.add("ql-snow"),l}return q(e,t),i(e,[{key:"extendToolbar",value:function(t){t.container.classList.add("ql-snow"),this.buildButtons([].slice.call(t.container.querySelectorAll("button")),f.default),this.buildPickers([].slice.call(t.container.querySelectorAll("select")),f.default),this.tooltip=new m(this.quill,this.options.bounds),t.container.querySelector(".ql-link")&&this.quill.keyboard.addBinding({key:"K",shortKey:!0},function(e,o){t.handlers.link.call(t,!o.format.link)})}}]),e}(c.default);v.DEFAULTS=(0,r.default)(!0,{},c.default.DEFAULTS,{modules:{toolbar:{handlers:{link:function(t){if(t){var e=this.quill.getSelection();if(null==e||0==e.length)return;var o=this.quill.getText(e);/^\S+@\S+\.\S+$/.test(o)&&0!==o.indexOf("mailto:")&&(o="mailto:"+o),this.quill.theme.tooltip.edit("link",o)}else this.quill.format("link",!1)}}}}});var m=function(t){function e(t,o){h(this,e);var l=b(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,t,o));return l.preview=l.root.querySelector("a.ql-preview"),l}return q(e,t),i(e,[{key:"listen",value:function(){var t=this;n(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"listen",this).call(this),this.root.querySelector("a.ql-action").addEventListener("click",function(e){t.root.classList.contains("ql-editing")?t.save():t.edit("link",t.preview.textContent),e.preventDefault()}),this.root.querySelector("a.ql-remove").addEventListener("click",function(e){if(null!=t.linkRange){var o=t.linkRange;t.restoreFocus(),t.quill.formatText(o,"link",!1,a.default.sources.USER),delete t.linkRange}e.preventDefault(),t.hide()}),this.quill.on(a.default.events.SELECTION_CHANGE,function(e,o,n){if(null!=e){if(0===e.length&&n===a.default.sources.USER){var i=t.quill.scroll.descendant(u.default,e.index),r=l(i,2),s=r[0],c=r[1];if(null!=s){t.linkRange=new d.Range(e.index-c,s.length());var f=u.default.formats(s.domNode);return t.preview.textContent=f,t.preview.setAttribute("href",f),t.show(),void t.position(t.quill.getBounds(t.linkRange))}}else delete t.linkRange;t.hide()}})}},{key:"show",value:function(){n(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"show",this).call(this),this.root.removeAttribute("data-mode")}}]),e}(s.BaseTooltip);m.TEMPLATE=['<a class="ql-preview" rel="noopener noreferrer" target="_blank" href="about:blank"></a>','<input type="text" data-formula="e=mc^2" data-link="https://quilljs.com" data-video="Embed URL">','<a class="ql-action"></a>','<a class="ql-remove"></a>'].join(""),e.default=v},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var l=M(o(29)),n=o(36),i=o(38),r=o(64),a=M(o(65)),s=M(o(66)),c=o(67),u=M(c),d=o(37),f=o(26),p=o(39),h=o(40),b=M(o(56)),q=M(o(68)),y=M(o(27)),v=M(o(69)),m=M(o(70)),g=M(o(71)),k=M(o(72)),_=M(o(73)),w=o(13),x=M(w),O=M(o(74)),E=M(o(75)),N=M(o(57)),A=M(o(41)),j=M(o(28)),S=M(o(59)),T=M(o(60)),P=M(o(61)),C=M(o(108)),L=M(o(62));function M(t){return t&&t.__esModule?t:{default:t}}l.default.register({"attributors/attribute/direction":i.DirectionAttribute,"attributors/class/align":n.AlignClass,"attributors/class/background":d.BackgroundClass,"attributors/class/color":f.ColorClass,"attributors/class/direction":i.DirectionClass,"attributors/class/font":p.FontClass,"attributors/class/size":h.SizeClass,"attributors/style/align":n.AlignStyle,"attributors/style/background":d.BackgroundStyle,"attributors/style/color":f.ColorStyle,"attributors/style/direction":i.DirectionStyle,"attributors/style/font":p.FontStyle,"attributors/style/size":h.SizeStyle},!0),l.default.register({"formats/align":n.AlignClass,"formats/direction":i.DirectionClass,"formats/indent":r.IndentClass,"formats/background":d.BackgroundStyle,"formats/color":f.ColorStyle,"formats/font":p.FontClass,"formats/size":h.SizeClass,"formats/blockquote":a.default,"formats/code-block":x.default,"formats/header":s.default,"formats/list":u.default,"formats/bold":b.default,"formats/code":w.Code,"formats/italic":q.default,"formats/link":y.default,"formats/script":v.default,"formats/strike":m.default,"formats/underline":g.default,"formats/image":k.default,"formats/video":_.default,"formats/list/item":c.ListItem,"modules/formula":O.default,"modules/syntax":E.default,"modules/toolbar":N.default,"themes/bubble":C.default,"themes/snow":L.default,"ui/icons":A.default,"ui/picker":j.default,"ui/icon-picker":T.default,"ui/color-picker":S.default,"ui/tooltip":P.default},!0),e.default=l.default},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.IndentClass=void 0;var l,n=function(){function t(t,e){for(var o=0;o<e.length;o++){var l=e[o];l.enumerable=l.enumerable||!1,l.configurable=!0,"value"in l&&(l.writable=!0),Object.defineProperty(t,l.key,l)}}return function(e,o,l){return o&&t(e.prototype,o),l&&t(e,l),e}}(),i=function t(e,o,l){null===e&&(e=Function.prototype);var n=Object.getOwnPropertyDescriptor(e,o);if(void 0===n){var i=Object.getPrototypeOf(e);return null===i?void 0:t(i,o,l)}if("value"in n)return n.value;var r=n.get;return void 0!==r?r.call(l):void 0},r=o(0),a=(l=r)&&l.__esModule?l:{default:l},s=function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(e.__proto__||Object.getPrototypeOf(e)).apply(this,arguments))}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),n(e,[{key:"add",value:function(t,o){if("+1"===o||"-1"===o){var l=this.value(t)||0;o="+1"===o?l+1:l-1}return 0===o?(this.remove(t),!0):i(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"add",this).call(this,t,o)}},{key:"canAdd",value:function(t,o){return i(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"canAdd",this).call(this,t,o)||i(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"canAdd",this).call(this,t,parseInt(o))}},{key:"value",value:function(t){return parseInt(i(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"value",this).call(this,t))||void 0}}]),e}(a.default.Attributor.Class),c=new s("indent","ql-indent",{scope:a.default.Scope.BLOCK,whitelist:[1,2,3,4,5,6,7,8]});e.IndentClass=c},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var l,n=o(4),i=function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(e.__proto__||Object.getPrototypeOf(e)).apply(this,arguments))}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),e}(((l=n)&&l.__esModule?l:{default:l}).default);i.blotName="blockquote",i.tagName="blockquote",e.default=i},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var l,n=function(){function t(t,e){for(var o=0;o<e.length;o++){var l=e[o];l.enumerable=l.enumerable||!1,l.configurable=!0,"value"in l&&(l.writable=!0),Object.defineProperty(t,l.key,l)}}return function(e,o,l){return o&&t(e.prototype,o),l&&t(e,l),e}}(),i=o(4),r=function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(e.__proto__||Object.getPrototypeOf(e)).apply(this,arguments))}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),n(e,null,[{key:"formats",value:function(t){return this.tagName.indexOf(t.tagName)+1}}]),e}(((l=i)&&l.__esModule?l:{default:l}).default);r.blotName="header",r.tagName=["H1","H2","H3","H4","H5","H6"],e.default=r},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=e.ListItem=void 0;var l=function(){function t(t,e){for(var o=0;o<e.length;o++){var l=e[o];l.enumerable=l.enumerable||!1,l.configurable=!0,"value"in l&&(l.writable=!0),Object.defineProperty(t,l.key,l)}}return function(e,o,l){return o&&t(e.prototype,o),l&&t(e,l),e}}(),n=function t(e,o,l){null===e&&(e=Function.prototype);var n=Object.getOwnPropertyDescriptor(e,o);if(void 0===n){var i=Object.getPrototypeOf(e);return null===i?void 0:t(i,o,l)}if("value"in n)return n.value;var r=n.get;return void 0!==r?r.call(l):void 0},i=s(o(0)),r=s(o(4)),a=s(o(25));function s(t){return t&&t.__esModule?t:{default:t}}function c(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function u(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}function d(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var f=function(t){function e(){return c(this,e),u(this,(e.__proto__||Object.getPrototypeOf(e)).apply(this,arguments))}return d(e,t),l(e,[{key:"format",value:function(t,o){t!==p.blotName||o?n(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"format",this).call(this,t,o):this.replaceWith(i.default.create(this.statics.scope))}},{key:"remove",value:function(){null==this.prev&&null==this.next?this.parent.remove():n(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"remove",this).call(this)}},{key:"replaceWith",value:function(t,o){return this.parent.isolate(this.offset(this.parent),this.length()),t===this.parent.statics.blotName?(this.parent.replaceWith(t,o),this):(this.parent.unwrap(),n(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"replaceWith",this).call(this,t,o))}}],[{key:"formats",value:function(t){return t.tagName===this.tagName?void 0:n(e.__proto__||Object.getPrototypeOf(e),"formats",this).call(this,t)}}]),e}(r.default);f.blotName="list-item",f.tagName="LI";var p=function(t){function e(t){c(this,e);var o=u(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,t)),l=function(e){if(e.target.parentNode===t){var l=o.statics.formats(t),n=i.default.find(e.target);"checked"===l?n.format("list","unchecked"):"unchecked"===l&&n.format("list","checked")}};return t.addEventListener("touchstart",l),t.addEventListener("mousedown",l),o}return d(e,t),l(e,null,[{key:"create",value:function(t){var o="ordered"===t?"OL":"UL",l=n(e.__proto__||Object.getPrototypeOf(e),"create",this).call(this,o);return"checked"!==t&&"unchecked"!==t||l.setAttribute("data-checked","checked"===t),l}},{key:"formats",value:function(t){return"OL"===t.tagName?"ordered":"UL"===t.tagName?t.hasAttribute("data-checked")?"true"===t.getAttribute("data-checked")?"checked":"unchecked":"bullet":void 0}}]),l(e,[{key:"format",value:function(t,e){this.children.length>0&&this.children.tail.format(t,e)}},{key:"formats",value:function(){return t={},e=this.statics.blotName,o=this.statics.formats(this.domNode),e in t?Object.defineProperty(t,e,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[e]=o,t;var t,e,o}},{key:"insertBefore",value:function(t,o){if(t instanceof f)n(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"insertBefore",this).call(this,t,o);else{var l=null==o?this.length():o.offset(this),i=this.split(l);i.parent.insertBefore(t,i)}}},{key:"optimize",value:function(t){n(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"optimize",this).call(this,t);var o=this.next;null!=o&&o.prev===this&&o.statics.blotName===this.statics.blotName&&o.domNode.tagName===this.domNode.tagName&&o.domNode.getAttribute("data-checked")===this.domNode.getAttribute("data-checked")&&(o.moveChildren(this),o.remove())}},{key:"replace",value:function(t){if(t.statics.blotName!==this.statics.blotName){var o=i.default.create(this.statics.defaultChild);t.moveChildren(o),this.appendChild(o)}n(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"replace",this).call(this,t)}}]),e}(a.default);p.blotName="list",p.scope=i.default.Scope.BLOCK_BLOT,p.tagName=["OL","UL"],p.defaultChild="list-item",p.allowedChildren=[f],e.ListItem=f,e.default=p},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var l,n=o(56),i=function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(e.__proto__||Object.getPrototypeOf(e)).apply(this,arguments))}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),e}(((l=n)&&l.__esModule?l:{default:l}).default);i.blotName="italic",i.tagName=["EM","I"],e.default=i},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var l,n=function(){function t(t,e){for(var o=0;o<e.length;o++){var l=e[o];l.enumerable=l.enumerable||!1,l.configurable=!0,"value"in l&&(l.writable=!0),Object.defineProperty(t,l.key,l)}}return function(e,o,l){return o&&t(e.prototype,o),l&&t(e,l),e}}(),i=function t(e,o,l){null===e&&(e=Function.prototype);var n=Object.getOwnPropertyDescriptor(e,o);if(void 0===n){var i=Object.getPrototypeOf(e);return null===i?void 0:t(i,o,l)}if("value"in n)return n.value;var r=n.get;return void 0!==r?r.call(l):void 0},r=o(6),a=function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(e.__proto__||Object.getPrototypeOf(e)).apply(this,arguments))}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),n(e,null,[{key:"create",value:function(t){return"super"===t?document.createElement("sup"):"sub"===t?document.createElement("sub"):i(e.__proto__||Object.getPrototypeOf(e),"create",this).call(this,t)}},{key:"formats",value:function(t){return"SUB"===t.tagName?"sub":"SUP"===t.tagName?"super":void 0}}]),e}(((l=r)&&l.__esModule?l:{default:l}).default);a.blotName="script",a.tagName=["SUB","SUP"],e.default=a},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var l,n=o(6),i=function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(e.__proto__||Object.getPrototypeOf(e)).apply(this,arguments))}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),e}(((l=n)&&l.__esModule?l:{default:l}).default);i.blotName="strike",i.tagName="S",e.default=i},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var l,n=o(6),i=function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(e.__proto__||Object.getPrototypeOf(e)).apply(this,arguments))}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),e}(((l=n)&&l.__esModule?l:{default:l}).default);i.blotName="underline",i.tagName="U",e.default=i},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var l,n=function(){function t(t,e){for(var o=0;o<e.length;o++){var l=e[o];l.enumerable=l.enumerable||!1,l.configurable=!0,"value"in l&&(l.writable=!0),Object.defineProperty(t,l.key,l)}}return function(e,o,l){return o&&t(e.prototype,o),l&&t(e,l),e}}(),i=function t(e,o,l){null===e&&(e=Function.prototype);var n=Object.getOwnPropertyDescriptor(e,o);if(void 0===n){var i=Object.getPrototypeOf(e);return null===i?void 0:t(i,o,l)}if("value"in n)return n.value;var r=n.get;return void 0!==r?r.call(l):void 0},r=o(0),a=(l=r)&&l.__esModule?l:{default:l},s=o(27),c=["alt","height","width"],u=function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(e.__proto__||Object.getPrototypeOf(e)).apply(this,arguments))}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),n(e,[{key:"format",value:function(t,o){c.indexOf(t)>-1?o?this.domNode.setAttribute(t,o):this.domNode.removeAttribute(t):i(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"format",this).call(this,t,o)}}],[{key:"create",value:function(t){var o=i(e.__proto__||Object.getPrototypeOf(e),"create",this).call(this,t);return"string"==typeof t&&o.setAttribute("src",this.sanitize(t)),o}},{key:"formats",value:function(t){return c.reduce(function(e,o){return t.hasAttribute(o)&&(e[o]=t.getAttribute(o)),e},{})}},{key:"match",value:function(t){return/\.(jpe?g|gif|png)$/.test(t)||/^data:image\/.+;base64/.test(t)}},{key:"sanitize",value:function(t){return(0,s.sanitize)(t,["http","https","data"])?t:"//:0"}},{key:"value",value:function(t){return t.getAttribute("src")}}]),e}(a.default.Embed);u.blotName="image",u.tagName="IMG",e.default=u},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var l,n=function(){function t(t,e){for(var o=0;o<e.length;o++){var l=e[o];l.enumerable=l.enumerable||!1,l.configurable=!0,"value"in l&&(l.writable=!0),Object.defineProperty(t,l.key,l)}}return function(e,o,l){return o&&t(e.prototype,o),l&&t(e,l),e}}(),i=function t(e,o,l){null===e&&(e=Function.prototype);var n=Object.getOwnPropertyDescriptor(e,o);if(void 0===n){var i=Object.getPrototypeOf(e);return null===i?void 0:t(i,o,l)}if("value"in n)return n.value;var r=n.get;return void 0!==r?r.call(l):void 0},r=o(4),a=o(27),s=(l=a)&&l.__esModule?l:{default:l},c=["height","width"],u=function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(e.__proto__||Object.getPrototypeOf(e)).apply(this,arguments))}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),n(e,[{key:"format",value:function(t,o){c.indexOf(t)>-1?o?this.domNode.setAttribute(t,o):this.domNode.removeAttribute(t):i(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"format",this).call(this,t,o)}}],[{key:"create",value:function(t){var o=i(e.__proto__||Object.getPrototypeOf(e),"create",this).call(this,t);return o.setAttribute("frameborder","0"),o.setAttribute("allowfullscreen",!0),o.setAttribute("src",this.sanitize(t)),o}},{key:"formats",value:function(t){return c.reduce(function(e,o){return t.hasAttribute(o)&&(e[o]=t.getAttribute(o)),e},{})}},{key:"sanitize",value:function(t){return s.default.sanitize(t)}},{key:"value",value:function(t){return t.getAttribute("src")}}]),e}(r.BlockEmbed);u.blotName="video",u.className="ql-video",u.tagName="IFRAME",e.default=u},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=e.FormulaBlot=void 0;var l=function(){function t(t,e){for(var o=0;o<e.length;o++){var l=e[o];l.enumerable=l.enumerable||!1,l.configurable=!0,"value"in l&&(l.writable=!0),Object.defineProperty(t,l.key,l)}}return function(e,o,l){return o&&t(e.prototype,o),l&&t(e,l),e}}(),n=function t(e,o,l){null===e&&(e=Function.prototype);var n=Object.getOwnPropertyDescriptor(e,o);if(void 0===n){var i=Object.getPrototypeOf(e);return null===i?void 0:t(i,o,l)}if("value"in n)return n.value;var r=n.get;return void 0!==r?r.call(l):void 0},i=s(o(35)),r=s(o(5)),a=s(o(9));function s(t){return t&&t.__esModule?t:{default:t}}function c(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function u(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}function d(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var f=function(t){function e(){return c(this,e),u(this,(e.__proto__||Object.getPrototypeOf(e)).apply(this,arguments))}return d(e,t),l(e,null,[{key:"create",value:function(t){var o=n(e.__proto__||Object.getPrototypeOf(e),"create",this).call(this,t);return"string"==typeof t&&(window.katex.render(t,o,{throwOnError:!1,errorColor:"#f00"}),o.setAttribute("data-value",t)),o}},{key:"value",value:function(t){return t.getAttribute("data-value")}}]),e}(i.default);f.blotName="formula",f.className="ql-formula",f.tagName="SPAN";var p=function(t){function e(){c(this,e);var t=u(this,(e.__proto__||Object.getPrototypeOf(e)).call(this));if(null==window.katex)throw new Error("Formula module requires KaTeX.");return t}return d(e,t),l(e,null,[{key:"register",value:function(){r.default.register(f,!0)}}]),e}(a.default);e.FormulaBlot=f,e.default=p},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=e.CodeToken=e.CodeBlock=void 0;var l=function(){function t(t,e){for(var o=0;o<e.length;o++){var l=e[o];l.enumerable=l.enumerable||!1,l.configurable=!0,"value"in l&&(l.writable=!0),Object.defineProperty(t,l.key,l)}}return function(e,o,l){return o&&t(e.prototype,o),l&&t(e,l),e}}(),n=function t(e,o,l){null===e&&(e=Function.prototype);var n=Object.getOwnPropertyDescriptor(e,o);if(void 0===n){var i=Object.getPrototypeOf(e);return null===i?void 0:t(i,o,l)}if("value"in n)return n.value;var r=n.get;return void 0!==r?r.call(l):void 0},i=s(o(0)),r=s(o(5)),a=s(o(9));function s(t){return t&&t.__esModule?t:{default:t}}function c(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function u(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}function d(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var f=function(t){function e(){return c(this,e),u(this,(e.__proto__||Object.getPrototypeOf(e)).apply(this,arguments))}return d(e,t),l(e,[{key:"replaceWith",value:function(t){this.domNode.textContent=this.domNode.textContent,this.attach(),n(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"replaceWith",this).call(this,t)}},{key:"highlight",value:function(t){var e=this.domNode.textContent;this.cachedText!==e&&((e.trim().length>0||null==this.cachedText)&&(this.domNode.innerHTML=t(e),this.domNode.normalize(),this.attach()),this.cachedText=e)}}]),e}(s(o(13)).default);f.className="ql-syntax";var p=new i.default.Attributor.Class("token","hljs",{scope:i.default.Scope.INLINE}),h=function(t){function e(t,o){c(this,e);var l=u(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,t,o));if("function"!=typeof l.options.highlight)throw new Error("Syntax module requires highlight.js. Please include the library on the page before Quill.");var n=null;return l.quill.on(r.default.events.SCROLL_OPTIMIZE,function(){clearTimeout(n),n=setTimeout(function(){l.highlight(),n=null},l.options.interval)}),l.highlight(),l}return d(e,t),l(e,null,[{key:"register",value:function(){r.default.register(p,!0),r.default.register(f,!0)}}]),l(e,[{key:"highlight",value:function(){var t=this;if(!this.quill.selection.composing){this.quill.update(r.default.sources.USER);var e=this.quill.getSelection();this.quill.scroll.descendants(f).forEach(function(e){e.highlight(t.options.highlight)}),this.quill.update(r.default.sources.SILENT),null!=e&&this.quill.setSelection(e,r.default.sources.SILENT)}}}]),e}(a.default);h.DEFAULTS={highlight:null==window.hljs?null:function(t){return window.hljs.highlightAuto(t).value},interval:1e3},e.CodeBlock=f,e.CodeToken=p,e.default=h},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=3 x2=15 y1=9 y2=9></line> <line class=ql-stroke x1=3 x2=13 y1=14 y2=14></line> <line class=ql-stroke x1=3 x2=9 y1=4 y2=4></line> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=15 x2=3 y1=9 y2=9></line> <line class=ql-stroke x1=14 x2=4 y1=14 y2=14></line> <line class=ql-stroke x1=12 x2=6 y1=4 y2=4></line> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=15 x2=3 y1=9 y2=9></line> <line class=ql-stroke x1=15 x2=5 y1=14 y2=14></line> <line class=ql-stroke x1=15 x2=9 y1=4 y2=4></line> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=15 x2=3 y1=9 y2=9></line> <line class=ql-stroke x1=15 x2=3 y1=14 y2=14></line> <line class=ql-stroke x1=15 x2=3 y1=4 y2=4></line> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <g class="ql-fill ql-color-label"> <polygon points="6 6.868 6 6 5 6 5 7 5.942 7 6 6.868"></polygon> <rect height=1 width=1 x=4 y=4></rect> <polygon points="6.817 5 6 5 6 6 6.38 6 6.817 5"></polygon> <rect height=1 width=1 x=2 y=6></rect> <rect height=1 width=1 x=3 y=5></rect> <rect height=1 width=1 x=4 y=7></rect> <polygon points="4 11.439 4 11 3 11 3 12 3.755 12 4 11.439"></polygon> <rect height=1 width=1 x=2 y=12></rect> <rect height=1 width=1 x=2 y=9></rect> <rect height=1 width=1 x=2 y=15></rect> <polygon points="4.63 10 4 10 4 11 4.192 11 4.63 10"></polygon> <rect height=1 width=1 x=3 y=8></rect> <path d=M10.832,4.2L11,4.582V4H10.708A1.948,1.948,0,0,1,10.832,4.2Z></path> <path d=M7,4.582L7.168,4.2A1.929,1.929,0,0,1,7.292,4H7V4.582Z></path> <path d=M8,13H7.683l-0.351.8a1.933,1.933,0,0,1-.124.2H8V13Z></path> <rect height=1 width=1 x=12 y=2></rect> <rect height=1 width=1 x=11 y=3></rect> <path d=M9,3H8V3.282A1.985,1.985,0,0,1,9,3Z></path> <rect height=1 width=1 x=2 y=3></rect> <rect height=1 width=1 x=6 y=2></rect> <rect height=1 width=1 x=3 y=2></rect> <rect height=1 width=1 x=5 y=3></rect> <rect height=1 width=1 x=9 y=2></rect> <rect height=1 width=1 x=15 y=14></rect> <polygon points="13.447 10.174 13.469 10.225 13.472 10.232 13.808 11 14 11 14 10 13.37 10 13.447 10.174"></polygon> <rect height=1 width=1 x=13 y=7></rect> <rect height=1 width=1 x=15 y=5></rect> <rect height=1 width=1 x=14 y=6></rect> <rect height=1 width=1 x=15 y=8></rect> <rect height=1 width=1 x=14 y=9></rect> <path d=M3.775,14H3v1H4V14.314A1.97,1.97,0,0,1,3.775,14Z></path> <rect height=1 width=1 x=14 y=3></rect> <polygon points="12 6.868 12 6 11.62 6 12 6.868"></polygon> <rect height=1 width=1 x=15 y=2></rect> <rect height=1 width=1 x=12 y=5></rect> <rect height=1 width=1 x=13 y=4></rect> <polygon points="12.933 9 13 9 13 8 12.495 8 12.933 9"></polygon> <rect height=1 width=1 x=9 y=14></rect> <rect height=1 width=1 x=8 y=15></rect> <path d=M6,14.926V15H7V14.316A1.993,1.993,0,0,1,6,14.926Z></path> <rect height=1 width=1 x=5 y=15></rect> <path d=M10.668,13.8L10.317,13H10v1h0.792A1.947,1.947,0,0,1,10.668,13.8Z></path> <rect height=1 width=1 x=11 y=15></rect> <path d=M14.332,12.2a1.99,1.99,0,0,1,.166.8H15V12H14.245Z></path> <rect height=1 width=1 x=14 y=15></rect> <rect height=1 width=1 x=15 y=11></rect> </g> <polyline class=ql-stroke points="5.5 13 9 5 12.5 13"></polyline> <line class=ql-stroke x1=11.63 x2=6.38 y1=11 y2=11></line> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <rect class="ql-fill ql-stroke" height=3 width=3 x=4 y=5></rect> <rect class="ql-fill ql-stroke" height=3 width=3 x=11 y=5></rect> <path class="ql-even ql-fill ql-stroke" d=M7,8c0,4.031-3,5-3,5></path> <path class="ql-even ql-fill ql-stroke" d=M14,8c0,4.031-3,5-3,5></path> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <path class=ql-stroke d=M5,4H9.5A2.5,2.5,0,0,1,12,6.5v0A2.5,2.5,0,0,1,9.5,9H5A0,0,0,0,1,5,9V4A0,0,0,0,1,5,4Z></path> <path class=ql-stroke d=M5,9h5.5A2.5,2.5,0,0,1,13,11.5v0A2.5,2.5,0,0,1,10.5,14H5a0,0,0,0,1,0,0V9A0,0,0,0,1,5,9Z></path> </svg>'},function(t,e){t.exports='<svg class="" viewbox="0 0 18 18"> <line class=ql-stroke x1=5 x2=13 y1=3 y2=3></line> <line class=ql-stroke x1=6 x2=9.35 y1=12 y2=3></line> <line class=ql-stroke x1=11 x2=15 y1=11 y2=15></line> <line class=ql-stroke x1=15 x2=11 y1=11 y2=15></line> <rect class=ql-fill height=1 rx=0.5 ry=0.5 width=7 x=2 y=14></rect> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <line class="ql-color-label ql-stroke ql-transparent" x1=3 x2=15 y1=15 y2=15></line> <polyline class=ql-stroke points="5.5 11 9 3 12.5 11"></polyline> <line class=ql-stroke x1=11.63 x2=6.38 y1=9 y2=9></line> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <polygon class="ql-stroke ql-fill" points="3 11 5 9 3 7 3 11"></polygon> <line class="ql-stroke ql-fill" x1=15 x2=11 y1=4 y2=4></line> <path class=ql-fill d=M11,3a3,3,0,0,0,0,6h1V3H11Z></path> <rect class=ql-fill height=11 width=1 x=11 y=4></rect> <rect class=ql-fill height=11 width=1 x=13 y=4></rect> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <polygon class="ql-stroke ql-fill" points="15 12 13 10 15 8 15 12"></polygon> <line class="ql-stroke ql-fill" x1=9 x2=5 y1=4 y2=4></line> <path class=ql-fill d=M5,3A3,3,0,0,0,5,9H6V3H5Z></path> <rect class=ql-fill height=11 width=1 x=5 y=4></rect> <rect class=ql-fill height=11 width=1 x=7 y=4></rect> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M14,16H4a1,1,0,0,1,0-2H14A1,1,0,0,1,14,16Z /> <path class=ql-fill d=M14,4H4A1,1,0,0,1,4,2H14A1,1,0,0,1,14,4Z /> <rect class=ql-fill x=3 y=6 width=12 height=6 rx=1 ry=1 /> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M13,16H5a1,1,0,0,1,0-2h8A1,1,0,0,1,13,16Z /> <path class=ql-fill d=M13,4H5A1,1,0,0,1,5,2h8A1,1,0,0,1,13,4Z /> <rect class=ql-fill x=2 y=6 width=14 height=6 rx=1 ry=1 /> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M15,8H13a1,1,0,0,1,0-2h2A1,1,0,0,1,15,8Z /> <path class=ql-fill d=M15,12H13a1,1,0,0,1,0-2h2A1,1,0,0,1,15,12Z /> <path class=ql-fill d=M15,16H5a1,1,0,0,1,0-2H15A1,1,0,0,1,15,16Z /> <path class=ql-fill d=M15,4H5A1,1,0,0,1,5,2H15A1,1,0,0,1,15,4Z /> <rect class=ql-fill x=2 y=6 width=8 height=6 rx=1 ry=1 /> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M5,8H3A1,1,0,0,1,3,6H5A1,1,0,0,1,5,8Z /> <path class=ql-fill d=M5,12H3a1,1,0,0,1,0-2H5A1,1,0,0,1,5,12Z /> <path class=ql-fill d=M13,16H3a1,1,0,0,1,0-2H13A1,1,0,0,1,13,16Z /> <path class=ql-fill d=M13,4H3A1,1,0,0,1,3,2H13A1,1,0,0,1,13,4Z /> <rect class=ql-fill x=8 y=6 width=8 height=6 rx=1 ry=1 transform="translate(24 18) rotate(-180)"/> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M11.759,2.482a2.561,2.561,0,0,0-3.53.607A7.656,7.656,0,0,0,6.8,6.2C6.109,9.188,5.275,14.677,4.15,14.927a1.545,1.545,0,0,0-1.3-.933A0.922,0.922,0,0,0,2,15.036S1.954,16,4.119,16s3.091-2.691,3.7-5.553c0.177-.826.36-1.726,0.554-2.6L8.775,6.2c0.381-1.421.807-2.521,1.306-2.676a1.014,1.014,0,0,0,1.02.56A0.966,0.966,0,0,0,11.759,2.482Z></path> <rect class=ql-fill height=1.6 rx=0.8 ry=0.8 width=5 x=5.15 y=6.2></rect> <path class=ql-fill d=M13.663,12.027a1.662,1.662,0,0,1,.266-0.276q0.193,0.069.456,0.138a2.1,2.1,0,0,0,.535.069,1.075,1.075,0,0,0,.767-0.3,1.044,1.044,0,0,0,.314-0.8,0.84,0.84,0,0,0-.238-0.619,0.8,0.8,0,0,0-.594-0.239,1.154,1.154,0,0,0-.781.3,4.607,4.607,0,0,0-.781,1q-0.091.15-.218,0.346l-0.246.38c-0.068-.288-0.137-0.582-0.212-0.885-0.459-1.847-2.494-.984-2.941-0.8-0.482.2-.353,0.647-0.094,0.529a0.869,0.869,0,0,1,1.281.585c0.217,0.751.377,1.436,0.527,2.038a5.688,5.688,0,0,1-.362.467,2.69,2.69,0,0,1-.264.271q-0.221-.08-0.471-0.147a2.029,2.029,0,0,0-.522-0.066,1.079,1.079,0,0,0-.768.3A1.058,1.058,0,0,0,9,15.131a0.82,0.82,0,0,0,.832.852,1.134,1.134,0,0,0,.787-0.3,5.11,5.11,0,0,0,.776-0.993q0.141-.219.215-0.34c0.046-.076.122-0.194,0.223-0.346a2.786,2.786,0,0,0,.918,1.726,2.582,2.582,0,0,0,2.376-.185c0.317-.181.212-0.565,0-0.494A0.807,0.807,0,0,1,14.176,15a5.159,5.159,0,0,1-.913-2.446l0,0Q13.487,12.24,13.663,12.027Z></path> </svg>'},function(t,e){t.exports='<svg viewBox="0 0 18 18"> <path class=ql-fill d=M10,4V14a1,1,0,0,1-2,0V10H3v4a1,1,0,0,1-2,0V4A1,1,0,0,1,3,4V8H8V4a1,1,0,0,1,2,0Zm6.06787,9.209H14.98975V7.59863a.54085.54085,0,0,0-.605-.60547h-.62744a1.01119,1.01119,0,0,0-.748.29688L11.645,8.56641a.5435.5435,0,0,0-.022.8584l.28613.30762a.53861.53861,0,0,0,.84717.0332l.09912-.08789a1.2137,1.2137,0,0,0,.2417-.35254h.02246s-.01123.30859-.01123.60547V13.209H12.041a.54085.54085,0,0,0-.605.60547v.43945a.54085.54085,0,0,0,.605.60547h4.02686a.54085.54085,0,0,0,.605-.60547v-.43945A.54085.54085,0,0,0,16.06787,13.209Z /> </svg>'},function(t,e){t.exports='<svg viewBox="0 0 18 18"> <path class=ql-fill d=M16.73975,13.81445v.43945a.54085.54085,0,0,1-.605.60547H11.855a.58392.58392,0,0,1-.64893-.60547V14.0127c0-2.90527,3.39941-3.42187,3.39941-4.55469a.77675.77675,0,0,0-.84717-.78125,1.17684,1.17684,0,0,0-.83594.38477c-.2749.26367-.561.374-.85791.13184l-.4292-.34082c-.30811-.24219-.38525-.51758-.1543-.81445a2.97155,2.97155,0,0,1,2.45361-1.17676,2.45393,2.45393,0,0,1,2.68408,2.40918c0,2.45312-3.1792,2.92676-3.27832,3.93848h2.79443A.54085.54085,0,0,1,16.73975,13.81445ZM9,3A.99974.99974,0,0,0,8,4V8H3V4A1,1,0,0,0,1,4V14a1,1,0,0,0,2,0V10H8v4a1,1,0,0,0,2,0V4A.99974.99974,0,0,0,9,3Z /> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=7 x2=13 y1=4 y2=4></line> <line class=ql-stroke x1=5 x2=11 y1=14 y2=14></line> <line class=ql-stroke x1=8 x2=10 y1=14 y2=4></line> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <rect class=ql-stroke height=10 width=12 x=3 y=4></rect> <circle class=ql-fill cx=6 cy=7 r=1></circle> <polyline class="ql-even ql-fill" points="5 12 5 11 7 9 8 10 11 7 13 9 13 12 5 12"></polyline> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=3 x2=15 y1=14 y2=14></line> <line class=ql-stroke x1=3 x2=15 y1=4 y2=4></line> <line class=ql-stroke x1=9 x2=15 y1=9 y2=9></line> <polyline class="ql-fill ql-stroke" points="3 7 3 11 5 9 3 7"></polyline> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=3 x2=15 y1=14 y2=14></line> <line class=ql-stroke x1=3 x2=15 y1=4 y2=4></line> <line class=ql-stroke x1=9 x2=15 y1=9 y2=9></line> <polyline class=ql-stroke points="5 7 5 11 3 9 5 7"></polyline> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=7 x2=11 y1=7 y2=11></line> <path class="ql-even ql-stroke" d=M8.9,4.577a3.476,3.476,0,0,1,.36,4.679A3.476,3.476,0,0,1,4.577,8.9C3.185,7.5,2.035,6.4,4.217,4.217S7.5,3.185,8.9,4.577Z></path> <path class="ql-even ql-stroke" d=M13.423,9.1a3.476,3.476,0,0,0-4.679-.36,3.476,3.476,0,0,0,.36,4.679c1.392,1.392,2.5,2.542,4.679.36S14.815,10.5,13.423,9.1Z></path> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=7 x2=15 y1=4 y2=4></line> <line class=ql-stroke x1=7 x2=15 y1=9 y2=9></line> <line class=ql-stroke x1=7 x2=15 y1=14 y2=14></line> <line class="ql-stroke ql-thin" x1=2.5 x2=4.5 y1=5.5 y2=5.5></line> <path class=ql-fill d=M3.5,6A0.5,0.5,0,0,1,3,5.5V3.085l-0.276.138A0.5,0.5,0,0,1,2.053,3c-0.124-.247-0.023-0.324.224-0.447l1-.5A0.5,0.5,0,0,1,4,2.5v3A0.5,0.5,0,0,1,3.5,6Z></path> <path class="ql-stroke ql-thin" d=M4.5,10.5h-2c0-.234,1.85-1.076,1.85-2.234A0.959,0.959,0,0,0,2.5,8.156></path> <path class="ql-stroke ql-thin" d=M2.5,14.846a0.959,0.959,0,0,0,1.85-.109A0.7,0.7,0,0,0,3.75,14a0.688,0.688,0,0,0,.6-0.736,0.959,0.959,0,0,0-1.85-.109></path> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=6 x2=15 y1=4 y2=4></line> <line class=ql-stroke x1=6 x2=15 y1=9 y2=9></line> <line class=ql-stroke x1=6 x2=15 y1=14 y2=14></line> <line class=ql-stroke x1=3 x2=3 y1=4 y2=4></line> <line class=ql-stroke x1=3 x2=3 y1=9 y2=9></line> <line class=ql-stroke x1=3 x2=3 y1=14 y2=14></line> </svg>'},function(t,e){t.exports='<svg class="" viewbox="0 0 18 18"> <line class=ql-stroke x1=9 x2=15 y1=4 y2=4></line> <polyline class=ql-stroke points="3 4 4 5 6 3"></polyline> <line class=ql-stroke x1=9 x2=15 y1=14 y2=14></line> <polyline class=ql-stroke points="3 14 4 15 6 13"></polyline> <line class=ql-stroke x1=9 x2=15 y1=9 y2=9></line> <polyline class=ql-stroke points="3 9 4 10 6 8"></polyline> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M15.5,15H13.861a3.858,3.858,0,0,0,1.914-2.975,1.8,1.8,0,0,0-1.6-1.751A1.921,1.921,0,0,0,12.021,11.7a0.50013,0.50013,0,1,0,.957.291h0a0.914,0.914,0,0,1,1.053-.725,0.81,0.81,0,0,1,.744.762c0,1.076-1.16971,1.86982-1.93971,2.43082A1.45639,1.45639,0,0,0,12,15.5a0.5,0.5,0,0,0,.5.5h3A0.5,0.5,0,0,0,15.5,15Z /> <path class=ql-fill d=M9.65,5.241a1,1,0,0,0-1.409.108L6,7.964,3.759,5.349A1,1,0,0,0,2.192,6.59178Q2.21541,6.6213,2.241,6.649L4.684,9.5,2.241,12.35A1,1,0,0,0,3.71,13.70722q0.02557-.02768.049-0.05722L6,11.036,8.241,13.65a1,1,0,1,0,1.567-1.24277Q9.78459,12.3777,9.759,12.35L7.316,9.5,9.759,6.651A1,1,0,0,0,9.65,5.241Z /> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M15.5,7H13.861a4.015,4.015,0,0,0,1.914-2.975,1.8,1.8,0,0,0-1.6-1.751A1.922,1.922,0,0,0,12.021,3.7a0.5,0.5,0,1,0,.957.291,0.917,0.917,0,0,1,1.053-.725,0.81,0.81,0,0,1,.744.762c0,1.077-1.164,1.925-1.934,2.486A1.423,1.423,0,0,0,12,7.5a0.5,0.5,0,0,0,.5.5h3A0.5,0.5,0,0,0,15.5,7Z /> <path class=ql-fill d=M9.651,5.241a1,1,0,0,0-1.41.108L6,7.964,3.759,5.349a1,1,0,1,0-1.519,1.3L4.683,9.5,2.241,12.35a1,1,0,1,0,1.519,1.3L6,11.036,8.241,13.65a1,1,0,0,0,1.519-1.3L7.317,9.5,9.759,6.651A1,1,0,0,0,9.651,5.241Z /> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <line class="ql-stroke ql-thin" x1=15.5 x2=2.5 y1=8.5 y2=9.5></line> <path class=ql-fill d=M9.007,8C6.542,7.791,6,7.519,6,6.5,6,5.792,7.283,5,9,5c1.571,0,2.765.679,2.969,1.309a1,1,0,0,0,1.9-.617C13.356,4.106,11.354,3,9,3,6.2,3,4,4.538,4,6.5a3.2,3.2,0,0,0,.5,1.843Z></path> <path class=ql-fill d=M8.984,10C11.457,10.208,12,10.479,12,11.5c0,0.708-1.283,1.5-3,1.5-1.571,0-2.765-.679-2.969-1.309a1,1,0,1,0-1.9.617C4.644,13.894,6.646,15,9,15c2.8,0,5-1.538,5-3.5a3.2,3.2,0,0,0-.5-1.843Z></path> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <path class=ql-stroke d=M5,3V9a4.012,4.012,0,0,0,4,4H9a4.012,4.012,0,0,0,4-4V3></path> <rect class=ql-fill height=1 rx=0.5 ry=0.5 width=12 x=3 y=15></rect> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <rect class=ql-stroke height=12 width=12 x=3 y=3></rect> <rect class=ql-fill height=12 width=1 x=5 y=3></rect> <rect class=ql-fill height=12 width=1 x=12 y=3></rect> <rect class=ql-fill height=2 width=8 x=5 y=8></rect> <rect class=ql-fill height=1 width=3 x=3 y=5></rect> <rect class=ql-fill height=1 width=3 x=3 y=7></rect> <rect class=ql-fill height=1 width=3 x=3 y=10></rect> <rect class=ql-fill height=1 width=3 x=3 y=12></rect> <rect class=ql-fill height=1 width=3 x=12 y=5></rect> <rect class=ql-fill height=1 width=3 x=12 y=7></rect> <rect class=ql-fill height=1 width=3 x=12 y=10></rect> <rect class=ql-fill height=1 width=3 x=12 y=12></rect> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <polygon class=ql-stroke points="7 11 9 13 11 11 7 11"></polygon> <polygon class=ql-stroke points="7 7 9 5 11 7 7 7"></polygon> </svg>'},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=e.BubbleTooltip=void 0;var l=function t(e,o,l){null===e&&(e=Function.prototype);var n=Object.getOwnPropertyDescriptor(e,o);if(void 0===n){var i=Object.getPrototypeOf(e);return null===i?void 0:t(i,o,l)}if("value"in n)return n.value;var r=n.get;return void 0!==r?r.call(l):void 0},n=function(){function t(t,e){for(var o=0;o<e.length;o++){var l=e[o];l.enumerable=l.enumerable||!1,l.configurable=!0,"value"in l&&(l.writable=!0),Object.defineProperty(t,l.key,l)}}return function(e,o,l){return o&&t(e.prototype,o),l&&t(e,l),e}}(),i=d(o(3)),r=d(o(8)),a=o(43),s=d(a),c=o(15),u=d(o(41));function d(t){return t&&t.__esModule?t:{default:t}}function f(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function p(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}function h(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var b=[["bold","italic","link"],[{header:1},{header:2},"blockquote"]],q=function(t){function e(t,o){f(this,e),null!=o.modules.toolbar&&null==o.modules.toolbar.container&&(o.modules.toolbar.container=b);var l=p(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,t,o));return l.quill.container.classList.add("ql-bubble"),l}return h(e,t),n(e,[{key:"extendToolbar",value:function(t){this.tooltip=new y(this.quill,this.options.bounds),this.tooltip.root.appendChild(t.container),this.buildButtons([].slice.call(t.container.querySelectorAll("button")),u.default),this.buildPickers([].slice.call(t.container.querySelectorAll("select")),u.default)}}]),e}(s.default);q.DEFAULTS=(0,i.default)(!0,{},s.default.DEFAULTS,{modules:{toolbar:{handlers:{link:function(t){t?this.quill.theme.tooltip.edit():this.quill.format("link",!1)}}}}});var y=function(t){function e(t,o){f(this,e);var l=p(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,t,o));return l.quill.on(r.default.events.EDITOR_CHANGE,function(t,e,o,n){if(t===r.default.events.SELECTION_CHANGE)if(null!=e&&e.length>0&&n===r.default.sources.USER){l.show(),l.root.style.left="0px",l.root.style.width="",l.root.style.width=l.root.offsetWidth+"px";var i=l.quill.getLines(e.index,e.length);if(1===i.length)l.position(l.quill.getBounds(e));else{var a=i[i.length-1],s=l.quill.getIndex(a),u=Math.min(a.length()-1,e.index+e.length-s),d=l.quill.getBounds(new c.Range(s,u));l.position(d)}}else document.activeElement!==l.textbox&&l.quill.hasFocus()&&l.hide()}),l}return h(e,t),n(e,[{key:"listen",value:function(){var t=this;l(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"listen",this).call(this),this.root.querySelector(".ql-close").addEventListener("click",function(){t.root.classList.remove("ql-editing")}),this.quill.on(r.default.events.SCROLL_OPTIMIZE,function(){setTimeout(function(){if(!t.root.classList.contains("ql-hidden")){var e=t.quill.getSelection();null!=e&&t.position(t.quill.getBounds(e))}},1)})}},{key:"cancel",value:function(){this.show()}},{key:"position",value:function(t){var o=l(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"position",this).call(this,t),n=this.root.querySelector(".ql-tooltip-arrow");if(n.style.marginLeft="",0===o)return o;n.style.marginLeft=-1*o-n.offsetWidth/2+"px"}}]),e}(a.BaseTooltip);y.TEMPLATE=['<span class="ql-tooltip-arrow"></span>','<div class="ql-tooltip-editor">','<input type="text" data-formula="e=mc^2" data-link="https://quilljs.com" data-video="Embed URL">','<a class="ql-close"></a>',"</div>"].join(""),e.BubbleTooltip=y,e.default=q},function(t,e,o){t.exports=o(63)}]).default},t.exports=l()},2676:(t,e,o)=>{"use strict";o.d(e,{A:()=>i});var l=o(6314),n=o.n(l)()(function(t){return t[1]});n.push([t.id,'/*!\n * Quill Editor v1.3.7\n * https://quilljs.com/\n * Copyright (c) 2014, Jason Chen\n * Copyright (c) 2013, salesforce.com\n */.ql-container{box-sizing:border-box;font-family:Helvetica,Arial,sans-serif;font-size:13px;height:100%;margin:0;position:relative}.ql-container.ql-disabled .ql-tooltip{visibility:hidden}.ql-container.ql-disabled .ql-editor ul[data-checked]>li:before{pointer-events:none}.ql-clipboard{height:1px;left:-100000px;overflow-y:hidden;position:absolute;top:50%}.ql-clipboard p{margin:0;padding:0}.ql-editor{word-wrap:break-word;box-sizing:border-box;height:100%;line-height:1.42;outline:none;overflow-y:auto;padding:12px 15px;-o-tab-size:4;tab-size:4;-moz-tab-size:4;text-align:left;white-space:pre-wrap}.ql-editor>*{cursor:text}.ql-editor blockquote,.ql-editor h1,.ql-editor h2,.ql-editor h3,.ql-editor h4,.ql-editor h5,.ql-editor h6,.ql-editor ol,.ql-editor p,.ql-editor pre,.ql-editor ul{counter-reset:list-1 list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9;margin:0;padding:0}.ql-editor ol,.ql-editor ul{padding-left:1.5em}.ql-editor ol>li,.ql-editor ul>li{list-style-type:none}.ql-editor ul>li:before{content:"\\2022"}.ql-editor ul[data-checked=false],.ql-editor ul[data-checked=true]{pointer-events:none}.ql-editor ul[data-checked=false]>li *,.ql-editor ul[data-checked=true]>li *{pointer-events:all}.ql-editor ul[data-checked=false]>li:before,.ql-editor ul[data-checked=true]>li:before{color:#777;cursor:pointer;pointer-events:all}.ql-editor ul[data-checked=true]>li:before{content:"\\2611"}.ql-editor ul[data-checked=false]>li:before{content:"\\2610"}.ql-editor li:before{display:inline-block;white-space:nowrap;width:1.2em}.ql-editor li:not(.ql-direction-rtl):before{margin-left:-1.5em;margin-right:.3em;text-align:right}.ql-editor li.ql-direction-rtl:before{margin-left:.3em;margin-right:-1.5em}.ql-editor ol li:not(.ql-direction-rtl),.ql-editor ul li:not(.ql-direction-rtl){padding-left:1.5em}.ql-editor ol li.ql-direction-rtl,.ql-editor ul li.ql-direction-rtl{padding-right:1.5em}.ql-editor ol li{counter-increment:list-0;counter-reset:list-1 list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9}.ql-editor ol li:before{content:counter(list-0,decimal) ". "}.ql-editor ol li.ql-indent-1{counter-increment:list-1}.ql-editor ol li.ql-indent-1:before{content:counter(list-1,lower-alpha) ". "}.ql-editor ol li.ql-indent-1{counter-reset:list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9}.ql-editor ol li.ql-indent-2{counter-increment:list-2}.ql-editor ol li.ql-indent-2:before{content:counter(list-2,lower-roman) ". "}.ql-editor ol li.ql-indent-2{counter-reset:list-3 list-4 list-5 list-6 list-7 list-8 list-9}.ql-editor ol li.ql-indent-3{counter-increment:list-3}.ql-editor ol li.ql-indent-3:before{content:counter(list-3,decimal) ". "}.ql-editor ol li.ql-indent-3{counter-reset:list-4 list-5 list-6 list-7 list-8 list-9}.ql-editor ol li.ql-indent-4{counter-increment:list-4}.ql-editor ol li.ql-indent-4:before{content:counter(list-4,lower-alpha) ". "}.ql-editor ol li.ql-indent-4{counter-reset:list-5 list-6 list-7 list-8 list-9}.ql-editor ol li.ql-indent-5{counter-increment:list-5}.ql-editor ol li.ql-indent-5:before{content:counter(list-5,lower-roman) ". "}.ql-editor ol li.ql-indent-5{counter-reset:list-6 list-7 list-8 list-9}.ql-editor ol li.ql-indent-6{counter-increment:list-6}.ql-editor ol li.ql-indent-6:before{content:counter(list-6,decimal) ". "}.ql-editor ol li.ql-indent-6{counter-reset:list-7 list-8 list-9}.ql-editor ol li.ql-indent-7{counter-increment:list-7}.ql-editor ol li.ql-indent-7:before{content:counter(list-7,lower-alpha) ". "}.ql-editor ol li.ql-indent-7{counter-reset:list-8 list-9}.ql-editor ol li.ql-indent-8{counter-increment:list-8}.ql-editor ol li.ql-indent-8:before{content:counter(list-8,lower-roman) ". "}.ql-editor ol li.ql-indent-8{counter-reset:list-9}.ql-editor ol li.ql-indent-9{counter-increment:list-9}.ql-editor ol li.ql-indent-9:before{content:counter(list-9,decimal) ". "}.ql-editor .ql-indent-1:not(.ql-direction-rtl){padding-left:3em}.ql-editor li.ql-indent-1:not(.ql-direction-rtl){padding-left:4.5em}.ql-editor .ql-indent-1.ql-direction-rtl.ql-align-right{padding-right:3em}.ql-editor li.ql-indent-1.ql-direction-rtl.ql-align-right{padding-right:4.5em}.ql-editor .ql-indent-2:not(.ql-direction-rtl){padding-left:6em}.ql-editor li.ql-indent-2:not(.ql-direction-rtl){padding-left:7.5em}.ql-editor .ql-indent-2.ql-direction-rtl.ql-align-right{padding-right:6em}.ql-editor li.ql-indent-2.ql-direction-rtl.ql-align-right{padding-right:7.5em}.ql-editor .ql-indent-3:not(.ql-direction-rtl){padding-left:9em}.ql-editor li.ql-indent-3:not(.ql-direction-rtl){padding-left:10.5em}.ql-editor .ql-indent-3.ql-direction-rtl.ql-align-right{padding-right:9em}.ql-editor li.ql-indent-3.ql-direction-rtl.ql-align-right{padding-right:10.5em}.ql-editor .ql-indent-4:not(.ql-direction-rtl){padding-left:12em}.ql-editor li.ql-indent-4:not(.ql-direction-rtl){padding-left:13.5em}.ql-editor .ql-indent-4.ql-direction-rtl.ql-align-right{padding-right:12em}.ql-editor li.ql-indent-4.ql-direction-rtl.ql-align-right{padding-right:13.5em}.ql-editor .ql-indent-5:not(.ql-direction-rtl){padding-left:15em}.ql-editor li.ql-indent-5:not(.ql-direction-rtl){padding-left:16.5em}.ql-editor .ql-indent-5.ql-direction-rtl.ql-align-right{padding-right:15em}.ql-editor li.ql-indent-5.ql-direction-rtl.ql-align-right{padding-right:16.5em}.ql-editor .ql-indent-6:not(.ql-direction-rtl){padding-left:18em}.ql-editor li.ql-indent-6:not(.ql-direction-rtl){padding-left:19.5em}.ql-editor .ql-indent-6.ql-direction-rtl.ql-align-right{padding-right:18em}.ql-editor li.ql-indent-6.ql-direction-rtl.ql-align-right{padding-right:19.5em}.ql-editor .ql-indent-7:not(.ql-direction-rtl){padding-left:21em}.ql-editor li.ql-indent-7:not(.ql-direction-rtl){padding-left:22.5em}.ql-editor .ql-indent-7.ql-direction-rtl.ql-align-right{padding-right:21em}.ql-editor li.ql-indent-7.ql-direction-rtl.ql-align-right{padding-right:22.5em}.ql-editor .ql-indent-8:not(.ql-direction-rtl){padding-left:24em}.ql-editor li.ql-indent-8:not(.ql-direction-rtl){padding-left:25.5em}.ql-editor .ql-indent-8.ql-direction-rtl.ql-align-right{padding-right:24em}.ql-editor li.ql-indent-8.ql-direction-rtl.ql-align-right{padding-right:25.5em}.ql-editor .ql-indent-9:not(.ql-direction-rtl){padding-left:27em}.ql-editor li.ql-indent-9:not(.ql-direction-rtl){padding-left:28.5em}.ql-editor .ql-indent-9.ql-direction-rtl.ql-align-right{padding-right:27em}.ql-editor li.ql-indent-9.ql-direction-rtl.ql-align-right{padding-right:28.5em}.ql-editor .ql-video{display:block;max-width:100%}.ql-editor .ql-video.ql-align-center{margin:0 auto}.ql-editor .ql-video.ql-align-right{margin:0 0 0 auto}.ql-editor .ql-bg-black{background-color:#000}.ql-editor .ql-bg-red{background-color:#e60000}.ql-editor .ql-bg-orange{background-color:#f90}.ql-editor .ql-bg-yellow{background-color:#ff0}.ql-editor .ql-bg-green{background-color:#008a00}.ql-editor .ql-bg-blue{background-color:#06c}.ql-editor .ql-bg-purple{background-color:#93f}.ql-editor .ql-color-white{color:#fff}.ql-editor .ql-color-red{color:#e60000}.ql-editor .ql-color-orange{color:#f90}.ql-editor .ql-color-yellow{color:#ff0}.ql-editor .ql-color-green{color:#008a00}.ql-editor .ql-color-blue{color:#06c}.ql-editor .ql-color-purple{color:#93f}.ql-editor .ql-font-serif{font-family:Georgia,Times New Roman,serif}.ql-editor .ql-font-monospace{font-family:Monaco,Courier New,monospace}.ql-editor .ql-size-small{font-size:.75em}.ql-editor .ql-size-large{font-size:1.5em}.ql-editor .ql-size-huge{font-size:2.5em}.ql-editor .ql-direction-rtl{direction:rtl;text-align:inherit}.ql-editor .ql-align-center{text-align:center}.ql-editor .ql-align-justify{text-align:justify}.ql-editor .ql-align-right{text-align:right}.ql-editor.ql-blank:before{color:rgba(0,0,0,.6);content:attr(data-placeholder);font-style:italic;left:15px;pointer-events:none;position:absolute;right:15px}.ql-snow .ql-toolbar:after,.ql-snow.ql-toolbar:after{clear:both;content:"";display:table}.ql-snow .ql-toolbar button,.ql-snow.ql-toolbar button{background:none;border:none;cursor:pointer;display:inline-block;float:left;height:24px;padding:3px 5px;width:28px}.ql-snow .ql-toolbar button svg,.ql-snow.ql-toolbar button svg{float:left;height:100%}.ql-snow .ql-toolbar button:active:hover,.ql-snow.ql-toolbar button:active:hover{outline:none}.ql-snow .ql-toolbar input.ql-image[type=file],.ql-snow.ql-toolbar input.ql-image[type=file]{display:none}.ql-snow .ql-toolbar .ql-picker-item.ql-selected,.ql-snow .ql-toolbar .ql-picker-item:hover,.ql-snow .ql-toolbar .ql-picker-label.ql-active,.ql-snow .ql-toolbar .ql-picker-label:hover,.ql-snow .ql-toolbar button.ql-active,.ql-snow .ql-toolbar button:focus,.ql-snow .ql-toolbar button:hover,.ql-snow.ql-toolbar .ql-picker-item.ql-selected,.ql-snow.ql-toolbar .ql-picker-item:hover,.ql-snow.ql-toolbar .ql-picker-label.ql-active,.ql-snow.ql-toolbar .ql-picker-label:hover,.ql-snow.ql-toolbar button.ql-active,.ql-snow.ql-toolbar button:focus,.ql-snow.ql-toolbar button:hover{color:#06c}.ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-fill,.ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-stroke.ql-fill,.ql-snow .ql-toolbar .ql-picker-item:hover .ql-fill,.ql-snow .ql-toolbar .ql-picker-item:hover .ql-stroke.ql-fill,.ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-fill,.ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-stroke.ql-fill,.ql-snow .ql-toolbar .ql-picker-label:hover .ql-fill,.ql-snow .ql-toolbar .ql-picker-label:hover .ql-stroke.ql-fill,.ql-snow .ql-toolbar button.ql-active .ql-fill,.ql-snow .ql-toolbar button.ql-active .ql-stroke.ql-fill,.ql-snow .ql-toolbar button:focus .ql-fill,.ql-snow .ql-toolbar button:focus .ql-stroke.ql-fill,.ql-snow .ql-toolbar button:hover .ql-fill,.ql-snow .ql-toolbar button:hover .ql-stroke.ql-fill,.ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-fill,.ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-stroke.ql-fill,.ql-snow.ql-toolbar .ql-picker-item:hover .ql-fill,.ql-snow.ql-toolbar .ql-picker-item:hover .ql-stroke.ql-fill,.ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-fill,.ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-stroke.ql-fill,.ql-snow.ql-toolbar .ql-picker-label:hover .ql-fill,.ql-snow.ql-toolbar .ql-picker-label:hover .ql-stroke.ql-fill,.ql-snow.ql-toolbar button.ql-active .ql-fill,.ql-snow.ql-toolbar button.ql-active .ql-stroke.ql-fill,.ql-snow.ql-toolbar button:focus .ql-fill,.ql-snow.ql-toolbar button:focus .ql-stroke.ql-fill,.ql-snow.ql-toolbar button:hover .ql-fill,.ql-snow.ql-toolbar button:hover .ql-stroke.ql-fill{fill:#06c}.ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-stroke,.ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-stroke-miter,.ql-snow .ql-toolbar .ql-picker-item:hover .ql-stroke,.ql-snow .ql-toolbar .ql-picker-item:hover .ql-stroke-miter,.ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-stroke,.ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-stroke-miter,.ql-snow .ql-toolbar .ql-picker-label:hover .ql-stroke,.ql-snow .ql-toolbar .ql-picker-label:hover .ql-stroke-miter,.ql-snow .ql-toolbar button.ql-active .ql-stroke,.ql-snow .ql-toolbar button.ql-active .ql-stroke-miter,.ql-snow .ql-toolbar button:focus .ql-stroke,.ql-snow .ql-toolbar button:focus .ql-stroke-miter,.ql-snow .ql-toolbar button:hover .ql-stroke,.ql-snow .ql-toolbar button:hover .ql-stroke-miter,.ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-stroke,.ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-stroke-miter,.ql-snow.ql-toolbar .ql-picker-item:hover .ql-stroke,.ql-snow.ql-toolbar .ql-picker-item:hover .ql-stroke-miter,.ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-stroke,.ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-stroke-miter,.ql-snow.ql-toolbar .ql-picker-label:hover .ql-stroke,.ql-snow.ql-toolbar .ql-picker-label:hover .ql-stroke-miter,.ql-snow.ql-toolbar button.ql-active .ql-stroke,.ql-snow.ql-toolbar button.ql-active .ql-stroke-miter,.ql-snow.ql-toolbar button:focus .ql-stroke,.ql-snow.ql-toolbar button:focus .ql-stroke-miter,.ql-snow.ql-toolbar button:hover .ql-stroke,.ql-snow.ql-toolbar button:hover .ql-stroke-miter{stroke:#06c}@media (pointer:coarse){.ql-snow .ql-toolbar button:hover:not(.ql-active),.ql-snow.ql-toolbar button:hover:not(.ql-active){color:#444}.ql-snow .ql-toolbar button:hover:not(.ql-active) .ql-fill,.ql-snow .ql-toolbar button:hover:not(.ql-active) .ql-stroke.ql-fill,.ql-snow.ql-toolbar button:hover:not(.ql-active) .ql-fill,.ql-snow.ql-toolbar button:hover:not(.ql-active) .ql-stroke.ql-fill{fill:#444}.ql-snow .ql-toolbar button:hover:not(.ql-active) .ql-stroke,.ql-snow .ql-toolbar button:hover:not(.ql-active) .ql-stroke-miter,.ql-snow.ql-toolbar button:hover:not(.ql-active) .ql-stroke,.ql-snow.ql-toolbar button:hover:not(.ql-active) .ql-stroke-miter{stroke:#444}}.ql-snow,.ql-snow *{box-sizing:border-box}.ql-snow .ql-hidden{display:none}.ql-snow .ql-out-bottom,.ql-snow .ql-out-top{visibility:hidden}.ql-snow .ql-tooltip{position:absolute;transform:translateY(10px)}.ql-snow .ql-tooltip a{cursor:pointer;text-decoration:none}.ql-snow .ql-tooltip.ql-flip{transform:translateY(-10px)}.ql-snow .ql-formats{display:inline-block;vertical-align:middle}.ql-snow .ql-formats:after{clear:both;content:"";display:table}.ql-snow .ql-stroke{fill:none;stroke:#444;stroke-linecap:round;stroke-linejoin:round;stroke-width:2}.ql-snow .ql-stroke-miter{fill:none;stroke:#444;stroke-miterlimit:10;stroke-width:2}.ql-snow .ql-fill,.ql-snow .ql-stroke.ql-fill{fill:#444}.ql-snow .ql-empty{fill:none}.ql-snow .ql-even{fill-rule:evenodd}.ql-snow .ql-stroke.ql-thin,.ql-snow .ql-thin{stroke-width:1}.ql-snow .ql-transparent{opacity:.4}.ql-snow .ql-direction svg:last-child{display:none}.ql-snow .ql-direction.ql-active svg:last-child{display:inline}.ql-snow .ql-direction.ql-active svg:first-child{display:none}.ql-snow .ql-editor h1{font-size:2em}.ql-snow .ql-editor h2{font-size:1.5em}.ql-snow .ql-editor h3{font-size:1.17em}.ql-snow .ql-editor h4{font-size:1em}.ql-snow .ql-editor h5{font-size:.83em}.ql-snow .ql-editor h6{font-size:.67em}.ql-snow .ql-editor a{text-decoration:underline}.ql-snow .ql-editor blockquote{border-left:4px solid #ccc;margin-bottom:5px;margin-top:5px;padding-left:16px}.ql-snow .ql-editor code,.ql-snow .ql-editor pre{background-color:#f0f0f0;border-radius:3px}.ql-snow .ql-editor pre{margin-bottom:5px;margin-top:5px;padding:5px 10px;white-space:pre-wrap}.ql-snow .ql-editor code{font-size:85%;padding:2px 4px}.ql-snow .ql-editor pre.ql-syntax{background-color:#23241f;color:#f8f8f2;overflow:visible}.ql-snow .ql-editor img{max-width:100%}.ql-snow .ql-picker{color:#444;display:inline-block;float:left;font-size:14px;font-weight:500;height:24px;position:relative;vertical-align:middle}.ql-snow .ql-picker-label{cursor:pointer;display:inline-block;height:100%;padding-left:8px;padding-right:2px;position:relative;width:100%}.ql-snow .ql-picker-label:before{display:inline-block;line-height:22px}.ql-snow .ql-picker-options{background-color:#fff;display:none;min-width:100%;padding:4px 8px;position:absolute;white-space:nowrap}.ql-snow .ql-picker-options .ql-picker-item{cursor:pointer;display:block;padding-bottom:5px;padding-top:5px}.ql-snow .ql-picker.ql-expanded .ql-picker-label{color:#ccc;z-index:2}.ql-snow .ql-picker.ql-expanded .ql-picker-label .ql-fill{fill:#ccc}.ql-snow .ql-picker.ql-expanded .ql-picker-label .ql-stroke{stroke:#ccc}.ql-snow .ql-picker.ql-expanded .ql-picker-options{display:block;margin-top:-1px;top:100%;z-index:1}.ql-snow .ql-color-picker,.ql-snow .ql-icon-picker{width:28px}.ql-snow .ql-color-picker .ql-picker-label,.ql-snow .ql-icon-picker .ql-picker-label{padding:2px 4px}.ql-snow .ql-color-picker .ql-picker-label svg,.ql-snow .ql-icon-picker .ql-picker-label svg{right:4px}.ql-snow .ql-icon-picker .ql-picker-options{padding:4px 0}.ql-snow .ql-icon-picker .ql-picker-item{height:24px;padding:2px 4px;width:24px}.ql-snow .ql-color-picker .ql-picker-options{padding:3px 5px;width:152px}.ql-snow .ql-color-picker .ql-picker-item{border:1px solid transparent;float:left;height:16px;margin:2px;padding:0;width:16px}.ql-snow .ql-picker:not(.ql-color-picker):not(.ql-icon-picker) svg{margin-top:-9px;position:absolute;right:0;top:50%;width:18px}.ql-snow .ql-picker.ql-font .ql-picker-item[data-label]:not([data-label=""]):before,.ql-snow .ql-picker.ql-font .ql-picker-label[data-label]:not([data-label=""]):before,.ql-snow .ql-picker.ql-header .ql-picker-item[data-label]:not([data-label=""]):before,.ql-snow .ql-picker.ql-header .ql-picker-label[data-label]:not([data-label=""]):before,.ql-snow .ql-picker.ql-size .ql-picker-item[data-label]:not([data-label=""]):before,.ql-snow .ql-picker.ql-size .ql-picker-label[data-label]:not([data-label=""]):before{content:attr(data-label)}.ql-snow .ql-picker.ql-header{width:98px}.ql-snow .ql-picker.ql-header .ql-picker-item:before,.ql-snow .ql-picker.ql-header .ql-picker-label:before{content:"Normal"}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="1"]:before,.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="1"]:before{content:"Heading 1"}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="2"]:before,.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="2"]:before{content:"Heading 2"}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="3"]:before,.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="3"]:before{content:"Heading 3"}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="4"]:before,.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="4"]:before{content:"Heading 4"}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="5"]:before,.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="5"]:before{content:"Heading 5"}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="6"]:before,.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="6"]:before{content:"Heading 6"}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="1"]:before{font-size:2em}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="2"]:before{font-size:1.5em}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="3"]:before{font-size:1.17em}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="4"]:before{font-size:1em}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="5"]:before{font-size:.83em}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="6"]:before{font-size:.67em}.ql-snow .ql-picker.ql-font{width:108px}.ql-snow .ql-picker.ql-font .ql-picker-item:before,.ql-snow .ql-picker.ql-font .ql-picker-label:before{content:"Sans Serif"}.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=serif]:before,.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=serif]:before{content:"Serif"}.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=monospace]:before,.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=monospace]:before{content:"Monospace"}.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=serif]:before{font-family:Georgia,Times New Roman,serif}.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=monospace]:before{font-family:Monaco,Courier New,monospace}.ql-snow .ql-picker.ql-size{width:98px}.ql-snow .ql-picker.ql-size .ql-picker-item:before,.ql-snow .ql-picker.ql-size .ql-picker-label:before{content:"Normal"}.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=small]:before,.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=small]:before{content:"Small"}.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=large]:before,.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=large]:before{content:"Large"}.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=huge]:before,.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=huge]:before{content:"Huge"}.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=small]:before{font-size:10px}.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=large]:before{font-size:18px}.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=huge]:before{font-size:32px}.ql-snow .ql-color-picker.ql-background .ql-picker-item{background-color:#fff}.ql-snow .ql-color-picker.ql-color .ql-picker-item{background-color:#000}.ql-toolbar.ql-snow{border:1px solid #ccc;box-sizing:border-box;font-family:Helvetica Neue,Helvetica,Arial,sans-serif;padding:8px}.ql-toolbar.ql-snow .ql-formats{margin-right:15px}.ql-toolbar.ql-snow .ql-picker-label{border:1px solid transparent}.ql-toolbar.ql-snow .ql-picker-options{border:1px solid transparent;box-shadow:0 2px 8px rgba(0,0,0,.2)}.ql-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-label,.ql-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-options{border-color:#ccc}.ql-toolbar.ql-snow .ql-color-picker .ql-picker-item.ql-selected,.ql-toolbar.ql-snow .ql-color-picker .ql-picker-item:hover{border-color:#000}.ql-toolbar.ql-snow+.ql-container.ql-snow{border-top:0}.ql-snow .ql-tooltip{background-color:#fff;border:1px solid #ccc;box-shadow:0 0 5px #ddd;color:#444;padding:5px 12px;white-space:nowrap}.ql-snow .ql-tooltip:before{content:"Visit URL:";line-height:26px;margin-right:8px}.ql-snow .ql-tooltip input[type=text]{border:1px solid #ccc;display:none;font-size:13px;height:26px;margin:0;padding:3px 5px;width:170px}.ql-snow .ql-tooltip a.ql-preview{display:inline-block;max-width:200px;overflow-x:hidden;text-overflow:ellipsis;vertical-align:top}.ql-snow .ql-tooltip a.ql-action:after{border-right:1px solid #ccc;content:"Edit";margin-left:16px;padding-right:8px}.ql-snow .ql-tooltip a.ql-remove:before{content:"Remove";margin-left:8px}.ql-snow .ql-tooltip a{line-height:26px}.ql-snow .ql-tooltip.ql-editing a.ql-preview,.ql-snow .ql-tooltip.ql-editing a.ql-remove{display:none}.ql-snow .ql-tooltip.ql-editing input[type=text]{display:inline-block}.ql-snow .ql-tooltip.ql-editing a.ql-action:after{border-right:0;content:"Save";padding-right:0}.ql-snow .ql-tooltip[data-mode=link]:before{content:"Enter link:"}.ql-snow .ql-tooltip[data-mode=formula]:before{content:"Enter formula:"}.ql-snow .ql-tooltip[data-mode=video]:before{content:"Enter video:"}.ql-snow a{color:#06c}.ql-container.ql-snow{border:1px solid #ccc}',""]);const i=n},5420:(t,e,o)=>{"use strict";o.d(e,{A:()=>i});var l=o(6314),n=o.n(l)()(function(t){return t[1]});n.push([t.id,'/*!\n * Quill Editor v1.3.7\n * https://quilljs.com/\n * Copyright (c) 2014, Jason Chen\n * Copyright (c) 2013, salesforce.com\n */.ql-container{box-sizing:border-box;font-family:Helvetica,Arial,sans-serif;font-size:13px;height:100%;margin:0;position:relative}.ql-container.ql-disabled .ql-tooltip{visibility:hidden}.ql-container.ql-disabled .ql-editor ul[data-checked]>li:before{pointer-events:none}.ql-clipboard{height:1px;left:-100000px;overflow-y:hidden;position:absolute;top:50%}.ql-clipboard p{margin:0;padding:0}.ql-editor{word-wrap:break-word;box-sizing:border-box;height:100%;line-height:1.42;outline:none;overflow-y:auto;padding:12px 15px;-o-tab-size:4;tab-size:4;-moz-tab-size:4;text-align:left;white-space:pre-wrap}.ql-editor>*{cursor:text}.ql-editor blockquote,.ql-editor h1,.ql-editor h2,.ql-editor h3,.ql-editor h4,.ql-editor h5,.ql-editor h6,.ql-editor ol,.ql-editor p,.ql-editor pre,.ql-editor ul{counter-reset:list-1 list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9;margin:0;padding:0}.ql-editor ol,.ql-editor ul{padding-left:1.5em}.ql-editor ol>li,.ql-editor ul>li{list-style-type:none}.ql-editor ul>li:before{content:"\\2022"}.ql-editor ul[data-checked=false],.ql-editor ul[data-checked=true]{pointer-events:none}.ql-editor ul[data-checked=false]>li *,.ql-editor ul[data-checked=true]>li *{pointer-events:all}.ql-editor ul[data-checked=false]>li:before,.ql-editor ul[data-checked=true]>li:before{color:#777;cursor:pointer;pointer-events:all}.ql-editor ul[data-checked=true]>li:before{content:"\\2611"}.ql-editor ul[data-checked=false]>li:before{content:"\\2610"}.ql-editor li:before{display:inline-block;white-space:nowrap;width:1.2em}.ql-editor li:not(.ql-direction-rtl):before{margin-left:-1.5em;margin-right:.3em;text-align:right}.ql-editor li.ql-direction-rtl:before{margin-left:.3em;margin-right:-1.5em}.ql-editor ol li:not(.ql-direction-rtl),.ql-editor ul li:not(.ql-direction-rtl){padding-left:1.5em}.ql-editor ol li.ql-direction-rtl,.ql-editor ul li.ql-direction-rtl{padding-right:1.5em}.ql-editor ol li{counter-increment:list-0;counter-reset:list-1 list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9}.ql-editor ol li:before{content:counter(list-0,decimal) ". "}.ql-editor ol li.ql-indent-1{counter-increment:list-1}.ql-editor ol li.ql-indent-1:before{content:counter(list-1,lower-alpha) ". "}.ql-editor ol li.ql-indent-1{counter-reset:list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9}.ql-editor ol li.ql-indent-2{counter-increment:list-2}.ql-editor ol li.ql-indent-2:before{content:counter(list-2,lower-roman) ". "}.ql-editor ol li.ql-indent-2{counter-reset:list-3 list-4 list-5 list-6 list-7 list-8 list-9}.ql-editor ol li.ql-indent-3{counter-increment:list-3}.ql-editor ol li.ql-indent-3:before{content:counter(list-3,decimal) ". "}.ql-editor ol li.ql-indent-3{counter-reset:list-4 list-5 list-6 list-7 list-8 list-9}.ql-editor ol li.ql-indent-4{counter-increment:list-4}.ql-editor ol li.ql-indent-4:before{content:counter(list-4,lower-alpha) ". "}.ql-editor ol li.ql-indent-4{counter-reset:list-5 list-6 list-7 list-8 list-9}.ql-editor ol li.ql-indent-5{counter-increment:list-5}.ql-editor ol li.ql-indent-5:before{content:counter(list-5,lower-roman) ". "}.ql-editor ol li.ql-indent-5{counter-reset:list-6 list-7 list-8 list-9}.ql-editor ol li.ql-indent-6{counter-increment:list-6}.ql-editor ol li.ql-indent-6:before{content:counter(list-6,decimal) ". "}.ql-editor ol li.ql-indent-6{counter-reset:list-7 list-8 list-9}.ql-editor ol li.ql-indent-7{counter-increment:list-7}.ql-editor ol li.ql-indent-7:before{content:counter(list-7,lower-alpha) ". "}.ql-editor ol li.ql-indent-7{counter-reset:list-8 list-9}.ql-editor ol li.ql-indent-8{counter-increment:list-8}.ql-editor ol li.ql-indent-8:before{content:counter(list-8,lower-roman) ". "}.ql-editor ol li.ql-indent-8{counter-reset:list-9}.ql-editor ol li.ql-indent-9{counter-increment:list-9}.ql-editor ol li.ql-indent-9:before{content:counter(list-9,decimal) ". "}.ql-editor .ql-indent-1:not(.ql-direction-rtl){padding-left:3em}.ql-editor li.ql-indent-1:not(.ql-direction-rtl){padding-left:4.5em}.ql-editor .ql-indent-1.ql-direction-rtl.ql-align-right{padding-right:3em}.ql-editor li.ql-indent-1.ql-direction-rtl.ql-align-right{padding-right:4.5em}.ql-editor .ql-indent-2:not(.ql-direction-rtl){padding-left:6em}.ql-editor li.ql-indent-2:not(.ql-direction-rtl){padding-left:7.5em}.ql-editor .ql-indent-2.ql-direction-rtl.ql-align-right{padding-right:6em}.ql-editor li.ql-indent-2.ql-direction-rtl.ql-align-right{padding-right:7.5em}.ql-editor .ql-indent-3:not(.ql-direction-rtl){padding-left:9em}.ql-editor li.ql-indent-3:not(.ql-direction-rtl){padding-left:10.5em}.ql-editor .ql-indent-3.ql-direction-rtl.ql-align-right{padding-right:9em}.ql-editor li.ql-indent-3.ql-direction-rtl.ql-align-right{padding-right:10.5em}.ql-editor .ql-indent-4:not(.ql-direction-rtl){padding-left:12em}.ql-editor li.ql-indent-4:not(.ql-direction-rtl){padding-left:13.5em}.ql-editor .ql-indent-4.ql-direction-rtl.ql-align-right{padding-right:12em}.ql-editor li.ql-indent-4.ql-direction-rtl.ql-align-right{padding-right:13.5em}.ql-editor .ql-indent-5:not(.ql-direction-rtl){padding-left:15em}.ql-editor li.ql-indent-5:not(.ql-direction-rtl){padding-left:16.5em}.ql-editor .ql-indent-5.ql-direction-rtl.ql-align-right{padding-right:15em}.ql-editor li.ql-indent-5.ql-direction-rtl.ql-align-right{padding-right:16.5em}.ql-editor .ql-indent-6:not(.ql-direction-rtl){padding-left:18em}.ql-editor li.ql-indent-6:not(.ql-direction-rtl){padding-left:19.5em}.ql-editor .ql-indent-6.ql-direction-rtl.ql-align-right{padding-right:18em}.ql-editor li.ql-indent-6.ql-direction-rtl.ql-align-right{padding-right:19.5em}.ql-editor .ql-indent-7:not(.ql-direction-rtl){padding-left:21em}.ql-editor li.ql-indent-7:not(.ql-direction-rtl){padding-left:22.5em}.ql-editor .ql-indent-7.ql-direction-rtl.ql-align-right{padding-right:21em}.ql-editor li.ql-indent-7.ql-direction-rtl.ql-align-right{padding-right:22.5em}.ql-editor .ql-indent-8:not(.ql-direction-rtl){padding-left:24em}.ql-editor li.ql-indent-8:not(.ql-direction-rtl){padding-left:25.5em}.ql-editor .ql-indent-8.ql-direction-rtl.ql-align-right{padding-right:24em}.ql-editor li.ql-indent-8.ql-direction-rtl.ql-align-right{padding-right:25.5em}.ql-editor .ql-indent-9:not(.ql-direction-rtl){padding-left:27em}.ql-editor li.ql-indent-9:not(.ql-direction-rtl){padding-left:28.5em}.ql-editor .ql-indent-9.ql-direction-rtl.ql-align-right{padding-right:27em}.ql-editor li.ql-indent-9.ql-direction-rtl.ql-align-right{padding-right:28.5em}.ql-editor .ql-video{display:block;max-width:100%}.ql-editor .ql-video.ql-align-center{margin:0 auto}.ql-editor .ql-video.ql-align-right{margin:0 0 0 auto}.ql-editor .ql-bg-black{background-color:#000}.ql-editor .ql-bg-red{background-color:#e60000}.ql-editor .ql-bg-orange{background-color:#f90}.ql-editor .ql-bg-yellow{background-color:#ff0}.ql-editor .ql-bg-green{background-color:#008a00}.ql-editor .ql-bg-blue{background-color:#06c}.ql-editor .ql-bg-purple{background-color:#93f}.ql-editor .ql-color-white{color:#fff}.ql-editor .ql-color-red{color:#e60000}.ql-editor .ql-color-orange{color:#f90}.ql-editor .ql-color-yellow{color:#ff0}.ql-editor .ql-color-green{color:#008a00}.ql-editor .ql-color-blue{color:#06c}.ql-editor .ql-color-purple{color:#93f}.ql-editor .ql-font-serif{font-family:Georgia,Times New Roman,serif}.ql-editor .ql-font-monospace{font-family:Monaco,Courier New,monospace}.ql-editor .ql-size-small{font-size:.75em}.ql-editor .ql-size-large{font-size:1.5em}.ql-editor .ql-size-huge{font-size:2.5em}.ql-editor .ql-direction-rtl{direction:rtl;text-align:inherit}.ql-editor .ql-align-center{text-align:center}.ql-editor .ql-align-justify{text-align:justify}.ql-editor .ql-align-right{text-align:right}.ql-editor.ql-blank:before{color:rgba(0,0,0,.6);content:attr(data-placeholder);font-style:italic;left:15px;pointer-events:none;position:absolute;right:15px}',""]);const i=n},6341:(t,e,o)=>{"use strict";o.d(e,{zs:()=>y});var l=o(1574),n=o.n(l),i=o(9726);const r={ref:"editor"};var a=o(5072),s=o.n(a),c=o(5420),u={insert:"head",singleton:!1};s()(c.A,u);c.A.locals;var d=o(2676),f={insert:"head",singleton:!1};s()(d.A,f);d.A.locals;var p=o(9399),h={insert:"head",singleton:!1};s()(p.A,h);p.A.locals;const b={theme:"snow",boundary:document.body,modules:{toolbar:[["bold","italic","underline","strike"],["blockquote","code-block"],[{header:1},{header:2}],[{list:"ordered"},{list:"bullet"}],[{script:"sub"},{script:"super"}],[{indent:"-1"},{indent:"+1"}],[{direction:"rtl"}],[{size:["small",!1,"large","huge"]}],[{header:[1,2,3,4,5,6,!1]}],[{color:[]},{background:[]}],[{font:[]}],[{align:[]}],["clean"],["link","image","video"]]},placeholder:"Insert content here ...",readOnly:!1},q={name:"quill-editor",props:{content:String,value:String,disabled:{type:Boolean,default:!1},options:{type:Object,required:!1,default:()=>({})}},emits:["ready","change","input","blur","focus","update:value"],setup(t,e){const o={editorOption:{},quill:null};let l="";(0,i.watch)(()=>t.value,t=>{o.quill&&(t&&t!==l?(l=t,o.quill.pasteHTML(t)):t||o.quill.setText(""))}),(0,i.watch)(()=>t.content,t=>{o.quill&&(t&&t!==l?(l=t,o.quill.pasteHTML(t)):t||o.quill.setText(""))}),(0,i.watch)(()=>t.disabled,t=>{o.quill&&o.quill.enable(!t)});const r=(0,i.ref)(null),a=(t,e)=>{for(const o in e)t[o]&&"modules"===o?a(t[o],e[o]):t[o]=e[o];return t};return(0,i.onBeforeUnmount)(()=>{const t=r.value.previousSibling;t&&1===t.nodeType&&t.className.indexOf("ql-toolbar")>-1&&t.parentNode.removeChild(t)}),(0,i.onMounted)(()=>{r.value&&(o.editorOption=a(b,t.options),o.editorOption.readOnly=!!t.disabled,o.quill=new(n())(r.value,o.editorOption),t.value&&o.quill.pasteHTML(t.value),o.quill.on("selection-change",t=>{t?e.emit("focus",o.quill):e.emit("blur",o.quill)}),o.quill.on("text-change",()=>{t.disabled&&o.quill.enable(!1);let n=r.value.children[0].innerHTML;const i=o.quill,a=o.quill.getText();"<p><br></p>"===n&&(n=""),l=n,e.emit("update:value",l),e.emit("change",{html:n,text:a,quill:i})}),e.emit("ready",o.quill))}),(0,i.onUnmounted)(()=>{o.quill=null}),{editor:r}}};const y=(0,o(6262).A)(q,[["render",function(t,e,o,l,n,a){return(0,i.openBlock)(),(0,i.createElementBlock)("section",r,null,512)}]]);y.install=function(t){t.component(y.name,y)};n()},7278:(t,e,o)=>{"use strict";o.d(e,{A:()=>T});var l=o(9726),n={class:"w-full rounded-xl mx-auto bg-white transition-all duration-300 max-w-3xl"},i={class:"flex items-center justify-between gap-2 py-4 px-4 border-b border-slate-100"},r={class:"text-lg font-bold capitalize"},a={class:"d-block w-full p-4"},s={class:"form-row"},c={class:"form-col-12 sm:form-col-6"},u={class:"db-field-title"},d={class:"form-col-12 sm:form-col-6"},f={class:"db-field-title required"},p={class:"form-col-12 sm:form-col-6"},h={class:"db-field-title"},b={class:"form-col-12 sm:form-col-6"},q={class:"db-field-title required"},y={key:0,class:"form-row pt-1.5"},v={class:"modal-btns mt-8"},m=["disabled"];var g=o(8655),k={key:0,class:"form-col-12 sm:form-col-6"},_={value:"-1"},w=["value"];const x={name:"ProductVariationsComponent",props:["variations","mode","item"],emits:["method"],data:function(){return{loading:{isActive:!1},selectedVariationIndex:"-1",selectedVariations:[],finalSelectedVariation:null}},mounted:function(){var t=this;"edit"===this.mode&&(this.loading.isActive=!0,this.$store.dispatch("productVariation/ancestorsAndSelfId",this.item.variation_id).then(function(e){t.loading.isActive=!1,t.variations.map(function(o,l){e.data.data.map(function(e,n){o.id===e&&(t.loading.isActive=!0,t.$store.dispatch("productVariation/childrenVariation",e).then(function(e){t.loading.isActive=!1,t.selectedVariationIndex=l,t.selectedVariations=e.data.data,"-1"!==t.selectedVariationIndex&&(t.variations[t.selectedVariationIndex].sku?(t.finalSelectedVariation=t.variations[t.selectedVariationIndex],t.getFinalVariation(t.finalSelectedVariation)):(t.finalSelectedVariation=null,t.getFinalVariation(t.finalSelectedVariation)))}).catch(function(e){t.loading.isActive=!1}))})})}).catch(function(e){t.loading.isActive=!1}))},methods:{selectVariation:function(){var t=this;"-1"!==this.selectedVariationIndex?(this.variations[this.selectedVariationIndex].sku?(this.finalSelectedVariation=this.variations[this.selectedVariationIndex],this.getFinalVariation(this.finalSelectedVariation)):(this.finalSelectedVariation=null,this.getFinalVariation(this.finalSelectedVariation)),this.$store.dispatch("productVariation/childrenVariation",this.variations[this.selectedVariationIndex].id).then(function(e){t.selectedVariations=e.data.data}).catch(function(e){t.loading.isActive=!1})):(this.finalSelectedVariation=null,this.getFinalVariation(this.finalSelectedVariation),this.selectedVariations=[])},getFinalVariation:function(t){this.$emit("method",t)}}};var O=o(5072),E=o.n(O),N=o(8413),A={insert:"head",singleton:!1};E()(N.A,A);N.A.locals;var j=o(6262);const S={components:{ProductVariationsComponent:(0,j.A)(x,[["render",function(t,e,o,n,i,r){var a=(0,l.resolveComponent)("ProductVariationsComponent",!0);return(0,l.openBlock)(),(0,l.createElementBlock)(l.Fragment,null,[o.variations.length>0?((0,l.openBlock)(),(0,l.createElementBlock)("div",k,[((0,l.openBlock)(!0),(0,l.createElementBlock)(l.Fragment,null,(0,l.renderList)(o.variations.slice(0,1),function(t){return(0,l.openBlock)(),(0,l.createElementBlock)("label",{key:t,class:"db-field-title required"},(0,l.toDisplayString)(t.product_attribute_name),1)}),128)),(0,l.withDirectives)((0,l.createElementVNode)("select",{class:"db-field-control",onChange:e[0]||(e[0]=function(e){return r.selectVariation(t.variation)}),"onUpdate:modelValue":e[1]||(e[1]=function(t){return i.selectedVariationIndex=t})},[(0,l.createElementVNode)("option",_,(0,l.toDisplayString)(t.$t("label.please_select")),1),((0,l.openBlock)(!0),(0,l.createElementBlock)(l.Fragment,null,(0,l.renderList)(o.variations,function(t,e){return(0,l.openBlock)(),(0,l.createElementBlock)("option",{value:e,key:e},(0,l.toDisplayString)(t.product_attribute_option_name),9,w)}),128))],544),[[l.vModelSelect,i.selectedVariationIndex]])])):(0,l.createCommentVNode)("",!0),i.selectedVariations.length>0?((0,l.openBlock)(),(0,l.createBlock)(a,{onMethod:r.getFinalVariation,mode:o.mode,item:o.item,key:i.selectedVariations,variations:i.selectedVariations},null,8,["onMethod","mode","item","variations"])):(0,l.createCommentVNode)("",!0)],64)}],["__scopeId","data-v-22a5eb67"]])},name:"ProductModalComponent",props:["item","modal"],data:function(){return{finalVariation:null}},mounted:function(){this.taxList()},computed:{taxes:function(){return this.$store.getters["tax/lists"]},initialVariations:function(){return this.$store.getters["productVariation/initialVariation"]}},watch:{item:function(){this.finalVariation=null}},methods:{onlyNumber:function(t){return g.A.onlyNumber(t)},floatNumber:function(t){return g.A.floatNumber(t)},taxList:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;this.$store.dispatch("tax/lists",{page:t})},taxRateById:function(t){for(var e=0;e<this.taxes.length;e++){var o=this.taxes[e];if(o.id===t)return o.tax_rate}return 0},submitItem:function(t){this.item.is_variation&&(this.item.variation_id=this.finalVariation.id,this.item.sku=this.finalVariation.sku),this.$emit("submitItem")},selectedVariation:function(t){var e=this;this.finalVariation=t,this.finalVariation&&this.$store.dispatch("productVariation/ancestorsToString",this.finalVariation.id).then(function(t){e.item.variation_names=t.data.data})},closeItemModal:function(){this.modal.isShowModal=!1}}},T=(0,j.A)(S,[["render",function(t,e,o,g,k,_){var w=(0,l.resolveComponent)("vue-select"),x=(0,l.resolveComponent)("ProductVariationsComponent");return(0,l.openBlock)(),(0,l.createElementBlock)("div",{id:"modal-demo",class:(0,l.normalizeClass)([{"modal-active":o.modal.isShowModal},"fixed inset-0 z-50 p-3 w-screen h-dvh overflow-y-auto bg-black/50 transition-all duration-300 opacity-0 invisible"])},[(0,l.createElementVNode)("div",n,[(0,l.createElementVNode)("div",i,[(0,l.createElementVNode)("h3",r,(0,l.toDisplayString)(o.item.name),1),(0,l.createElementVNode)("button",{onClick:e[0]||(e[0]=function(t){return _.closeItemModal()}),type:"button",class:"lab-line-circle-cross text-lg text-danger"})]),(0,l.createElementVNode)("form",a,[(0,l.createElementVNode)("div",s,[(0,l.createElementVNode)("div",c,[(0,l.createElementVNode)("label",u,(0,l.toDisplayString)(t.$t("label.tax")),1),(0,l.createVNode)(w,{modelValue:o.item.tax_id,"onUpdate:modelValue":e[1]||(e[1]=function(t){return o.item.tax_id=t}),class:"db-field-control f-b-custom-select",options:_.taxes,"label-by":"name","value-by":"id",closeOnSelect:!0,searchable:!0,clearOnClose:!0,placeholder:t.$t("label.select_one"),"search-placeholder":"--",multiple:!0},null,8,["modelValue","options","placeholder"])]),(0,l.createElementVNode)("div",d,[(0,l.createElementVNode)("label",f,(0,l.toDisplayString)(t.$t("label.quantity")),1),(0,l.withDirectives)((0,l.createElementVNode)("input",{"onUpdate:modelValue":e[2]||(e[2]=function(t){return o.item.quantity=t}),onKeypress:e[3]||(e[3]=function(t){return _.onlyNumber(t)}),min:"1",type:"number",class:"db-field-control"},null,544),[[l.vModelText,o.item.quantity]])]),(0,l.createElementVNode)("div",p,[(0,l.createElementVNode)("label",h,(0,l.toDisplayString)(t.$t("label.discount")),1),(0,l.withDirectives)((0,l.createElementVNode)("input",{"onUpdate:modelValue":e[4]||(e[4]=function(t){return o.item.discount=t}),type:"number",onKeypress:e[5]||(e[5]=function(t){return _.floatNumber(t)}),min:"0",class:"db-field-control"},null,544),[[l.vModelText,o.item.discount]])]),(0,l.createElementVNode)("div",b,[(0,l.createElementVNode)("label",q,(0,l.toDisplayString)(t.$t("label.unit_cost")),1),(0,l.withDirectives)((0,l.createElementVNode)("input",{"onUpdate:modelValue":e[6]||(e[6]=function(t){return o.item.price=t}),type:"number",onKeypress:e[7]||(e[7]=function(t){return _.floatNumber(t)}),min:"0",class:"db-field-control"},null,544),[[l.vModelText,o.item.price]])])]),o.item.is_variation?((0,l.openBlock)(),(0,l.createElementBlock)("div",y,[_.initialVariations.length>0?((0,l.openBlock)(),(0,l.createBlock)(x,{key:0,onMethod:_.selectedVariation,variations:_.initialVariations,mode:o.item.mode,item:o.item},null,8,["onMethod","variations","mode","item"])):(0,l.createCommentVNode)("",!0)])):(0,l.createCommentVNode)("",!0),(0,l.createElementVNode)("div",v,[(0,l.createElementVNode)("button",{disabled:o.item.is_variation&&!k.finalVariation,onClick:e[8]||(e[8]=function(t){return _.submitItem(o.item)}),type:"button",class:"modal-btn-fill disabled:opacity-25"},[e[10]||(e[10]=(0,l.createElementVNode)("i",{class:"fa-solid fa-circle-check"},null,-1)),(0,l.createElementVNode)("span",null,(0,l.toDisplayString)(t.$t("button.save")),1)],8,m),(0,l.createElementVNode)("button",{type:"button",class:"modal-btn-outline modal-close",onClick:e[9]||(e[9]=function(t){return _.closeItemModal()})},[e[11]||(e[11]=(0,l.createElementVNode)("i",{class:"fa-solid fa-circle-xmark"},null,-1)),(0,l.createElementVNode)("span",null,(0,l.toDisplayString)(t.$t("button.close")),1)])])])])],2)}]])},8413:(t,e,o)=>{"use strict";o.d(e,{A:()=>i});var l=o(6314),n=o.n(l)()(function(t){return t[1]});n.push([t.id,".save-btn[data-v-22a5eb67]{margin-top:20px;text-align:end}",""]);const i=n},9399:(t,e,o)=>{"use strict";o.d(e,{A:()=>i});var l=o(6314),n=o.n(l)()(function(t){return t[1]});n.push([t.id,'/*!\n * Quill Editor v1.3.7\n * https://quilljs.com/\n * Copyright (c) 2014, Jason Chen\n * Copyright (c) 2013, salesforce.com\n */.ql-container{box-sizing:border-box;font-family:Helvetica,Arial,sans-serif;font-size:13px;height:100%;margin:0;position:relative}.ql-container.ql-disabled .ql-tooltip{visibility:hidden}.ql-container.ql-disabled .ql-editor ul[data-checked]>li:before{pointer-events:none}.ql-clipboard{height:1px;left:-100000px;overflow-y:hidden;position:absolute;top:50%}.ql-clipboard p{margin:0;padding:0}.ql-editor{word-wrap:break-word;box-sizing:border-box;height:100%;line-height:1.42;outline:none;overflow-y:auto;padding:12px 15px;-o-tab-size:4;tab-size:4;-moz-tab-size:4;text-align:left;white-space:pre-wrap}.ql-editor>*{cursor:text}.ql-editor blockquote,.ql-editor h1,.ql-editor h2,.ql-editor h3,.ql-editor h4,.ql-editor h5,.ql-editor h6,.ql-editor ol,.ql-editor p,.ql-editor pre,.ql-editor ul{counter-reset:list-1 list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9;margin:0;padding:0}.ql-editor ol,.ql-editor ul{padding-left:1.5em}.ql-editor ol>li,.ql-editor ul>li{list-style-type:none}.ql-editor ul>li:before{content:"\\2022"}.ql-editor ul[data-checked=false],.ql-editor ul[data-checked=true]{pointer-events:none}.ql-editor ul[data-checked=false]>li *,.ql-editor ul[data-checked=true]>li *{pointer-events:all}.ql-editor ul[data-checked=false]>li:before,.ql-editor ul[data-checked=true]>li:before{color:#777;cursor:pointer;pointer-events:all}.ql-editor ul[data-checked=true]>li:before{content:"\\2611"}.ql-editor ul[data-checked=false]>li:before{content:"\\2610"}.ql-editor li:before{display:inline-block;white-space:nowrap;width:1.2em}.ql-editor li:not(.ql-direction-rtl):before{margin-left:-1.5em;margin-right:.3em;text-align:right}.ql-editor li.ql-direction-rtl:before{margin-left:.3em;margin-right:-1.5em}.ql-editor ol li:not(.ql-direction-rtl),.ql-editor ul li:not(.ql-direction-rtl){padding-left:1.5em}.ql-editor ol li.ql-direction-rtl,.ql-editor ul li.ql-direction-rtl{padding-right:1.5em}.ql-editor ol li{counter-increment:list-0;counter-reset:list-1 list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9}.ql-editor ol li:before{content:counter(list-0,decimal) ". "}.ql-editor ol li.ql-indent-1{counter-increment:list-1}.ql-editor ol li.ql-indent-1:before{content:counter(list-1,lower-alpha) ". "}.ql-editor ol li.ql-indent-1{counter-reset:list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9}.ql-editor ol li.ql-indent-2{counter-increment:list-2}.ql-editor ol li.ql-indent-2:before{content:counter(list-2,lower-roman) ". "}.ql-editor ol li.ql-indent-2{counter-reset:list-3 list-4 list-5 list-6 list-7 list-8 list-9}.ql-editor ol li.ql-indent-3{counter-increment:list-3}.ql-editor ol li.ql-indent-3:before{content:counter(list-3,decimal) ". "}.ql-editor ol li.ql-indent-3{counter-reset:list-4 list-5 list-6 list-7 list-8 list-9}.ql-editor ol li.ql-indent-4{counter-increment:list-4}.ql-editor ol li.ql-indent-4:before{content:counter(list-4,lower-alpha) ". "}.ql-editor ol li.ql-indent-4{counter-reset:list-5 list-6 list-7 list-8 list-9}.ql-editor ol li.ql-indent-5{counter-increment:list-5}.ql-editor ol li.ql-indent-5:before{content:counter(list-5,lower-roman) ". "}.ql-editor ol li.ql-indent-5{counter-reset:list-6 list-7 list-8 list-9}.ql-editor ol li.ql-indent-6{counter-increment:list-6}.ql-editor ol li.ql-indent-6:before{content:counter(list-6,decimal) ". "}.ql-editor ol li.ql-indent-6{counter-reset:list-7 list-8 list-9}.ql-editor ol li.ql-indent-7{counter-increment:list-7}.ql-editor ol li.ql-indent-7:before{content:counter(list-7,lower-alpha) ". "}.ql-editor ol li.ql-indent-7{counter-reset:list-8 list-9}.ql-editor ol li.ql-indent-8{counter-increment:list-8}.ql-editor ol li.ql-indent-8:before{content:counter(list-8,lower-roman) ". "}.ql-editor ol li.ql-indent-8{counter-reset:list-9}.ql-editor ol li.ql-indent-9{counter-increment:list-9}.ql-editor ol li.ql-indent-9:before{content:counter(list-9,decimal) ". "}.ql-editor .ql-indent-1:not(.ql-direction-rtl){padding-left:3em}.ql-editor li.ql-indent-1:not(.ql-direction-rtl){padding-left:4.5em}.ql-editor .ql-indent-1.ql-direction-rtl.ql-align-right{padding-right:3em}.ql-editor li.ql-indent-1.ql-direction-rtl.ql-align-right{padding-right:4.5em}.ql-editor .ql-indent-2:not(.ql-direction-rtl){padding-left:6em}.ql-editor li.ql-indent-2:not(.ql-direction-rtl){padding-left:7.5em}.ql-editor .ql-indent-2.ql-direction-rtl.ql-align-right{padding-right:6em}.ql-editor li.ql-indent-2.ql-direction-rtl.ql-align-right{padding-right:7.5em}.ql-editor .ql-indent-3:not(.ql-direction-rtl){padding-left:9em}.ql-editor li.ql-indent-3:not(.ql-direction-rtl){padding-left:10.5em}.ql-editor .ql-indent-3.ql-direction-rtl.ql-align-right{padding-right:9em}.ql-editor li.ql-indent-3.ql-direction-rtl.ql-align-right{padding-right:10.5em}.ql-editor .ql-indent-4:not(.ql-direction-rtl){padding-left:12em}.ql-editor li.ql-indent-4:not(.ql-direction-rtl){padding-left:13.5em}.ql-editor .ql-indent-4.ql-direction-rtl.ql-align-right{padding-right:12em}.ql-editor li.ql-indent-4.ql-direction-rtl.ql-align-right{padding-right:13.5em}.ql-editor .ql-indent-5:not(.ql-direction-rtl){padding-left:15em}.ql-editor li.ql-indent-5:not(.ql-direction-rtl){padding-left:16.5em}.ql-editor .ql-indent-5.ql-direction-rtl.ql-align-right{padding-right:15em}.ql-editor li.ql-indent-5.ql-direction-rtl.ql-align-right{padding-right:16.5em}.ql-editor .ql-indent-6:not(.ql-direction-rtl){padding-left:18em}.ql-editor li.ql-indent-6:not(.ql-direction-rtl){padding-left:19.5em}.ql-editor .ql-indent-6.ql-direction-rtl.ql-align-right{padding-right:18em}.ql-editor li.ql-indent-6.ql-direction-rtl.ql-align-right{padding-right:19.5em}.ql-editor .ql-indent-7:not(.ql-direction-rtl){padding-left:21em}.ql-editor li.ql-indent-7:not(.ql-direction-rtl){padding-left:22.5em}.ql-editor .ql-indent-7.ql-direction-rtl.ql-align-right{padding-right:21em}.ql-editor li.ql-indent-7.ql-direction-rtl.ql-align-right{padding-right:22.5em}.ql-editor .ql-indent-8:not(.ql-direction-rtl){padding-left:24em}.ql-editor li.ql-indent-8:not(.ql-direction-rtl){padding-left:25.5em}.ql-editor .ql-indent-8.ql-direction-rtl.ql-align-right{padding-right:24em}.ql-editor li.ql-indent-8.ql-direction-rtl.ql-align-right{padding-right:25.5em}.ql-editor .ql-indent-9:not(.ql-direction-rtl){padding-left:27em}.ql-editor li.ql-indent-9:not(.ql-direction-rtl){padding-left:28.5em}.ql-editor .ql-indent-9.ql-direction-rtl.ql-align-right{padding-right:27em}.ql-editor li.ql-indent-9.ql-direction-rtl.ql-align-right{padding-right:28.5em}.ql-editor .ql-video{display:block;max-width:100%}.ql-editor .ql-video.ql-align-center{margin:0 auto}.ql-editor .ql-video.ql-align-right{margin:0 0 0 auto}.ql-editor .ql-bg-black{background-color:#000}.ql-editor .ql-bg-red{background-color:#e60000}.ql-editor .ql-bg-orange{background-color:#f90}.ql-editor .ql-bg-yellow{background-color:#ff0}.ql-editor .ql-bg-green{background-color:#008a00}.ql-editor .ql-bg-blue{background-color:#06c}.ql-editor .ql-bg-purple{background-color:#93f}.ql-editor .ql-color-white{color:#fff}.ql-editor .ql-color-red{color:#e60000}.ql-editor .ql-color-orange{color:#f90}.ql-editor .ql-color-yellow{color:#ff0}.ql-editor .ql-color-green{color:#008a00}.ql-editor .ql-color-blue{color:#06c}.ql-editor .ql-color-purple{color:#93f}.ql-editor .ql-font-serif{font-family:Georgia,Times New Roman,serif}.ql-editor .ql-font-monospace{font-family:Monaco,Courier New,monospace}.ql-editor .ql-size-small{font-size:.75em}.ql-editor .ql-size-large{font-size:1.5em}.ql-editor .ql-size-huge{font-size:2.5em}.ql-editor .ql-direction-rtl{direction:rtl;text-align:inherit}.ql-editor .ql-align-center{text-align:center}.ql-editor .ql-align-justify{text-align:justify}.ql-editor .ql-align-right{text-align:right}.ql-editor.ql-blank:before{color:rgba(0,0,0,.6);content:attr(data-placeholder);font-style:italic;left:15px;pointer-events:none;position:absolute;right:15px}.ql-bubble .ql-toolbar:after,.ql-bubble.ql-toolbar:after{clear:both;content:"";display:table}.ql-bubble .ql-toolbar button,.ql-bubble.ql-toolbar button{background:none;border:none;cursor:pointer;display:inline-block;float:left;height:24px;padding:3px 5px;width:28px}.ql-bubble .ql-toolbar button svg,.ql-bubble.ql-toolbar button svg{float:left;height:100%}.ql-bubble .ql-toolbar button:active:hover,.ql-bubble.ql-toolbar button:active:hover{outline:none}.ql-bubble .ql-toolbar input.ql-image[type=file],.ql-bubble.ql-toolbar input.ql-image[type=file]{display:none}.ql-bubble .ql-toolbar .ql-picker-item.ql-selected,.ql-bubble .ql-toolbar .ql-picker-item:hover,.ql-bubble .ql-toolbar .ql-picker-label.ql-active,.ql-bubble .ql-toolbar .ql-picker-label:hover,.ql-bubble .ql-toolbar button.ql-active,.ql-bubble .ql-toolbar button:focus,.ql-bubble .ql-toolbar button:hover,.ql-bubble.ql-toolbar .ql-picker-item.ql-selected,.ql-bubble.ql-toolbar .ql-picker-item:hover,.ql-bubble.ql-toolbar .ql-picker-label.ql-active,.ql-bubble.ql-toolbar .ql-picker-label:hover,.ql-bubble.ql-toolbar button.ql-active,.ql-bubble.ql-toolbar button:focus,.ql-bubble.ql-toolbar button:hover{color:#fff}.ql-bubble .ql-toolbar .ql-picker-item.ql-selected .ql-fill,.ql-bubble .ql-toolbar .ql-picker-item.ql-selected .ql-stroke.ql-fill,.ql-bubble .ql-toolbar .ql-picker-item:hover .ql-fill,.ql-bubble .ql-toolbar .ql-picker-item:hover .ql-stroke.ql-fill,.ql-bubble .ql-toolbar .ql-picker-label.ql-active .ql-fill,.ql-bubble .ql-toolbar .ql-picker-label.ql-active .ql-stroke.ql-fill,.ql-bubble .ql-toolbar .ql-picker-label:hover .ql-fill,.ql-bubble .ql-toolbar .ql-picker-label:hover .ql-stroke.ql-fill,.ql-bubble .ql-toolbar button.ql-active .ql-fill,.ql-bubble .ql-toolbar button.ql-active .ql-stroke.ql-fill,.ql-bubble .ql-toolbar button:focus .ql-fill,.ql-bubble .ql-toolbar button:focus .ql-stroke.ql-fill,.ql-bubble .ql-toolbar button:hover .ql-fill,.ql-bubble .ql-toolbar button:hover .ql-stroke.ql-fill,.ql-bubble.ql-toolbar .ql-picker-item.ql-selected .ql-fill,.ql-bubble.ql-toolbar .ql-picker-item.ql-selected .ql-stroke.ql-fill,.ql-bubble.ql-toolbar .ql-picker-item:hover .ql-fill,.ql-bubble.ql-toolbar .ql-picker-item:hover .ql-stroke.ql-fill,.ql-bubble.ql-toolbar .ql-picker-label.ql-active .ql-fill,.ql-bubble.ql-toolbar .ql-picker-label.ql-active .ql-stroke.ql-fill,.ql-bubble.ql-toolbar .ql-picker-label:hover .ql-fill,.ql-bubble.ql-toolbar .ql-picker-label:hover .ql-stroke.ql-fill,.ql-bubble.ql-toolbar button.ql-active .ql-fill,.ql-bubble.ql-toolbar button.ql-active .ql-stroke.ql-fill,.ql-bubble.ql-toolbar button:focus .ql-fill,.ql-bubble.ql-toolbar button:focus .ql-stroke.ql-fill,.ql-bubble.ql-toolbar button:hover .ql-fill,.ql-bubble.ql-toolbar button:hover .ql-stroke.ql-fill{fill:#fff}.ql-bubble .ql-toolbar .ql-picker-item.ql-selected .ql-stroke,.ql-bubble .ql-toolbar .ql-picker-item.ql-selected .ql-stroke-miter,.ql-bubble .ql-toolbar .ql-picker-item:hover .ql-stroke,.ql-bubble .ql-toolbar .ql-picker-item:hover .ql-stroke-miter,.ql-bubble .ql-toolbar .ql-picker-label.ql-active .ql-stroke,.ql-bubble .ql-toolbar .ql-picker-label.ql-active .ql-stroke-miter,.ql-bubble .ql-toolbar .ql-picker-label:hover .ql-stroke,.ql-bubble .ql-toolbar .ql-picker-label:hover .ql-stroke-miter,.ql-bubble .ql-toolbar button.ql-active .ql-stroke,.ql-bubble .ql-toolbar button.ql-active .ql-stroke-miter,.ql-bubble .ql-toolbar button:focus .ql-stroke,.ql-bubble .ql-toolbar button:focus .ql-stroke-miter,.ql-bubble .ql-toolbar button:hover .ql-stroke,.ql-bubble .ql-toolbar button:hover .ql-stroke-miter,.ql-bubble.ql-toolbar .ql-picker-item.ql-selected .ql-stroke,.ql-bubble.ql-toolbar .ql-picker-item.ql-selected .ql-stroke-miter,.ql-bubble.ql-toolbar .ql-picker-item:hover .ql-stroke,.ql-bubble.ql-toolbar .ql-picker-item:hover .ql-stroke-miter,.ql-bubble.ql-toolbar .ql-picker-label.ql-active .ql-stroke,.ql-bubble.ql-toolbar .ql-picker-label.ql-active .ql-stroke-miter,.ql-bubble.ql-toolbar .ql-picker-label:hover .ql-stroke,.ql-bubble.ql-toolbar .ql-picker-label:hover .ql-stroke-miter,.ql-bubble.ql-toolbar button.ql-active .ql-stroke,.ql-bubble.ql-toolbar button.ql-active .ql-stroke-miter,.ql-bubble.ql-toolbar button:focus .ql-stroke,.ql-bubble.ql-toolbar button:focus .ql-stroke-miter,.ql-bubble.ql-toolbar button:hover .ql-stroke,.ql-bubble.ql-toolbar button:hover .ql-stroke-miter{stroke:#fff}@media (pointer:coarse){.ql-bubble .ql-toolbar button:hover:not(.ql-active),.ql-bubble.ql-toolbar button:hover:not(.ql-active){color:#ccc}.ql-bubble .ql-toolbar button:hover:not(.ql-active) .ql-fill,.ql-bubble .ql-toolbar button:hover:not(.ql-active) .ql-stroke.ql-fill,.ql-bubble.ql-toolbar button:hover:not(.ql-active) .ql-fill,.ql-bubble.ql-toolbar button:hover:not(.ql-active) .ql-stroke.ql-fill{fill:#ccc}.ql-bubble .ql-toolbar button:hover:not(.ql-active) .ql-stroke,.ql-bubble .ql-toolbar button:hover:not(.ql-active) .ql-stroke-miter,.ql-bubble.ql-toolbar button:hover:not(.ql-active) .ql-stroke,.ql-bubble.ql-toolbar button:hover:not(.ql-active) .ql-stroke-miter{stroke:#ccc}}.ql-bubble,.ql-bubble *{box-sizing:border-box}.ql-bubble .ql-hidden{display:none}.ql-bubble .ql-out-bottom,.ql-bubble .ql-out-top{visibility:hidden}.ql-bubble .ql-tooltip{position:absolute;transform:translateY(10px)}.ql-bubble .ql-tooltip a{cursor:pointer;text-decoration:none}.ql-bubble .ql-tooltip.ql-flip{transform:translateY(-10px)}.ql-bubble .ql-formats{display:inline-block;vertical-align:middle}.ql-bubble .ql-formats:after{clear:both;content:"";display:table}.ql-bubble .ql-stroke{fill:none;stroke:#ccc;stroke-linecap:round;stroke-linejoin:round;stroke-width:2}.ql-bubble .ql-stroke-miter{fill:none;stroke:#ccc;stroke-miterlimit:10;stroke-width:2}.ql-bubble .ql-fill,.ql-bubble .ql-stroke.ql-fill{fill:#ccc}.ql-bubble .ql-empty{fill:none}.ql-bubble .ql-even{fill-rule:evenodd}.ql-bubble .ql-stroke.ql-thin,.ql-bubble .ql-thin{stroke-width:1}.ql-bubble .ql-transparent{opacity:.4}.ql-bubble .ql-direction svg:last-child{display:none}.ql-bubble .ql-direction.ql-active svg:last-child{display:inline}.ql-bubble .ql-direction.ql-active svg:first-child{display:none}.ql-bubble .ql-editor h1{font-size:2em}.ql-bubble .ql-editor h2{font-size:1.5em}.ql-bubble .ql-editor h3{font-size:1.17em}.ql-bubble .ql-editor h4{font-size:1em}.ql-bubble .ql-editor h5{font-size:.83em}.ql-bubble .ql-editor h6{font-size:.67em}.ql-bubble .ql-editor a{text-decoration:underline}.ql-bubble .ql-editor blockquote{border-left:4px solid #ccc;margin-bottom:5px;margin-top:5px;padding-left:16px}.ql-bubble .ql-editor code,.ql-bubble .ql-editor pre{background-color:#f0f0f0;border-radius:3px}.ql-bubble .ql-editor pre{margin-bottom:5px;margin-top:5px;padding:5px 10px;white-space:pre-wrap}.ql-bubble .ql-editor code{font-size:85%;padding:2px 4px}.ql-bubble .ql-editor pre.ql-syntax{background-color:#23241f;color:#f8f8f2;overflow:visible}.ql-bubble .ql-editor img{max-width:100%}.ql-bubble .ql-picker{color:#ccc;display:inline-block;float:left;font-size:14px;font-weight:500;height:24px;position:relative;vertical-align:middle}.ql-bubble .ql-picker-label{cursor:pointer;display:inline-block;height:100%;padding-left:8px;padding-right:2px;position:relative;width:100%}.ql-bubble .ql-picker-label:before{display:inline-block;line-height:22px}.ql-bubble .ql-picker-options{background-color:#444;display:none;min-width:100%;padding:4px 8px;position:absolute;white-space:nowrap}.ql-bubble .ql-picker-options .ql-picker-item{cursor:pointer;display:block;padding-bottom:5px;padding-top:5px}.ql-bubble .ql-picker.ql-expanded .ql-picker-label{color:#777;z-index:2}.ql-bubble .ql-picker.ql-expanded .ql-picker-label .ql-fill{fill:#777}.ql-bubble .ql-picker.ql-expanded .ql-picker-label .ql-stroke{stroke:#777}.ql-bubble .ql-picker.ql-expanded .ql-picker-options{display:block;margin-top:-1px;top:100%;z-index:1}.ql-bubble .ql-color-picker,.ql-bubble .ql-icon-picker{width:28px}.ql-bubble .ql-color-picker .ql-picker-label,.ql-bubble .ql-icon-picker .ql-picker-label{padding:2px 4px}.ql-bubble .ql-color-picker .ql-picker-label svg,.ql-bubble .ql-icon-picker .ql-picker-label svg{right:4px}.ql-bubble .ql-icon-picker .ql-picker-options{padding:4px 0}.ql-bubble .ql-icon-picker .ql-picker-item{height:24px;padding:2px 4px;width:24px}.ql-bubble .ql-color-picker .ql-picker-options{padding:3px 5px;width:152px}.ql-bubble .ql-color-picker .ql-picker-item{border:1px solid transparent;float:left;height:16px;margin:2px;padding:0;width:16px}.ql-bubble .ql-picker:not(.ql-color-picker):not(.ql-icon-picker) svg{margin-top:-9px;position:absolute;right:0;top:50%;width:18px}.ql-bubble .ql-picker.ql-font .ql-picker-item[data-label]:not([data-label=""]):before,.ql-bubble .ql-picker.ql-font .ql-picker-label[data-label]:not([data-label=""]):before,.ql-bubble .ql-picker.ql-header .ql-picker-item[data-label]:not([data-label=""]):before,.ql-bubble .ql-picker.ql-header .ql-picker-label[data-label]:not([data-label=""]):before,.ql-bubble .ql-picker.ql-size .ql-picker-item[data-label]:not([data-label=""]):before,.ql-bubble .ql-picker.ql-size .ql-picker-label[data-label]:not([data-label=""]):before{content:attr(data-label)}.ql-bubble .ql-picker.ql-header{width:98px}.ql-bubble .ql-picker.ql-header .ql-picker-item:before,.ql-bubble .ql-picker.ql-header .ql-picker-label:before{content:"Normal"}.ql-bubble .ql-picker.ql-header .ql-picker-item[data-value="1"]:before,.ql-bubble .ql-picker.ql-header .ql-picker-label[data-value="1"]:before{content:"Heading 1"}.ql-bubble .ql-picker.ql-header .ql-picker-item[data-value="2"]:before,.ql-bubble .ql-picker.ql-header .ql-picker-label[data-value="2"]:before{content:"Heading 2"}.ql-bubble .ql-picker.ql-header .ql-picker-item[data-value="3"]:before,.ql-bubble .ql-picker.ql-header .ql-picker-label[data-value="3"]:before{content:"Heading 3"}.ql-bubble .ql-picker.ql-header .ql-picker-item[data-value="4"]:before,.ql-bubble .ql-picker.ql-header .ql-picker-label[data-value="4"]:before{content:"Heading 4"}.ql-bubble .ql-picker.ql-header .ql-picker-item[data-value="5"]:before,.ql-bubble .ql-picker.ql-header .ql-picker-label[data-value="5"]:before{content:"Heading 5"}.ql-bubble .ql-picker.ql-header .ql-picker-item[data-value="6"]:before,.ql-bubble .ql-picker.ql-header .ql-picker-label[data-value="6"]:before{content:"Heading 6"}.ql-bubble .ql-picker.ql-header .ql-picker-item[data-value="1"]:before{font-size:2em}.ql-bubble .ql-picker.ql-header .ql-picker-item[data-value="2"]:before{font-size:1.5em}.ql-bubble .ql-picker.ql-header .ql-picker-item[data-value="3"]:before{font-size:1.17em}.ql-bubble .ql-picker.ql-header .ql-picker-item[data-value="4"]:before{font-size:1em}.ql-bubble .ql-picker.ql-header .ql-picker-item[data-value="5"]:before{font-size:.83em}.ql-bubble .ql-picker.ql-header .ql-picker-item[data-value="6"]:before{font-size:.67em}.ql-bubble .ql-picker.ql-font{width:108px}.ql-bubble .ql-picker.ql-font .ql-picker-item:before,.ql-bubble .ql-picker.ql-font .ql-picker-label:before{content:"Sans Serif"}.ql-bubble .ql-picker.ql-font .ql-picker-item[data-value=serif]:before,.ql-bubble .ql-picker.ql-font .ql-picker-label[data-value=serif]:before{content:"Serif"}.ql-bubble .ql-picker.ql-font .ql-picker-item[data-value=monospace]:before,.ql-bubble .ql-picker.ql-font .ql-picker-label[data-value=monospace]:before{content:"Monospace"}.ql-bubble .ql-picker.ql-font .ql-picker-item[data-value=serif]:before{font-family:Georgia,Times New Roman,serif}.ql-bubble .ql-picker.ql-font .ql-picker-item[data-value=monospace]:before{font-family:Monaco,Courier New,monospace}.ql-bubble .ql-picker.ql-size{width:98px}.ql-bubble .ql-picker.ql-size .ql-picker-item:before,.ql-bubble .ql-picker.ql-size .ql-picker-label:before{content:"Normal"}.ql-bubble .ql-picker.ql-size .ql-picker-item[data-value=small]:before,.ql-bubble .ql-picker.ql-size .ql-picker-label[data-value=small]:before{content:"Small"}.ql-bubble .ql-picker.ql-size .ql-picker-item[data-value=large]:before,.ql-bubble .ql-picker.ql-size .ql-picker-label[data-value=large]:before{content:"Large"}.ql-bubble .ql-picker.ql-size .ql-picker-item[data-value=huge]:before,.ql-bubble .ql-picker.ql-size .ql-picker-label[data-value=huge]:before{content:"Huge"}.ql-bubble .ql-picker.ql-size .ql-picker-item[data-value=small]:before{font-size:10px}.ql-bubble .ql-picker.ql-size .ql-picker-item[data-value=large]:before{font-size:18px}.ql-bubble .ql-picker.ql-size .ql-picker-item[data-value=huge]:before{font-size:32px}.ql-bubble .ql-color-picker.ql-background .ql-picker-item{background-color:#fff}.ql-bubble .ql-color-picker.ql-color .ql-picker-item{background-color:#000}.ql-bubble .ql-toolbar .ql-formats{margin:8px 12px 8px 0}.ql-bubble .ql-toolbar .ql-formats:first-child{margin-left:12px}.ql-bubble .ql-color-picker svg{margin:1px}.ql-bubble .ql-color-picker .ql-picker-item.ql-selected,.ql-bubble .ql-color-picker .ql-picker-item:hover{border-color:#fff}.ql-bubble .ql-tooltip{background-color:#444;border-radius:25px;color:#fff}.ql-bubble .ql-tooltip-arrow{border-left:6px solid transparent;border-right:6px solid transparent;content:" ";display:block;left:50%;margin-left:-6px;position:absolute}.ql-bubble .ql-tooltip:not(.ql-flip) .ql-tooltip-arrow{border-bottom:6px solid #444;top:-6px}.ql-bubble .ql-tooltip.ql-flip .ql-tooltip-arrow{border-top:6px solid #444;bottom:-6px}.ql-bubble .ql-tooltip.ql-editing .ql-tooltip-editor{display:block}.ql-bubble .ql-tooltip.ql-editing .ql-formats{visibility:hidden}.ql-bubble .ql-tooltip-editor{display:none}.ql-bubble .ql-tooltip-editor input[type=text]{background:transparent;border:none;color:#fff;font-size:13px;height:100%;outline:none;padding:10px 20px;position:absolute;width:100%}.ql-bubble .ql-tooltip-editor a{position:absolute;right:20px;top:10px}.ql-bubble .ql-tooltip-editor a:before{color:#ccc;content:"\\D7";font-size:16px;font-weight:700}.ql-container.ql-bubble:not(.ql-disabled) a{position:relative;white-space:nowrap}.ql-container.ql-bubble:not(.ql-disabled) a:before{background-color:#444;border-radius:15px;color:#fff;content:attr(href);font-size:12px;font-weight:400;overflow:hidden;padding:5px 15px;text-decoration:none;top:-5px;z-index:1}.ql-container.ql-bubble:not(.ql-disabled) a:after{border-left:6px solid transparent;border-right:6px solid transparent;border-top:6px solid #444;content:" ";height:0;top:0;width:0}.ql-container.ql-bubble:not(.ql-disabled) a:after,.ql-container.ql-bubble:not(.ql-disabled) a:before{left:0;margin-left:50%;position:absolute;transform:translate(-50%,-100%);transition:visibility 0s ease .2s;visibility:hidden}.ql-container.ql-bubble:not(.ql-disabled) a:hover:after,.ql-container.ql-bubble:not(.ql-disabled) a:hover:before{visibility:visible}',""]);const i=n},9590:(t,e,o)=>{"use strict";o.d(e,{A:()=>a});var l=o(9726),n={class:"db-table-action delete"},i={class:"db-tooltip"};const r={name:"SmIconDeleteComponent"};const a=(0,o(6262).A)(r,[["render",function(t,e,o,r,a,s){return(0,l.openBlock)(),(0,l.createElementBlock)("button",n,[e[0]||(e[0]=(0,l.createElementVNode)("i",{class:"lab lab-line-trash"},null,-1)),(0,l.createElementVNode)("span",i,(0,l.toDisplayString)(t.$t("button.delete")),1)])}]])},9639:(t,e,o)=>{"use strict";o.d(e,{A:()=>a});var l=o(9726),n={class:"db-tooltip"};var i=o(5457);const r={name:"SmIconSidebarModalEditComponent",data:function(){return{openCanvas:(0,i.y)().openCanvas}}};const a=(0,o(6262).A)(r,[["render",function(t,e,o,i,r,a){return(0,l.openBlock)(),(0,l.createElementBlock)("button",{class:"db-table-action edit",onClick:e[0]||(e[0]=(0,l.withModifiers)(function(t){return r.openCanvas("sidebar")},["prevent"]))},[e[1]||(e[1]=(0,l.createElementVNode)("i",{class:"lab lab-line-edit"},null,-1)),(0,l.createElementVNode)("span",n,(0,l.toDisplayString)(t.$t("button.edit")),1)])}]])}}]);