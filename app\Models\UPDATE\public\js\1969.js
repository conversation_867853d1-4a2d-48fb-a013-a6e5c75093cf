"use strict";(self.webpackChunk=self.webpackChunk||[]).push([[1969],{970:(e,t,r)=>{r.d(t,{A:()=>l});const l=Object.freeze({PAID:5,UNPAID:10})},1969:(e,t,r)=>{r.r(t),r.d(t,{default:()=>Zr});var l=r(9726),a={class:"col-12"},n={class:"db-card p-4"},o={class:"flex flex-wrap gap-y-5 items-end justify-between"},s={class:"flex flex-wrap items-start gap-y-2 gap-x-6 mb-5"},i={class:"text-2xl font-medium"},c={class:"text-heading"},d={class:"flex items-center gap-2 mt-1.5"},p={class:"flex flex-col gap-2"},m={class:"flex items-center gap-2"},u={class:"text-xs"},y={class:"text-xs"},g={class:"text-heading"},b={class:"text-xs"},h={class:"text-heading"},E={key:0,class:"flex flex-wrap gap-3"},x={class:"text-sm capitalize text-white"},f={key:1,class:"flex flex-wrap gap-3"},v={class:"relative"},N=["value"],V={key:0,class:"relative"},k=["value"],S={key:1,class:"relative"},A=["value"],B={class:"col-12 sm:col-6"},w={class:"row"},D={class:"col-12"},C={class:"db-card"},_={class:"db-card-header"},$={class:"db-card-title"},O={class:"db-card-body"},I={class:"pl-3"},P={class:"flex items-center gap-3 relative"},T={class:"absolute top-5 ltr:-left-3 rtl:-right-3 text-sm w-[26px] h-[26px] leading-[26px] text-center rounded-full text-white bg-heading"},z=["src"],L={class:"flex-auto overflow-hidden"},U={class:"text-sm overflow-hidden"},j={class:"flex flex-wrap items-center justify-between gap-4"},F={class:"flex items-center gap-8"},R={class:"text-sm font-semibold"},M={key:0,class:"col-12"},H={class:"db-card"},q={class:"db-card-header"},W={class:"db-card-title"},Y={class:"db-card-body"},G={class:"col-12 sm:col-6"},K={class:"row"},J={class:"col-12"},X={class:"db-card p-1"},Q={class:"flex flex-col gap-2 p-3 border-b border-dashed border-[#EFF0F6]"},Z={class:"flex items-center justify-between text-heading"},ee={class:"text-sm leading-6 capitalize"},te={class:"text-sm leading-6 capitalize"},re={class:"flex items-center justify-between text-heading"},le={class:"text-sm leading-6 capitalize"},ae={class:"text-sm leading-6 capitalize"},ne={class:"flex items-center justify-between text-heading"},oe={class:"text-sm leading-6 capitalize"},se={class:"text-sm leading-6 capitalize"},ie={class:"flex items-center justify-between text-heading"},ce={class:"text-sm leading-6 capitalize"},de={class:"text-sm leading-6 capitalize font-semibold text-[#1AB759]"},pe={class:"flex items-center justify-between p-3"},me={class:"text-sm leading-6 font-bold capitalize"},ue={class:"text-sm leading-6 font-bold capitalize"},ye={class:"col-12"},ge={class:"db-card"},be={class:"db-card-header"},he={key:0,class:"db-card-title"},Ee={key:1,class:"db-card-title"},xe={class:"db-card-body"},fe={class:"flex items-center gap-3 mb-4"},ve=["src"],Ne={class:"font-semibold text-sm capitalize text-[#374151]"},Ve={class:"flex flex-col gap-3 py-4 border-t border-[#EFF0F6]"},ke={key:0,class:"flex items-center gap-2.5"},Se={class:"text-xs"},Ae={key:1,class:"flex items-center gap-2.5"},Be={class:"text-xs",dir:"ltr"},we={class:"flex items-center gap-2.5"},De={class:"text-xs"},Ce={key:0},_e={key:1},$e={key:2},Oe={key:3},Ie={key:4},Pe={key:1,class:"col-12"},Te={class:"db-card"},ze={class:"db-card-header"},Le={class:"db-card-title"},Ue={class:"db-card-body"},je={class:"flex items-center gap-3 mb-4"},Fe=["src"],Re={class:"font-semibold text-sm capitalize text-[#374151]"},Me={class:"flex flex-col gap-3 py-4 border-t border-[#EFF0F6]"},He={key:0,class:"flex items-center gap-2.5"},qe={class:"text-xs"},We={key:1,class:"flex items-center gap-2.5"},Ye={class:"text-xs",dir:"ltr"},Ge={class:"flex items-center gap-2.5"},Ke={class:"text-xs"},Je={key:0},Xe={key:1},Qe={key:2},Ze={key:3},et={key:4};var tt=r(5475),rt=r(8655),lt=r(970),at=r(5570),nt=r(908),ot=r(1978),st=r(9856),it={class:"text-sm capitalize text-white"},ct={id:"reasonModal",class:"modal"},dt={class:"modal-dialog"},pt={class:"modal-header"},mt={class:"modal-title"},ut={class:"modal-body"},yt={class:"form-row"},gt={class:"form-col-12"},bt={for:"name",class:"db-field-title required"},ht={key:0,class:"db-field-alert"},Et={class:"form-col-12"},xt={class:"modal-btns"},ft={type:"submit",class:"db-btn py-2 text-white bg-primary"};const vt={name:"OnlineOrderReasonComponent",components:{LoadingComponent:tt.A},data:function(){return{loading:{isActive:!1},form:{reason:""},error:""}},methods:{reasonModal:function(){rt.A.modalShow("#reasonModal")},resetModal:function(){rt.A.modalHide("#reasonModal"),this.form.reason="",this.error=""},rejectOrder:function(){var e=this;try{this.loading.isActive=!0,this.$store.dispatch("onlineOrder/changeStatus",{id:this.$route.params.id,status:nt.A.REJECTED,reason:this.form.reason}).then(function(t){e.loading.isActive=!1,rt.A.modalHide(),e.form={reason:""},e.error="",st.A.successFlip(1,e.$t("label.status"))}).catch(function(t){e.loading.isActive=!1,e.error=t.response.data.message})}catch(e){this.loading.isActive=!1,st.A.error(e.response.data.message)}}}};var Nt=r(6262);const Vt=(0,Nt.A)(vt,[["render",function(e,t,r,a,n,o){var s=(0,l.resolveComponent)("LoadingComponent");return(0,l.openBlock)(),(0,l.createElementBlock)(l.Fragment,null,[(0,l.createVNode)(s,{props:n.loading},null,8,["props"]),(0,l.createElementVNode)("button",{type:"button",onClick:t[0]||(t[0]=function(){return o.reasonModal&&o.reasonModal.apply(o,arguments)}),"data-modal":"#reasonModal",class:"flex items-center justify-center text-white gap-2 px-4 h-[38px] rounded shadow-db-card bg-[#FB4E4E]"},[t[5]||(t[5]=(0,l.createElementVNode)("i",{class:"lab lab-fill-close-circle"},null,-1)),(0,l.createElementVNode)("span",it,(0,l.toDisplayString)(e.$t("button.reject")),1)]),(0,l.createElementVNode)("div",ct,[(0,l.createElementVNode)("div",dt,[(0,l.createElementVNode)("div",pt,[(0,l.createElementVNode)("h3",mt,(0,l.toDisplayString)(e.$t("label.reason")),1),(0,l.createElementVNode)("button",{class:"modal-close fa-solid fa-xmark text-xl text-slate-400 hover:text-red-500",onClick:t[1]||(t[1]=(0,l.withModifiers)(function(){return o.resetModal&&o.resetModal.apply(o,arguments)},["prevent"]))})]),(0,l.createElementVNode)("div",ut,[(0,l.createElementVNode)("form",{onSubmit:t[4]||(t[4]=(0,l.withModifiers)(function(){return o.rejectOrder&&o.rejectOrder.apply(o,arguments)},["prevent"])),class:"w-full d-block"},[(0,l.createElementVNode)("div",yt,[(0,l.createElementVNode)("div",gt,[(0,l.createElementVNode)("label",bt,(0,l.toDisplayString)(e.$t("label.reason")),1),(0,l.withDirectives)((0,l.createElementVNode)("input",{"onUpdate:modelValue":t[2]||(t[2]=function(e){return n.form.reason=e}),class:(0,l.normalizeClass)([n.error?"invalid":"","db-field-control"]),type:"text",id:"name"},null,2),[[l.vModelText,n.form.reason]]),n.error?((0,l.openBlock)(),(0,l.createElementBlock)("small",ht,(0,l.toDisplayString)(n.error),1)):(0,l.createCommentVNode)("",!0)]),(0,l.createElementVNode)("div",Et,[(0,l.createElementVNode)("div",xt,[(0,l.createElementVNode)("button",{type:"button",class:"modal-btn-outline modal-close",onClick:t[3]||(t[3]=(0,l.withModifiers)(function(){return o.resetModal&&o.resetModal.apply(o,arguments)},["prevent"]))},[t[6]||(t[6]=(0,l.createElementVNode)("i",{class:"lab lab-fill-close-circle"},null,-1)),(0,l.createElementVNode)("span",null,(0,l.toDisplayString)(e.$t("button.close")),1)]),(0,l.createElementVNode)("button",ft,[t[7]||(t[7]=(0,l.createElementVNode)("i",{class:"lab lab-fill-save"},null,-1)),(0,l.createElementVNode)("span",null,(0,l.toDisplayString)(e.$t("button.save")),1)])])])])],32)])])])],64)}]]);var kt={type:"button",class:"flex items-center justify-center gap-2 px-4 h-[38px] rounded shadow-db-card bg-primary"},St={class:"text-sm capitalize text-white"},At={id:"receipt",class:"modal"},Bt={class:"modal-dialog max-w-[302px] rounded-none",id:"print"},wt={class:"modal-body"},Dt={class:"text-center pb-3.5 border-b border-dashed border-gray-400"},Ct={class:"font-bold mb-1"},_t={class:"text-sm font-normal"},$t={class:"text-sm font-normal"},Ot={class:"w-full my-1.5"},It={class:"text-xs text-left py-0.5 text-heading"},Pt={class:"text-xs text-left py-0.5 text-heading"},Tt={class:"text-xs text-right py-0.5 text-heading"},zt={class:"w-full"},Lt={class:"border-t border-b border-dashed border-gray-400"},Ut={scope:"col",class:"py-1 font-normal text-xs capitalize text-left text-heading w-8"},jt={scope:"col",class:"py-1 font-normal text-xs capitalize flex items-center justify-between text-heading"},Ft={class:"border-b border-dashed border-gray-400"},Rt={class:"text-left font-normal align-top py-1"},Mt={class:"text-xs leading-5 text-heading"},Ht={class:"text-left font-normal align-top py-1"},qt={class:"flex items-center justify-between"},Wt={class:"text-xs leading-5 text-heading"},Yt={class:"text-xs leading-5 text-heading"},Gt={key:0,class:"text-xs leading-5 text-heading max-w-[200px]"},Kt={class:"py-2 pl-7"},Jt={class:"w-full"},Xt={class:"text-xs text-left py-0.5 uppercase text-heading"},Qt={class:"text-xs text-right py-0.5 text-heading"},Zt={class:"text-xs text-left py-0.5 uppercase text-heading"},er={class:"text-xs text-right py-0.5 text-heading"},tr={class:"text-xs text-left py-0.5 uppercase text-heading"},rr={class:"text-xs text-right py-0.5 text-heading"},lr={key:0},ar={class:"text-xs text-left py-0.5 uppercase text-heading"},nr={class:"text-xs text-right py-0.5 text-heading"},or={class:"text-xs text-left py-0.5 font-bold uppercase text-heading"},sr={class:"text-xs text-right py-0.5 font-bold text-heading"},ir={class:"text-xs py-1 border-t border-b border-dashed border-gray-400 text-heading"},cr={class:"pt-1 pb-1 pr-1"},dr={class:"pt-1 pb-1"},pr={class:"pt-1 pb-1 pr-1"},mr={class:"pt-1 pb-1"},ur={class:"pt-1 pb-1 pr-1"},yr={class:"pt-1 pb-1"},gr={key:0,class:"text-xs py-1 border-b border-dashed border-gray-400 text-heading"},br={class:"pt-1 pb-1 pr-1"},hr={class:"pt-1 pb-1"},Er={class:"pt-1 pb-1 pr-1"},xr={class:"pt-1 pb-1"},fr={key:0},vr={class:"pt-1 pb-1 pr-1"},Nr={class:"pt-1 pb-1"},Vr={key:0},kr={key:1},Sr={key:2},Ar={key:3},Br={key:4},wr={key:0,class:"text-xs py-1 border-b border-dashed border-gray-400 text-heading"},Dr={class:"pt-1 pb-1 pr-1"},Cr={class:"pt-1 pb-1"},_r={class:"pt-1 pb-1 pr-1"},$r={class:"pt-1 pb-1"},Or={class:"pt-1 pb-1 pr-1"},Ir={class:"pt-1 pb-1"},Pr={key:0},Tr={key:1},zr={key:2},Lr={key:3},Ur={key:4},jr={class:"text-center pt-2 pb-4"},Fr={class:"text-[11px] leading-[14px] capitalize text-heading"},Rr={class:"text-[11px] leading-[14px] capitalize text-heading"},Mr={class:"flex flex-col items-end"},Hr={class:"text-[8px] font-normal text-left w-[46px] leading-[10px]"},qr={class:"text-xs font-normal leading-4"};var Wr=r(5316);function Yr(e){return Yr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Yr(e)}function Gr(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=Yr(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var l=r.call(e,t||"default");if("object"!=Yr(l))return l;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==Yr(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}const Kr={name:"OnlineOrderReceiptComponent",props:{order:Object,orderProducts:Object,orderUser:Object,orderAddress:Object},directives:{print:Wr.A},data:function(){return{loading:{isActive:!1},printObj:{id:"print",popTitle:this.$t("menu.order_receipt")},enums:{orderTypeEnum:ot.A,addressTypeEnum:at.A,orderTypeEnumArray:Gr(Gr({},ot.A.DELIVERY,this.$t("label.delivery")),ot.A.PICK_UP,this.$t("label.pick_up"))}}},mounted:function(){this.$store.dispatch("company/lists").then().catch()},computed:{company:function(){return this.$store.getters["company/lists"]},outletAddress:function(){return this.$store.getters["onlineOrder/outletAddress"]}}};function Jr(e){return Jr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Jr(e)}function Xr(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=Jr(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var l=r.call(e,t||"default");if("object"!=Jr(l))return l;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==Jr(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}const Qr={name:"OnlineOrderShowComponent",components:{OnlineOrderReceiptComponent:(0,Nt.A)(Kr,[["render",function(e,t,r,a,n,o){var s=(0,l.resolveDirective)("print");return(0,l.openBlock)(),(0,l.createElementBlock)(l.Fragment,null,[(0,l.withDirectives)(((0,l.openBlock)(),(0,l.createElementBlock)("button",kt,[t[0]||(t[0]=(0,l.createElementVNode)("i",{class:"lab lab-line-printer lab-font-size-16 text-white"},null,-1)),(0,l.createElementVNode)("span",St,(0,l.toDisplayString)(e.$t("button.print_invoice")),1)])),[[s,n.printObj]]),(0,l.createElementVNode)("div",At,[(0,l.createElementVNode)("div",Bt,[(0,l.createElementVNode)("div",wt,[(0,l.createElementVNode)("div",Dt,[(0,l.createElementVNode)("h3",Ct,(0,l.toDisplayString)(o.company.company_name),1),(0,l.createElementVNode)("h4",_t,(0,l.toDisplayString)(o.company.company_address),1),(0,l.createElementVNode)("h5",$t,(0,l.toDisplayString)(e.$t("label.tel"))+": "+(0,l.toDisplayString)(o.company.company_calling_code)+" "+(0,l.toDisplayString)(o.company.company_phone),1)]),(0,l.createElementVNode)("table",Ot,[(0,l.createElementVNode)("tbody",null,[(0,l.createElementVNode)("tr",null,[(0,l.createElementVNode)("td",It,(0,l.toDisplayString)(e.$t("label.order_id"))+" #"+(0,l.toDisplayString)(r.order.order_serial_no),1)]),(0,l.createElementVNode)("tr",null,[(0,l.createElementVNode)("td",Pt,(0,l.toDisplayString)(r.order.order_date),1),(0,l.createElementVNode)("td",Tt,(0,l.toDisplayString)(r.order.order_time),1)])])]),(0,l.createElementVNode)("table",zt,[(0,l.createElementVNode)("thead",Lt,[(0,l.createElementVNode)("tr",null,[(0,l.createElementVNode)("th",Ut,(0,l.toDisplayString)(e.$t("label.qty")),1),(0,l.createElementVNode)("th",jt,[(0,l.createElementVNode)("span",null,(0,l.toDisplayString)(e.$t("label.product_description")),1),(0,l.createElementVNode)("span",null,(0,l.toDisplayString)(e.$t("label.price")),1)])])]),(0,l.createElementVNode)("tbody",Ft,[r.orderProducts.length>0?((0,l.openBlock)(!0),(0,l.createElementBlock)(l.Fragment,{key:0},(0,l.renderList)(r.orderProducts,function(e){return(0,l.openBlock)(),(0,l.createElementBlock)("tr",{key:e},[(0,l.createElementVNode)("td",Rt,[(0,l.createElementVNode)("p",Mt,(0,l.toDisplayString)(e.quantity),1)]),(0,l.createElementVNode)("td",Ht,[(0,l.createElementVNode)("div",qt,[(0,l.createElementVNode)("p",Wt,(0,l.toDisplayString)(e.product_name),1),(0,l.createElementVNode)("p",Yt,(0,l.toDisplayString)(e.subtotal_currency_price),1)]),e.variation_names?((0,l.openBlock)(),(0,l.createElementBlock)("p",Gt,(0,l.toDisplayString)(e.variation_names),1)):(0,l.createCommentVNode)("",!0),e.product_tax.length>0?((0,l.openBlock)(!0),(0,l.createElementBlock)(l.Fragment,{key:1},(0,l.renderList)(e.product_tax,function(e){return(0,l.openBlock)(),(0,l.createElementBlock)("p",{class:"text-xs leading-5 text-heading",key:e},(0,l.toDisplayString)(e.tax_name)+" ("+(0,l.toDisplayString)(e.tax_rate)+"%) ",1)}),128)):(0,l.createCommentVNode)("",!0)])])}),128)):(0,l.createCommentVNode)("",!0)])]),(0,l.createElementVNode)("div",Kt,[(0,l.createElementVNode)("table",Jt,[(0,l.createElementVNode)("tbody",null,[(0,l.createElementVNode)("tr",null,[(0,l.createElementVNode)("td",Xt,(0,l.toDisplayString)(e.$t("label.subtotal"))+": ",1),(0,l.createElementVNode)("td",Qt,(0,l.toDisplayString)(r.order.subtotal_currency_price),1)]),(0,l.createElementVNode)("tr",null,[(0,l.createElementVNode)("td",Zt,(0,l.toDisplayString)(e.$t("label.tax_fee"))+": ",1),(0,l.createElementVNode)("td",er,(0,l.toDisplayString)(r.order.tax_currency_price),1)]),(0,l.createElementVNode)("tr",null,[(0,l.createElementVNode)("td",tr,(0,l.toDisplayString)(e.$t("label.discount"))+": ",1),(0,l.createElementVNode)("td",rr,(0,l.toDisplayString)(r.order.discount_currency_price),1)]),r.order.order_type===n.enums.orderTypeEnum.DELIVERY?((0,l.openBlock)(),(0,l.createElementBlock)("tr",lr,[(0,l.createElementVNode)("td",ar,(0,l.toDisplayString)(e.$t("label.shipping_charge"))+": ",1),(0,l.createElementVNode)("td",nr,(0,l.toDisplayString)(r.order.shipping_charge_currency_price),1)])):(0,l.createCommentVNode)("",!0),(0,l.createElementVNode)("tr",null,[(0,l.createElementVNode)("td",or,(0,l.toDisplayString)(e.$t("label.total"))+": ",1),(0,l.createElementVNode)("td",sr,(0,l.toDisplayString)(r.order.total_currency_price),1)])])])]),(0,l.createElementVNode)("div",ir,[(0,l.createElementVNode)("table",null,[(0,l.createElementVNode)("tbody",null,[(0,l.createElementVNode)("tr",null,[(0,l.createElementVNode)("td",cr,(0,l.toDisplayString)(e.$t("label.order_type"))+":",1),(0,l.createElementVNode)("td",dr,(0,l.toDisplayString)(n.enums.orderTypeEnumArray[r.order.order_type]),1)]),(0,l.createElementVNode)("tr",null,[(0,l.createElementVNode)("td",pr,(0,l.toDisplayString)(e.$t("label.payment_type"))+":",1),(0,l.createElementVNode)("td",mr,(0,l.toDisplayString)(r.order.payment_method_name),1)]),(0,l.createElementVNode)("tr",null,[(0,l.createElementVNode)("td",ur,(0,l.toDisplayString)(e.$t("label.order_date_time"))+":",1),(0,l.createElementVNode)("td",yr,(0,l.toDisplayString)(r.order.order_datetime),1)])])])]),((0,l.openBlock)(!0),(0,l.createElementBlock)(l.Fragment,null,(0,l.renderList)(r.orderAddress,function(t){return(0,l.openBlock)(),(0,l.createElementBlock)("div",null,[t.address_type===n.enums.addressTypeEnum.SHIPPING?((0,l.openBlock)(),(0,l.createElementBlock)("div",gr,[(0,l.createElementVNode)("table",null,[(0,l.createElementVNode)("tbody",null,[(0,l.createElementVNode)("tr",null,[(0,l.createElementVNode)("td",br,(0,l.toDisplayString)(e.$t("label.customer"))+":",1),(0,l.createElementVNode)("td",hr,(0,l.toDisplayString)(t.full_name),1)]),(0,l.createElementVNode)("tr",null,[(0,l.createElementVNode)("td",Er,(0,l.toDisplayString)(e.$t("label.phone"))+":",1),(0,l.createElementVNode)("td",xr,(0,l.toDisplayString)(t.country_code+""+t.phone),1)]),r.order.order_type===n.enums.orderTypeEnum.DELIVERY?((0,l.openBlock)(),(0,l.createElementBlock)("tr",fr,[(0,l.createElementVNode)("td",vr,(0,l.toDisplayString)(e.$t("label.address"))+":",1),(0,l.createElementVNode)("td",Nr,[t.address?((0,l.openBlock)(),(0,l.createElementBlock)("span",Vr,(0,l.toDisplayString)(t.address)+",",1)):(0,l.createCommentVNode)("",!0),t.city?((0,l.openBlock)(),(0,l.createElementBlock)("span",kr,(0,l.toDisplayString)(t.city)+",",1)):(0,l.createCommentVNode)("",!0),t.state?((0,l.openBlock)(),(0,l.createElementBlock)("span",Sr,(0,l.toDisplayString)(t.state)+",",1)):(0,l.createCommentVNode)("",!0),t.country?((0,l.openBlock)(),(0,l.createElementBlock)("span",Ar,(0,l.toDisplayString)(t.country)+",",1)):(0,l.createCommentVNode)("",!0),t.zip_code?((0,l.openBlock)(),(0,l.createElementBlock)("span",Br,(0,l.toDisplayString)(t.zip_code),1)):(0,l.createCommentVNode)("",!0)])])):(0,l.createCommentVNode)("",!0)])])])):(0,l.createCommentVNode)("",!0)])}),256)),r.order.order_type===n.enums.orderTypeEnum.PICK_UP?((0,l.openBlock)(),(0,l.createElementBlock)("div",wr,[(0,l.createElementVNode)("table",null,[(0,l.createElementVNode)("tbody",null,[(0,l.createElementVNode)("tr",null,[(0,l.createElementVNode)("td",Dr,(0,l.toDisplayString)(e.$t("label.customer"))+":",1),(0,l.createElementVNode)("td",Cr,(0,l.toDisplayString)(r.orderUser.name),1)]),(0,l.createElementVNode)("tr",null,[(0,l.createElementVNode)("td",_r,(0,l.toDisplayString)(e.$t("label.phone"))+":",1),(0,l.createElementVNode)("td",$r,(0,l.toDisplayString)(r.orderUser.country_code+""+r.orderUser.phone),1)]),(0,l.createElementVNode)("tr",null,[(0,l.createElementVNode)("td",Or,(0,l.toDisplayString)(e.$t("label.outlet"))+":",1),(0,l.createElementVNode)("td",Ir,[o.outletAddress.address?((0,l.openBlock)(),(0,l.createElementBlock)("span",Pr,(0,l.toDisplayString)(o.outletAddress.address)+",",1)):(0,l.createCommentVNode)("",!0),o.outletAddress.city?((0,l.openBlock)(),(0,l.createElementBlock)("span",Tr,(0,l.toDisplayString)(o.outletAddress.city)+",",1)):(0,l.createCommentVNode)("",!0),o.outletAddress.state?((0,l.openBlock)(),(0,l.createElementBlock)("span",zr,(0,l.toDisplayString)(o.outletAddress.state)+",",1)):(0,l.createCommentVNode)("",!0),o.outletAddress.country?((0,l.openBlock)(),(0,l.createElementBlock)("span",Lr,(0,l.toDisplayString)(o.outletAddress.country)+",",1)):(0,l.createCommentVNode)("",!0),o.outletAddress.zip_code?((0,l.openBlock)(),(0,l.createElementBlock)("span",Ur,(0,l.toDisplayString)(o.outletAddress.zip_code),1)):(0,l.createCommentVNode)("",!0)])])])])])):(0,l.createCommentVNode)("",!0)]),(0,l.createElementVNode)("div",jr,[(0,l.createElementVNode)("p",Fr,(0,l.toDisplayString)(e.$t("message.thank_you")),1),(0,l.createElementVNode)("p",Rr,(0,l.toDisplayString)(e.$t("message.please_come_again")),1)]),(0,l.createElementVNode)("div",Mr,[(0,l.createElementVNode)("h5",Hr,(0,l.toDisplayString)(e.$t("label.powered_by")),1),(0,l.createElementVNode)("h6",qr,(0,l.toDisplayString)(o.company.company_name),1)])])])],64)}]]),LoadingComponent:tt.A,OnlineOrderReasonComponent:Vt},data:function(){return{loading:{isActive:!1},payment_status:null,delivery_boy:null,order_status:null,enums:{paymentStatusEnum:lt.A,addressTypeEnum:at.A,orderStatusEnum:nt.A,orderTypeEnum:ot.A}}},computed:{order:function(){return this.$store.getters["onlineOrder/show"]},orderProducts:function(){return this.$store.getters["onlineOrder/orderProducts"]},orderUser:function(){return this.$store.getters["onlineOrder/orderUser"]},orderAddress:function(){return this.$store.getters["onlineOrder/orderAddress"]},outletAddress:function(){return this.$store.getters["onlineOrder/outletAddress"]},paymentStatusEnumArray:function(){return Xr(Xr({},lt.A.PAID,this.$t("label.paid")),lt.A.UNPAID,this.$t("label.unpaid"))},paymentStatusObject:function(){return[{name:this.$t("label.paid"),value:lt.A.PAID},{name:this.$t("label.unpaid"),value:lt.A.UNPAID}]},orderStatusEnumArray:function(){return Xr(Xr(Xr(Xr(Xr(Xr({},nt.A.PENDING,this.$t("label.pending")),nt.A.CONFIRMED,this.$t("label.confirmed")),nt.A.ON_THE_WAY,this.$t("label.on_the_way")),nt.A.DELIVERED,this.$t("label.delivered")),nt.A.CANCELED,this.$t("label.canceled")),nt.A.REJECTED,this.$t("label.rejected"))},orderStatusObject:function(){return[{name:this.$t("label.confirmed"),value:nt.A.CONFIRMED},{name:this.$t("label.on_the_way"),value:nt.A.ON_THE_WAY},{name:this.$t("label.delivered"),value:nt.A.DELIVERED}]},orderStatusPickupObject:function(){return[{name:this.$t("label.confirmed"),value:nt.A.CONFIRMED},{name:this.$t("label.delivered"),value:nt.A.DELIVERED}]},orderTypeEnumArray:function(){return Xr(Xr({},ot.A.DELIVERY,this.$t("label.delivery")),ot.A.PICK_UP,this.$t("label.pick_up"))}},mounted:function(){var e=this;this.loading.isActive=!0,this.$store.dispatch("onlineOrder/show",this.$route.params.id).then(function(t){e.payment_status=t.data.data.payment_status,e.order_status=t.data.data.status,e.loading.isActive=!1}).catch(function(t){e.loading.isActive=!1})},methods:{statusClass:function(e){return rt.A.statusClass(e)},orderStatusClass:function(e){return rt.A.orderStatusClass(e)},textShortener:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:30;return rt.A.textShortener(e,t)},changeStatus:function(e){var t=this;rt.A.acceptOrder().then(function(r){try{t.loading.isActive=!0,t.$store.dispatch("onlineOrder/changeStatus",{id:t.$route.params.id,status:e}).then(function(e){t.order_status=e.data.data.status,t.loading.isActive=!1,st.A.successFlip(1,t.$t("label.status"))}).catch(function(e){t.loading.isActive=!1,st.A.error(e.response.data.message)})}catch(e){t.loading.isActive=!1,st.A.error(e.response.data.message)}}).catch(function(e){t.loading.isActive=!1})},changePaymentStatus:function(e){var t=this;try{this.loading.isActive=!0,this.$store.dispatch("onlineOrder/changePaymentStatus",{id:this.$route.params.id,payment_status:e.target.value}).then(function(e){t.loading.isActive=!1,st.A.successFlip(1,t.$t("label.status"))}).catch(function(e){t.loading.isActive=!1,st.A.error(e.response.data.message)})}catch(e){this.loading.isActive=!1,st.A.error(e.response.data.message)}},orderStatus:function(e){var t=this;try{this.loading.isActive=!0,this.$store.dispatch("onlineOrder/changeStatus",{id:this.$route.params.id,status:e.target.value}).then(function(e){t.loading.isActive=!1,st.A.successFlip(1,t.$t("label.status"))}).catch(function(e){t.loading.isActive=!1,st.A.error(e.response.data.message)})}catch(e){this.loading.isActive=!1,st.A.error(e.response.data.message)}}}},Zr=(0,Nt.A)(Qr,[["render",function(e,t,r,tt,rt,lt){var at=(0,l.resolveComponent)("LoadingComponent"),nt=(0,l.resolveComponent)("OnlineOrderReasonComponent"),ot=(0,l.resolveComponent)("OnlineOrderReceiptComponent");return(0,l.openBlock)(),(0,l.createElementBlock)(l.Fragment,null,[(0,l.createVNode)(at,{props:rt.loading},null,8,["props"]),(0,l.createElementVNode)("div",a,[(0,l.createElementVNode)("div",n,[(0,l.createElementVNode)("div",o,[(0,l.createElementVNode)("div",null,[(0,l.createElementVNode)("div",s,[(0,l.createElementVNode)("p",i,[(0,l.createTextVNode)((0,l.toDisplayString)(e.$t("label.order_id"))+": ",1),(0,l.createElementVNode)("span",c," #"+(0,l.toDisplayString)(lt.order.order_serial_no),1)]),(0,l.createElementVNode)("div",d,[(0,l.createElementVNode)("span",{class:(0,l.normalizeClass)("text-sm capitalize h-5 leading-5 px-2 rounded-3xl text-[#FB4E4E] bg-[#FFDADA]"+lt.statusClass(lt.order.payment_status))},(0,l.toDisplayString)(lt.paymentStatusEnumArray[lt.order.payment_status]),3),(0,l.createElementVNode)("span",{class:(0,l.normalizeClass)("text-sm capitalize px-2 rounded-3xl "+lt.orderStatusClass(lt.order.status))},(0,l.toDisplayString)(lt.orderStatusEnumArray[lt.order.status]),3)])]),(0,l.createElementVNode)("ul",p,[(0,l.createElementVNode)("li",m,[t[7]||(t[7]=(0,l.createElementVNode)("i",{class:"lab lab-line-calendar lab-font-size-16"},null,-1)),(0,l.createElementVNode)("span",u,(0,l.toDisplayString)(lt.order.order_datetime),1)]),(0,l.createElementVNode)("li",y,[(0,l.createTextVNode)((0,l.toDisplayString)(e.$t("label.payment_type"))+": ",1),(0,l.createElementVNode)("span",g,(0,l.toDisplayString)(lt.order.payment_method_name),1)]),(0,l.createElementVNode)("li",b,[(0,l.createTextVNode)((0,l.toDisplayString)(e.$t("label.order_type"))+": ",1),(0,l.createElementVNode)("span",h,(0,l.toDisplayString)(lt.orderTypeEnumArray[lt.order.order_type]),1)])])]),lt.order.status===rt.enums.orderStatusEnum.PENDING?((0,l.openBlock)(),(0,l.createElementBlock)("div",E,[(0,l.createVNode)(nt),(0,l.createElementVNode)("button",{type:"button",onClick:t[0]||(t[0]=function(e){return lt.changeStatus(rt.enums.orderStatusEnum.CONFIRMED)}),class:"flex items-center justify-center text-white gap-2 px-4 h-[38px] rounded shadow-db-card bg-[#2AC769]"},[t[8]||(t[8]=(0,l.createElementVNode)("i",{class:"lab lab-fill-save"},null,-1)),(0,l.createElementVNode)("span",x,(0,l.toDisplayString)(e.$t("button.accept")),1)])])):lt.order.status!==rt.enums.orderStatusEnum.REJECTED&&lt.order.status!==rt.enums.orderStatusEnum.CANCELED?((0,l.openBlock)(),(0,l.createElementBlock)("div",f,[(0,l.createElementVNode)("div",v,[(0,l.withDirectives)((0,l.createElementVNode)("select",{"onUpdate:modelValue":t[1]||(t[1]=function(e){return rt.payment_status=e}),onChange:t[2]||(t[2]=function(e){return lt.changePaymentStatus(e)}),class:"text-sm capitalize appearance-none pl-4 pr-10 h-[38px] rounded border border-primary bg-white text-primary"},[((0,l.openBlock)(!0),(0,l.createElementBlock)(l.Fragment,null,(0,l.renderList)(lt.paymentStatusObject,function(e){return(0,l.openBlock)(),(0,l.createElementBlock)("option",{value:e.value},(0,l.toDisplayString)(e.name),9,N)}),256))],544),[[l.vModelSelect,rt.payment_status]]),t[9]||(t[9]=(0,l.createElementVNode)("i",{class:"lab lab-line-chevron-down lab-font-size-16 absolute top-1/2 right-3.5 -translate-y-1/2 text-primary"},null,-1))]),lt.order.order_type===rt.enums.orderTypeEnum.DELIVERY?((0,l.openBlock)(),(0,l.createElementBlock)("div",V,[(0,l.withDirectives)((0,l.createElementVNode)("select",{"onUpdate:modelValue":t[3]||(t[3]=function(e){return rt.order_status=e}),onChange:t[4]||(t[4]=function(e){return lt.orderStatus(e)}),class:"text-sm capitalize appearance-none pl-4 pr-10 h-[38px] rounded border border-primary bg-white text-primary"},[((0,l.openBlock)(!0),(0,l.createElementBlock)(l.Fragment,null,(0,l.renderList)(lt.orderStatusObject,function(e){return(0,l.openBlock)(),(0,l.createElementBlock)("option",{value:e.value},(0,l.toDisplayString)(e.name),9,k)}),256))],544),[[l.vModelSelect,rt.order_status]]),t[10]||(t[10]=(0,l.createElementVNode)("i",{class:"lab lab-line-chevron-down lab-font-size-16 absolute top-1/2 right-3.5 -translate-y-1/2 text-primary"},null,-1))])):(0,l.createCommentVNode)("",!0),lt.order.order_type===rt.enums.orderTypeEnum.PICK_UP?((0,l.openBlock)(),(0,l.createElementBlock)("div",S,[(0,l.withDirectives)((0,l.createElementVNode)("select",{"onUpdate:modelValue":t[5]||(t[5]=function(e){return rt.order_status=e}),onChange:t[6]||(t[6]=function(e){return lt.orderStatus(e)}),class:"text-sm capitalize appearance-none pl-4 pr-10 h-[38px] rounded border border-primary bg-white text-primary"},[((0,l.openBlock)(!0),(0,l.createElementBlock)(l.Fragment,null,(0,l.renderList)(lt.orderStatusPickupObject,function(e){return(0,l.openBlock)(),(0,l.createElementBlock)("option",{value:e.value},(0,l.toDisplayString)(e.name),9,A)}),256))],544),[[l.vModelSelect,rt.order_status]]),t[11]||(t[11]=(0,l.createElementVNode)("i",{class:"lab lab-line-chevron-down lab-font-size-16 absolute top-1/2 right-3.5 -translate-y-1/2 text-primary"},null,-1))])):(0,l.createCommentVNode)("",!0),(0,l.createVNode)(ot,{order:lt.order,orderProducts:lt.orderProducts,orderUser:lt.orderUser,orderAddress:lt.orderAddress},null,8,["order","orderProducts","orderUser","orderAddress"])])):(0,l.createCommentVNode)("",!0)])])]),(0,l.createElementVNode)("div",B,[(0,l.createElementVNode)("div",w,[(0,l.createElementVNode)("div",D,[(0,l.createElementVNode)("div",C,[(0,l.createElementVNode)("div",_,[(0,l.createElementVNode)("h3",$,(0,l.toDisplayString)(e.$t("label.order_details")),1)]),(0,l.createElementVNode)("div",O,[(0,l.createElementVNode)("div",I,[lt.orderProducts.length>0?((0,l.openBlock)(!0),(0,l.createElementBlock)(l.Fragment,{key:0},(0,l.renderList)(lt.orderProducts,function(e){return(0,l.openBlock)(),(0,l.createElementBlock)("div",{class:"mb-3 pb-3 border-b last:mb-0 last:pb-0 last:border-b-0 border-gray-2",key:e},[(0,l.createElementVNode)("div",P,[(0,l.createElementVNode)("h3",T,(0,l.toDisplayString)(e.quantity),1),(0,l.createElementVNode)("img",{class:"w-16 h-16 rounded-lg flex-shrink-0",src:e.product_image,alt:"thumbnail"},null,8,z),(0,l.createElementVNode)("div",L,[(0,l.createElementVNode)("h4",{class:(0,l.normalizeClass)([e.variation_names?"":"mb-4","text-sm capitalize whitespace-nowrap overflow-hidden text-ellipsis"])},(0,l.toDisplayString)(e.product_name),3),(0,l.createElementVNode)("p",U,(0,l.toDisplayString)(e.variation_names),1),(0,l.createElementVNode)("div",j,[(0,l.createElementVNode)("div",F,[(0,l.createElementVNode)("span",R,(0,l.toDisplayString)(e.subtotal_currency_price),1)])])])])])}),128)):(0,l.createCommentVNode)("",!0)])])])]),lt.order.status===rt.enums.orderStatusEnum.REJECTED?((0,l.openBlock)(),(0,l.createElementBlock)("div",M,[(0,l.createElementVNode)("div",H,[(0,l.createElementVNode)("div",q,[(0,l.createElementVNode)("h3",W,(0,l.toDisplayString)(e.$t("label.reason")),1)]),(0,l.createElementVNode)("div",Y,[(0,l.createElementVNode)("p",null,(0,l.toDisplayString)(lt.order.reason),1)])])])):(0,l.createCommentVNode)("",!0)])]),(0,l.createElementVNode)("div",G,[(0,l.createElementVNode)("div",K,[(0,l.createElementVNode)("div",J,[(0,l.createElementVNode)("div",X,[(0,l.createElementVNode)("ul",Q,[(0,l.createElementVNode)("li",Z,[(0,l.createElementVNode)("span",ee,(0,l.toDisplayString)(e.$t("label.subtotal")),1),(0,l.createElementVNode)("span",te,(0,l.toDisplayString)(lt.order.subtotal_currency_price),1)]),(0,l.createElementVNode)("li",re,[(0,l.createElementVNode)("span",le,(0,l.toDisplayString)(e.$t("label.tax_fee")),1),(0,l.createElementVNode)("span",ae,(0,l.toDisplayString)(lt.order.tax_currency_price),1)]),(0,l.createElementVNode)("li",ne,[(0,l.createElementVNode)("span",oe,(0,l.toDisplayString)(e.$t("label.discount")),1),(0,l.createElementVNode)("span",se,(0,l.toDisplayString)(lt.order.discount_currency_price),1)]),(0,l.createElementVNode)("li",ie,[(0,l.createElementVNode)("span",ce,(0,l.toDisplayString)(e.$t("label.shipping_charge")),1),(0,l.createElementVNode)("span",de,(0,l.toDisplayString)(lt.order.shipping_charge_currency_price),1)])]),(0,l.createElementVNode)("div",pe,[(0,l.createElementVNode)("h4",me,(0,l.toDisplayString)(e.$t("label.total")),1),(0,l.createElementVNode)("h5",ue,(0,l.toDisplayString)(lt.order.total_currency_price),1)])])]),lt.order.order_type===rt.enums.orderTypeEnum.DELIVERY&&lt.orderAddress.length>0?((0,l.openBlock)(!0),(0,l.createElementBlock)(l.Fragment,{key:0},(0,l.renderList)(lt.orderAddress,function(r){return(0,l.openBlock)(),(0,l.createElementBlock)("div",ye,[(0,l.createElementVNode)("div",ge,[(0,l.createElementVNode)("div",be,[r.address_type===rt.enums.addressTypeEnum.SHIPPING?((0,l.openBlock)(),(0,l.createElementBlock)("h3",he,(0,l.toDisplayString)(e.$t("label.shipping_address")),1)):(0,l.createCommentVNode)("",!0),r.address_type===rt.enums.addressTypeEnum.BILLING?((0,l.openBlock)(),(0,l.createElementBlock)("h3",Ee,(0,l.toDisplayString)(e.$t("label.billing_address")),1)):(0,l.createCommentVNode)("",!0)]),(0,l.createElementVNode)("div",xe,[(0,l.createElementVNode)("div",fe,[(0,l.createElementVNode)("img",{class:"w-8 rounded-full",alt:"avatar",src:lt.orderUser.image},null,8,ve),(0,l.createElementVNode)("h4",Ne,(0,l.toDisplayString)(lt.textShortener(r.full_name,20)),1)]),(0,l.createElementVNode)("ul",Ve,[r.email?((0,l.openBlock)(),(0,l.createElementBlock)("li",ke,[t[12]||(t[12]=(0,l.createElementVNode)("i",{class:"lab lab-line-mail lab-font-size-14"},null,-1)),(0,l.createElementVNode)("span",Se,(0,l.toDisplayString)(r.email),1)])):(0,l.createCommentVNode)("",!0),r.phone?((0,l.openBlock)(),(0,l.createElementBlock)("li",Ae,[t[13]||(t[13]=(0,l.createElementVNode)("i",{class:"lab lab-line-call-calling lab-font-size-14"},null,-1)),(0,l.createElementVNode)("span",Be,(0,l.toDisplayString)(r.country_code+""+r.phone),1)])):(0,l.createCommentVNode)("",!0),(0,l.createElementVNode)("li",we,[t[14]||(t[14]=(0,l.createElementVNode)("i",{class:"lab lab-line-location lab-font-size-14"},null,-1)),(0,l.createElementVNode)("span",De,[r.address?((0,l.openBlock)(),(0,l.createElementBlock)("span",Ce,(0,l.toDisplayString)(r.address)+",",1)):(0,l.createCommentVNode)("",!0),r.city?((0,l.openBlock)(),(0,l.createElementBlock)("span",_e,(0,l.toDisplayString)(r.city)+",",1)):(0,l.createCommentVNode)("",!0),r.state?((0,l.openBlock)(),(0,l.createElementBlock)("span",$e,(0,l.toDisplayString)(r.state)+",",1)):(0,l.createCommentVNode)("",!0),r.country?((0,l.openBlock)(),(0,l.createElementBlock)("span",Oe,(0,l.toDisplayString)(r.country)+",",1)):(0,l.createCommentVNode)("",!0),r.zip_code?((0,l.openBlock)(),(0,l.createElementBlock)("span",Ie,(0,l.toDisplayString)(r.zip_code),1)):(0,l.createCommentVNode)("",!0)])])])])])])}),256)):lt.order.order_type===rt.enums.orderTypeEnum.PICK_UP?((0,l.openBlock)(),(0,l.createElementBlock)("div",Pe,[(0,l.createElementVNode)("div",Te,[(0,l.createElementVNode)("div",ze,[(0,l.createElementVNode)("h3",Le,(0,l.toDisplayString)(e.$t("label.pick_up_address")),1)]),(0,l.createElementVNode)("div",Ue,[(0,l.createElementVNode)("div",je,[(0,l.createElementVNode)("img",{class:"w-8 rounded-full",alt:"avatar",src:lt.orderUser.image},null,8,Fe),(0,l.createElementVNode)("h4",Re,(0,l.toDisplayString)(lt.textShortener(lt.orderUser.name,20)),1)]),(0,l.createElementVNode)("ul",Me,[lt.orderUser.email?((0,l.openBlock)(),(0,l.createElementBlock)("li",He,[t[15]||(t[15]=(0,l.createElementVNode)("i",{class:"lab lab-line-mail lab-font-size-14"},null,-1)),(0,l.createElementVNode)("span",qe,(0,l.toDisplayString)(lt.orderUser.email),1)])):(0,l.createCommentVNode)("",!0),lt.orderUser.phone?((0,l.openBlock)(),(0,l.createElementBlock)("li",We,[t[16]||(t[16]=(0,l.createElementVNode)("i",{class:"lab lab-line-call-calling lab-font-size-14"},null,-1)),(0,l.createElementVNode)("span",Ye,(0,l.toDisplayString)(lt.orderUser.country_code+""+lt.orderUser.phone),1)])):(0,l.createCommentVNode)("",!0),(0,l.createElementVNode)("li",Ge,[t[17]||(t[17]=(0,l.createElementVNode)("i",{class:"lab lab-line-location lab-font-size-14"},null,-1)),(0,l.createElementVNode)("span",Ke,[lt.outletAddress.address?((0,l.openBlock)(),(0,l.createElementBlock)("span",Je,(0,l.toDisplayString)(lt.outletAddress.address)+",",1)):(0,l.createCommentVNode)("",!0),lt.outletAddress.city?((0,l.openBlock)(),(0,l.createElementBlock)("span",Xe,(0,l.toDisplayString)(lt.outletAddress.city)+",",1)):(0,l.createCommentVNode)("",!0),lt.outletAddress.state?((0,l.openBlock)(),(0,l.createElementBlock)("span",Qe,(0,l.toDisplayString)(lt.outletAddress.state)+",",1)):(0,l.createCommentVNode)("",!0),lt.outletAddress.country?((0,l.openBlock)(),(0,l.createElementBlock)("span",Ze,(0,l.toDisplayString)(lt.outletAddress.country)+",",1)):(0,l.createCommentVNode)("",!0),lt.outletAddress.zip_code?((0,l.openBlock)(),(0,l.createElementBlock)("span",et,(0,l.toDisplayString)(lt.outletAddress.zip_code),1)):(0,l.createCommentVNode)("",!0)])])])])])])):(0,l.createCommentVNode)("",!0)])])],64)}]])},5316:(e,t,r)=>{r.d(t,{A:()=>n});class l{constructor(e){this.standards={strict:"strict",loose:"loose",html5:"html5"},this.previewBody=null,this.close=null,this.previewBodyUtilPrintBtn=null,this.selectArray=[],this.counter=0,this.settings={standard:this.standards.html5},Object.assign(this.settings,e),this.init()}init(){this.counter++,this.settings.id=`printArea_${this.counter}`;let e="";this.settings.url&&!this.settings.asyncUrl&&(e=this.settings.url);let t=this;if(this.settings.asyncUrl)return void t.settings.asyncUrl(function(e){let r=t.getPrintWindow(e);t.settings.preview?t.previewIfrmaeLoad():t.print(r)},t.settings.vue);let r=this.getPrintWindow(e);this.settings.url||this.write(r.doc),this.settings.preview?this.previewIfrmaeLoad():this.print(r)}addEvent(e,t,r){e.addEventListener?e.addEventListener(t,r,!1):e.attachEvent?e.attachEvent("on"+t,r):e["on"+t]=r}previewIfrmaeLoad(){let e=document.getElementById("vue-pirnt-nb-previewBox");if(e){let t=this,r=e.querySelector("iframe");this.settings.previewBeforeOpenCallback(),this.addEvent(r,"load",function(){t.previewBoxShow(),t.removeCanvasImg(),t.settings.previewOpenCallback()}),this.addEvent(e.querySelector(".previewBodyUtilPrintBtn"),"click",function(){t.settings.beforeOpenCallback(),t.settings.openCallback(),r.contentWindow.print(),t.settings.closeCallback()})}}removeCanvasImg(){let e=this;try{if(e.elsdom){let t=e.elsdom.querySelectorAll(".canvasImg");for(let e=0;e<t.length;e++)t[e].remove()}}catch(e){console.log(e)}}print(e){var t=this;let r=document.getElementById(this.settings.id)||e.f,l=document.getElementById(this.settings.id).contentWindow||e.f.contentWindow;t.settings.beforeOpenCallback(),t.addEvent(r,"load",function(){l.focus(),t.settings.openCallback(),l.print(),r.remove(),t.settings.closeCallback(),t.removeCanvasImg()})}write(e){e.open(),e.write(`${this.docType()}<html>${this.getHead()}${this.getBody()}</html>`),e.close()}docType(){return this.settings.standard===this.standards.html5?"<!DOCTYPE html>":`<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01${this.settings.standard===this.standards.loose?" Transitional":""}//EN" "http://www.w3.org/TR/html4/${this.settings.standard===this.standards.loose?"loose":"strict"}.dtd">`}getHead(){let e="",t="",r="";this.settings.extraHead&&this.settings.extraHead.replace(/([^,]+)/g,t=>{e+=t}),[].forEach.call(document.querySelectorAll("link"),function(e){e.href.indexOf(".css")>=0&&(t+=`<link type="text/css" rel="stylesheet" href="${e.href}" >`)});let l=document.styleSheets;if(l&&l.length>0)for(let e=0;e<l.length;e++)try{if(l[e].cssRules||l[e].rules){let t=l[e].cssRules||l[e].rules;for(let e=0;e<t.length;e++)r+=t[e].cssText}}catch(t){console.log(l[e].href+t)}return this.settings.extraCss&&this.settings.extraCss.replace(/([^,\s]+)/g,e=>{t+=`<link type="text/css" rel="stylesheet" href="${e}">`}),`<head><title>${this.settings.popTitle}</title>${e}${t}<style type="text/css">${r}</style></head>`}getBody(){let e=this.settings.ids;return e=e.replace(new RegExp("#","g"),""),this.elsdom=this.beforeHanler(document.getElementById(e)),"<body>"+this.getFormData(this.elsdom).outerHTML+"</body>"}beforeHanler(e){let t=e.querySelectorAll("canvas");for(let e=0;e<t.length;e++)if(!t[e].style.display){let r=t[e].parentNode,l=t[e].toDataURL("image/png"),a=new Image;a.className="canvasImg",a.style.display="none",a.src=l,r.appendChild(a)}return e}getFormData(e){let t=e.cloneNode(!0),r=t.querySelectorAll("input,select,textarea"),l=t.querySelectorAll(".canvasImg,canvas"),a=-1;for(let e=0;e<l.length;e++){let t=l[e].parentNode,r=l[e];"canvas"===r.tagName.toLowerCase()?t.removeChild(r):r.style.display="block"}for(let t=0;t<r.length;t++){let l=r[t],n=l.getAttribute("type"),o=r[t];if(n||(n="SELECT"===l.tagName?"select":"TEXTAREA"===l.tagName?"textarea":""),"INPUT"===l.tagName)"radio"===n||"checkbox"===n?l.checked&&o.setAttribute("checked",l.checked):(o.value=l.value,o.setAttribute("value",l.value));else if("select"===n){a++;for(let t=0;t<e.querySelectorAll("select").length;t++){let r=e.querySelectorAll("select")[t];if(!r.getAttribute("newbs")&&r.setAttribute("newbs",t),r.getAttribute("newbs")==a){let t=e.querySelectorAll("select")[a].selectedIndex;l.options[t].setAttribute("selected",!0)}}}else o.innerHTML=l.value,o.setAttribute("html",l.value)}return t}getPrintWindow(e){var t=this.Iframe(e);return{f:t,win:t.contentWindow||t,doc:t.doc}}previewBoxShow(){let e=document.getElementById("vue-pirnt-nb-previewBox");e&&(document.querySelector("html").setAttribute("style","overflow: hidden"),e.style.display="block")}previewBoxHide(){let e=document.getElementById("vue-pirnt-nb-previewBox");e&&(document.querySelector("html").setAttribute("style","overflow: visible;"),e.querySelector("iframe")&&e.querySelector("iframe").remove(),e.style.display="none")}previewBox(){let e=document.getElementById("vue-pirnt-nb-previewBox");if(e)return e.querySelector("iframe")&&e.querySelector("iframe").remove(),{close:e.querySelector(".previewClose"),previewBody:e.querySelector(".previewBody")};let t=document.createElement("div");t.setAttribute("id","vue-pirnt-nb-previewBox"),t.setAttribute("style","position: fixed;top: 0px;left: 0px;width: 100%;height: 100%;background: white;display:none"),t.style.zIndex=this.settings.zIndex;let r=document.createElement("div");r.setAttribute("class","previewHeader"),r.setAttribute("style","padding: 5px 20px;"),r.innerHTML=this.settings.previewTitle,t.appendChild(r),this.close=document.createElement("div");let l=this.close;l.setAttribute("class","previewClose"),l.setAttribute("style","position: absolute;top: 5px;right: 20px;width: 25px;height: 20px;cursor: pointer;");let a=document.createElement("div"),n=document.createElement("div");a.setAttribute("class","closeBefore"),a.setAttribute("style","position: absolute;width: 3px;height: 100%;background: #040404;transform: rotate(45deg); top: 0px;left: 50%;"),n.setAttribute("class","closeAfter"),n.setAttribute("style","position: absolute;width: 3px;height: 100%;background: #040404;transform: rotate(-45deg); top: 0px;left: 50%;"),l.appendChild(a),l.appendChild(n),r.appendChild(l),this.previewBody=document.createElement("div");let o=this.previewBody;o.setAttribute("class","previewBody"),o.setAttribute("style","display: flex;flex-direction: column; height: 100%;"),t.appendChild(o);let s=document.createElement("div");s.setAttribute("class","previewBodyUtil"),s.setAttribute("style","height: 32px;background: #474747;position: relative;"),o.appendChild(s),this.previewBodyUtilPrintBtn=document.createElement("div");let i=this.previewBodyUtilPrintBtn;return i.setAttribute("class","previewBodyUtilPrintBtn"),i.innerHTML=this.settings.previewPrintBtnLabel,i.setAttribute("style","position: absolute;padding: 2px 10px;margin-top: 3px;left: 24px;font-size: 14px;color: white;cursor: pointer;background-color: rgba(0,0,0,.12);background-image: linear-gradient(hsla(0,0%,100%,.05),hsla(0,0%,100%,0));background-clip: padding-box;border: 1px solid rgba(0,0,0,.35);border-color: rgba(0,0,0,.32) rgba(0,0,0,.38) rgba(0,0,0,.42);box-shadow: inset 0 1px 0 hsla(0,0%,100%,.05), inset 0 0 1px hsla(0,0%,100%,.15), 0 1px 0 hsla(0,0%,100%,.05);"),s.appendChild(i),document.body.appendChild(t),{close:this.close,previewBody:this.previewBody}}iframeBox(e,t){let r=document.createElement("iframe");return r.style.border="0px",r.style.position="absolute",r.style.width="0px",r.style.height="0px",r.style.right="0px",r.style.top="0px",r.setAttribute("id",e),r.setAttribute("src",t),r}Iframe(e){let t=this.settings.id;e=e||(new Date).getTime();let r=this,l=this.iframeBox(t,e);try{if(this.settings.preview){l.setAttribute("style","border: 0px;flex: 1;");let e=this.previewBox(),t=e.previewBody,a=e.close;t.appendChild(l),this.addEvent(a,"click",function(){r.previewBoxHide()})}else document.body.appendChild(l);l.doc=null,l.doc=l.contentDocument?l.contentDocument:l.contentWindow?l.contentWindow.document:l.document}catch(e){throw new Error(e+". iframes may not be supported in this browser.")}if(null==l.doc)throw new Error("Cannot find document.");return l}}var a={directiveName:"print",mounted(e,t,r){let a=t.instance,n="";var o,s,i;s="click",i=()=>{if("string"==typeof t.value)n=t.value;else{if("object"!=typeof t.value||!t.value.id)return void window.print();{n=t.value.id;let e=n.replace(new RegExp("#","g"),"");document.getElementById(e)||(console.log("id in Error"),n="")}}c()},(o=e).addEventListener?o.addEventListener(s,i,!1):o.attachEvent?o.attachEvent("on"+s,i):o["on"+s]=i;const c=()=>{new l({ids:n,vue:a,url:t.value.url,standard:"",extraHead:t.value.extraHead,extraCss:t.value.extraCss,zIndex:t.value.zIndex||20002,previewTitle:t.value.previewTitle||"打印预览",previewPrintBtnLabel:t.value.previewPrintBtnLabel||"打印",popTitle:t.value.popTitle,preview:t.value.preview||!1,asyncUrl:t.value.asyncUrl,previewBeforeOpenCallback(){t.value.previewBeforeOpenCallback&&t.value.previewBeforeOpenCallback(a)},previewOpenCallback(){t.value.previewOpenCallback&&t.value.previewOpenCallback(a)},openCallback(){t.value.openCallback&&t.value.openCallback(a)},closeCallback(){t.value.closeCallback&&t.value.closeCallback(a)},beforeOpenCallback(){t.value.beforeOpenCallback&&t.value.beforeOpenCallback(a)}})}},install:function(e){e.directive("print",a)}};const n=a},5570:(e,t,r)=>{r.d(t,{A:()=>l});const l=Object.freeze({SHIPPING:5,BILLING:10})}}]);