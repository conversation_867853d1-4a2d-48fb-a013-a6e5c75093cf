"use strict";(self.webpackChunk=self.webpackChunk||[]).push([[1008],{1008:(e,t,a)=>{a.r(t),a.d(t,{default:()=>j});var r=a(9726),n={class:"capitalize text-2xl font-bold mb-7 text-primary"},o={class:"rounded-2xl shadow-card bg-white mobile:mb-20"},l={class:"max-md:overflow-x-auto"},i={class:"w-full text-left text-sm capitalize"},s={class:"font-semibold border-b-2 border-gray-200"},c={class:"p-4"},d={class:"p-4"},u={class:"p-4"},p={class:"p-4"},g={class:"p-4"},m={key:0,class:"font-medium"},h={class:"p-4 border-t border-gray-100"},f={class:"font-semibold mb-1"},b={class:"text-xs text-text"},y={class:"p-4 border-t border-gray-100"},P={class:"p-4 border-t border-gray-100"},v={class:"p-4 border-t border-gray-100"},x={class:"p-4 border-t border-gray-100"},E={key:1,class:"db-table-body"},N={class:"db-table-body-tr"},k={class:"db-table-body-td text-center",colspan:"5"},V={class:"p-4"},_={class:"max-w-[300px] mx-auto mt-2"},S=["src"],A={class:"d-block mt-3 text-lg"},C={key:0,class:"px-4 py-6 border-t border-gray-100"},B={class:"hidden sm:flex sm:flex-1 sm:items-center sm:justify-between"};var D=a(1811),$=a(4290),R=a(8655),w=a(6079),U=a(3301);function T(e){return T="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},T(e)}function L(e,t,a){return(t=function(e){var t=function(e,t){if("object"!=T(e)||!e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var r=a.call(e,t||"default");if("object"!=T(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==T(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}const O={name:"ReturnOrdersComponent",components:{LoadingComponent:D.A,PaginationComponent:w.A,PaginationTextComponent:U.A},data:function(){return{loading:{isActive:!1},enums:{returnStatusEnum:$.A,returnStatusEnumArray:L(L(L({},$.A.PENDING,this.$t("label.pending")),$.A.ACCEPT,this.$t("label.accepted")),$.A.REJECTED,this.$t("label.rejected"))},search:{paginate:1,page:1,per_page:10,order_column:"id",order_by:"desc"}}},mounted:function(){this.list()},computed:{setting:function(){return this.$store.getters["frontendSetting/lists"]},returnOrders:function(){return this.$store.getters["frontendReturnAndRefund/lists"]},pagination:function(){return this.$store.getters["frontendReturnAndRefund/pagination"]},paginationPage:function(){return this.$store.getters["frontendReturnAndRefund/page"]}},methods:{returnStatusClass:function(e){return R.A.returnStatusClass(e)},list:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;this.loading.isActive=!0,this.search.page=t,this.$store.dispatch("frontendReturnAndRefund/lists",{search:this.search}).then(function(t){e.loading.isActive=!1}).catch(function(t){e.loading.isActive=!1})}}};const j=(0,a(6262).A)(O,[["render",function(e,t,a,D,$,R){var w=(0,r.resolveComponent)("LoadingComponent"),U=(0,r.resolveComponent)("RouterLink"),T=(0,r.resolveComponent)("PaginationTextComponent"),L=(0,r.resolveComponent)("PaginationComponent");return(0,r.openBlock)(),(0,r.createElementBlock)(r.Fragment,null,[(0,r.createVNode)(w,{props:$.loading},null,8,["props"]),(0,r.createElementVNode)("h2",n,(0,r.toDisplayString)(e.$t("label.return_orders")),1),(0,r.createElementVNode)("div",o,[(0,r.createElementVNode)("div",l,[(0,r.createElementVNode)("table",i,[(0,r.createElementVNode)("thead",s,[(0,r.createElementVNode)("tr",null,[(0,r.createElementVNode)("th",c,(0,r.toDisplayString)(e.$t("label.order_id")),1),(0,r.createElementVNode)("th",d,(0,r.toDisplayString)(e.$t("label.products")),1),(0,r.createElementVNode)("th",u,(0,r.toDisplayString)(e.$t("label.status")),1),(0,r.createElementVNode)("th",p,(0,r.toDisplayString)(e.$t("label.amount")),1),(0,r.createElementVNode)("th",g,(0,r.toDisplayString)(e.$t("label.action")),1)])]),R.returnOrders.length>0?((0,r.openBlock)(),(0,r.createElementBlock)("tbody",m,[((0,r.openBlock)(!0),(0,r.createElementBlock)(r.Fragment,null,(0,r.renderList)(R.returnOrders,function(a){return(0,r.openBlock)(),(0,r.createElementBlock)("tr",{key:a},[(0,r.createElementVNode)("td",h,[(0,r.createElementVNode)("h5",f,(0,r.toDisplayString)(a.order_serial_no),1),(0,r.createElementVNode)("p",b,(0,r.toDisplayString)(a.order_datetime),1)]),(0,r.createElementVNode)("td",y,(0,r.toDisplayString)(a.return_items)+" "+(0,r.toDisplayString)(e.$t("label.product")),1),(0,r.createElementVNode)("td",P,[(0,r.createElementVNode)("span",{class:(0,r.normalizeClass)(R.returnStatusClass(a.status))},(0,r.toDisplayString)($.enums.returnStatusEnumArray[a.status]),3)]),(0,r.createElementVNode)("td",v,(0,r.toDisplayString)(a.return_total_currency_price),1),(0,r.createElementVNode)("td",x,[(0,r.createVNode)(U,{to:{name:"frontend.account.returnOrder.details",params:{id:a.id}}},{default:(0,r.withCtx)(function(){return t[0]||(t[0]=[(0,r.createElementVNode)("i",{class:"lab-fill-eye w-[30px] h-[30px] text-center rounded-lg text-white bg-primary shadow-action before:mt-1 lab-font-size-16"},null,-1)])}),_:2,__:[0]},1032,["to"])])])}),128))])):((0,r.openBlock)(),(0,r.createElementBlock)("tbody",E,[(0,r.createElementVNode)("tr",N,[(0,r.createElementVNode)("td",k,[(0,r.createElementVNode)("div",V,[(0,r.createElementVNode)("div",_,[(0,r.createElementVNode)("img",{class:"w-full h-full",src:R.setting.not_found,alt:"Not Found"},null,8,S)]),(0,r.createElementVNode)("span",A,(0,r.toDisplayString)(e.$t("message.no_data_found")),1)])])])]))])]),R.returnOrders.length>0?((0,r.openBlock)(),(0,r.createElementBlock)("div",C,[(0,r.createElementVNode)("div",B,[(0,r.createVNode)(T,{props:{page:R.paginationPage}},null,8,["props"]),(0,r.createVNode)(L,{onPaginationChangePage:R.list,data:R.pagination,limit:1,"keep-length":!1},null,8,["onPaginationChangePage","data"])])])):(0,r.createCommentVNode)("",!0)])],64)}]])},3301:(e,t,a)=>{a.d(t,{A:()=>l});var r=a(9726),n={class:"first-letter:capitalize"};const o={name:"PaginationTextComponent",props:["props"]};const l=(0,a(6262).A)(o,[["render",function(e,t,a,o,l,i){var s,c;return(0,r.openBlock)(),(0,r.createElementBlock)("p",n,[(0,r.createTextVNode)((0,r.toDisplayString)(e.$t("label.showing"))+" ",1),(0,r.createElementVNode)("b",null,(0,r.toDisplayString)(null!==(s=a.props.page.from)&&void 0!==s?s:0),1),(0,r.createTextVNode)(" "+(0,r.toDisplayString)(e.$t("label.to"))+" ",1),(0,r.createElementVNode)("b",null,(0,r.toDisplayString)(null!==(c=a.props.page.to)&&void 0!==c?c:0),1),(0,r.createTextVNode)(" "+(0,r.toDisplayString)(e.$t("label.of"))+" ",1),(0,r.createElementVNode)("b",null,(0,r.toDisplayString)(a.props.page.total),1),(0,r.createTextVNode)(" "+(0,r.toDisplayString)(e.$t("label.results")),1)])}]])},6079:(e,t,a)=>{a.d(t,{A:()=>s});var r=a(9726),n=["disabled"],o=["aria-current"],l=["disabled"];const i={name:"PaginationComponent",inheritAttrs:!1,emits:["pagination-change-page"],components:{RenderLessPagination:{emits:["pagination-change-page"],props:{data:{type:Object,default:()=>{}},limit:{type:Number,default:0},keepLength:{type:Boolean,default:!1}},computed:{isApiResource(){return!!this.data.meta},currentPage(){return this.isApiResource?this.data.meta.current_page:this.data.current_page??null},firstPageUrl(){return this.data.first_page_url??this.data.meta?.first_page_url??this.data.links?.first??null},from(){return this.isApiResource?this.data.meta.from:this.data.from??null},lastPage(){return this.isApiResource?this.data.meta.last_page:this.data.last_page??null},lastPageUrl(){return this.data.last_page_url??this.data.meta?.last_page_url??this.data.links?.last??null},nextPageUrl(){return this.data.next_page_url??this.data.meta?.next_page_url??this.data.links?.next??null},perPage(){return this.isApiResource?this.data.meta.per_page:this.data.per_page??null},prevPageUrl(){return this.data.prev_page_url??this.data.meta?.prev_page_url??this.data.links?.prev??null},to(){return this.isApiResource?this.data.meta.to:this.data.to??null},total(){return this.isApiResource?this.data.meta.total:this.data.total??null},pageRange(){if(-1===this.limit)return 0;if(0===this.limit)return this.lastPage;for(var e,t=this.currentPage,a=this.keepLength,r=this.lastPage,n=this.limit,o=t-n,l=t+n,i=2*(n+2),s=2*(n+2)-1,c=[],d=[],u=1;u<=r;u++)(1===u||u===r||u>=o&&u<=l||a&&u<i&&t<i-2||a&&u>r-s&&t>r-s+2)&&c.push(u);return c.forEach(function(t){e&&(t-e===2?d.push(e+1):t-e!==1&&d.push("...")),d.push(t),e=t}),d}},methods:{previousPage(){this.selectPage(this.currentPage-1)},nextPage(){this.selectPage(this.currentPage+1)},selectPage(e){"..."!==e&&e!==this.currentPage&&this.$emit("pagination-change-page",e)}},render(){return this.$slots.default({data:this.data,limit:this.limit,computed:{isApiResource:this.isApiResource,currentPage:this.currentPage,firstPageUrl:this.firstPageUrl,from:this.from,lastPage:this.lastPage,lastPageUrl:this.lastPageUrl,nextPageUrl:this.nextPageUrl,perPage:this.perPage,prevPageUrl:this.prevPageUrl,to:this.to,total:this.total,pageRange:this.pageRange},prevButtonEvents:{click:e=>{e.preventDefault(),this.previousPage()}},nextButtonEvents:{click:e=>{e.preventDefault(),this.nextPage()}},pageButtonEvents:e=>({click:t=>{t.preventDefault(),this.selectPage(e)}})})}}},props:{data:{type:Object,default:function(){}},limit:{type:Number,default:0},keepLength:{type:Boolean,default:!1}},data:function(){return{activeClass:["bg-primary","text-white"]}},methods:{onPaginationChangePage:function(e){this.$emit("pagination-change-page",e)}}};const s=(0,a(6262).A)(i,[["render",function(e,t,a,i,s,c){var d=(0,r.resolveComponent)("RenderLessPagination");return(0,r.openBlock)(),(0,r.createBlock)(d,{data:a.data,limit:a.limit,"keep-length":a.keepLength,onPaginationChangePage:c.onPaginationChangePage},{default:(0,r.withCtx)(function(t){return[t.computed.total>t.computed.perPage?((0,r.openBlock)(),(0,r.createElementBlock)("nav",(0,r.mergeProps)({key:0},e.$attrs,{"aria-label":"Pagination",class:"flex items-center justify-center gap-4 mobile:mb-12"}),[(0,r.createElementVNode)("button",(0,r.mergeProps)({disabled:!t.computed.prevPageUrl,class:t.computed.prevPageUrl?"hover:text-white hover:bg-primary":""},(0,r.toHandlers)(t.prevButtonEvents,!0),{class:"h-10 leading-10 px-4 rounded-full font-medium capitalize bg-gray-100 transition-all duration-500"}),[(0,r.renderSlot)(e.$slots,"prev-nav",{},function(){return[(0,r.createTextVNode)((0,r.toDisplayString)(e.$t("label.previous")),1)]})],16,n),((0,r.openBlock)(!0),(0,r.createElementBlock)(r.Fragment,null,(0,r.renderList)(t.computed.pageRange,function(e,a){return(0,r.openBlock)(),(0,r.createElementBlock)("button",(0,r.mergeProps)({class:t.computed.currentPage===e?"bg-primary text-white":"","aria-current":t.computed.currentPage?"page":null,key:a},(0,r.toHandlers)(t.pageButtonEvents(e),!0),{class:"w-10 h-10 leading-10 rounded-full font-medium capitalize text-center transition-all duration-500 hover:text-white hover:bg-primary hidden sm:block bg-gray-100"}),(0,r.toDisplayString)(e),17,o)}),128)),(0,r.createElementVNode)("button",(0,r.mergeProps)({disabled:!t.computed.nextPageUrl,class:t.computed.nextPageUrl?"hover:text-white hover:bg-primary":""},(0,r.toHandlers)(t.nextButtonEvents,!0),{class:"h-10 leading-10 px-4 rounded-full font-medium capitalize bg-gray-100 transition-all duration-500"}),[(0,r.renderSlot)(e.$slots,"next-nav",{},function(){return[(0,r.createTextVNode)((0,r.toDisplayString)(e.$t("label.next")),1)]})],16,l)],16)):(0,r.createCommentVNode)("",!0)]}),_:3},8,["data","limit","keep-length","onPaginationChangePage"])}]])}}]);