"use strict";(self.webpackChunk=self.webpackChunk||[]).push([[5201],{5201:(e,t,a)=>{a.r(t),a.d(t,{default:()=>r});var n=a(9726),s={class:"relative"},l={class:"capitalize text-lg font-bold mb-1"},o={class:"text-sm font-medium capitalize text-text"};const c={name:"HeaderComponent"};const r=(0,a(6262).A)(c,[["render",function(e,t,a,c,r,m){return(0,n.openBlock)(),(0,n.createElementBlock)("dl",s,[(0,n.createElementVNode)("dt",l,(0,n.toDisplayString)(e.$t("message.payment_information")),1),(0,n.createElementVNode)("dd",o,(0,n.toDisplayString)(e.$t("message.select_your_payment_method")),1)])}]])}}]);