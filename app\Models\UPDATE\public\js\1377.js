"use strict";(self.webpackChunk=self.webpackChunk||[]).push([[1377],{970:(e,t,r)=>{r.d(t,{A:()=>l});const l=Object.freeze({PAID:5,UNPAID:10})},1377:(e,t,r)=>{r.r(t),r.d(t,{default:()=>n});var l=r(9726);var s=r(5475),a=r(3402);const o={name:"AdministratorOrderDetailsComponent",components:{LoadingComponent:s.A,OrderDetailsComponent:a.A},data:function(){return{loading:{isActive:!1}}},computed:{order:function(){return this.$store.getters["myOrderDetails/orderDetails"]},orderProducts:function(){return this.$store.getters["myOrderDetails/orderProducts"]},orderAddresses:function(){return this.$store.getters["myOrderDetails/orderAddresses"]},outletAddress:function(){return this.$store.getters["myOrderDetails/outletAddress"]}},mounted:function(){var e=this;this.$route.params.id&&(this.loading.isActive=!0,this.$store.dispatch("myOrderDetails/orderDetails",{id:this.$route.params.id,orderId:this.$route.params.orderId}).then(function(t){e.loading.isActive=!1}).catch(function(t){e.loading.isActive=!1}))}};const n=(0,r(6262).A)(o,[["render",function(e,t,r,s,a,o){var n=(0,l.resolveComponent)("LoadingComponent"),i=(0,l.resolveComponent)("OrderDetailsComponent");return(0,l.openBlock)(),(0,l.createElementBlock)(l.Fragment,null,[(0,l.createVNode)(n,{props:a.loading},null,8,["props"]),(0,l.createVNode)(i,{order:o.order,orderProducts:o.orderProducts,orderAddresses:o.orderAddresses,outletAddress:o.outletAddress},null,8,["order","orderProducts","orderAddresses","outletAddress"])],64)}]])},3402:(e,t,r)=>{r.d(t,{A:()=>bt});var l=r(9726),s={class:"col-12 pt-4 pb-4"},a={class:"text-xs font-medium leading-6"},o={class:"flex items-start flex-col md:flex-row gap-6"},n={class:"w-full"},i={class:"p-4 mb-6 rounded-2xl shadow-xs bg-white"},c={class:"text-sm leading-6 mb-1 font-medium"},d={class:"text-[#008BBA]"},m={class:"text-xs font-light mb-3"},p={class:"text-xs font-light mb-3"},u={class:"text-xs font-light mb-3"},E={key:0,class:"my-4"},b={class:"capitalize font-medium text-sm leading-6 mb-2"},f={class:"text-sm text-heading mb-2"},y={key:0},g={class:"p-4 mb-6 rounded-2xl shadow-xs bg-white"},x={key:0,class:"text-sm leading-6 font-medium mb-2"},N={key:1,class:"text-sm leading-6 font-medium mb-2"},h={class:"flex items-start justify-start gap-2.5"},V={class:"flex flex-col gap-2.5 border-t border-dashed border-gray-100"},k={class:"flex flex-wrap sm:flex-nowrap gap-2 pt-4"},S={class:"text-sm font-semibold capitalize w-20 flex-shrink-0"},D={class:"text-sm font-normal capitalize"},w={class:"flex flex-wrap sm:flex-nowrap gap-2"},A={class:"text-sm font-semibold capitalize w-20 flex-shrink-0"},v={class:"text-sm font-normal capitalize"},B={class:"flex flex-wrap sm:flex-nowrap gap-2"},_={class:"text-sm font-semibold capitalize w-20 flex-shrink-0"},C={class:"text-sm font-normal"},$={class:"flex flex-wrap sm:flex-nowrap gap-2"},z={class:"text-sm font-semibold capitalize w-20 flex-shrink-0"},P={class:"text-sm font-normal capitalize"},j={key:0},I={key:1},O={key:2},T={key:3},F={key:4},L={key:1},R={class:"p-4 mb-6 rounded-2xl shadow-xs bg-white"},J={class:"text-sm leading-6 font-medium mb-2"},G={class:"flex items-start justify-start gap-2.5"},U={class:"flex flex-col gap-2.5 border-t border-dashed border-gray-100"},H={class:"flex flex-wrap sm:flex-nowrap gap-2 pt-4"},Y={class:"text-sm font-semibold capitalize w-20 flex-shrink-0"},K={class:"text-sm font-normal capitalize"},M={key:0,class:"flex flex-wrap sm:flex-nowrap gap-2"},W={class:"text-sm font-semibold capitalize w-20 flex-shrink-0"},q={class:"text-sm font-normal capitalize"},Q={key:1,class:"flex flex-wrap sm:flex-nowrap gap-2"},X={class:"text-sm font-semibold capitalize w-20 flex-shrink-0"},Z={class:"text-sm font-normal capitalize"},ee={class:"flex flex-wrap sm:flex-nowrap gap-2"},te={class:"text-sm font-semibold capitalize w-20 flex-shrink-0"},re={class:"text-sm font-normal capitalize"},le={key:0},se={key:0},ae={key:1},oe={key:2},ne={key:2,class:"p-4 rounded-2xl shadow-xs bg-white"},ie={class:"capitalize font-medium text-sm leading-6 mb-2"},ce={class:"flex flex-col gap-2 border-t border-dashed border-gray-100"},de={class:"flex items-center gap-2 pt-4"},me={class:"capitalize text-sm leading-6"},pe={class:"capitalize text-sm leading-6"},ue={class:"flex items-center gap-2"},Ee={class:"capitalize text-sm leading-6"},be={class:"w-full rounded-2xl shadow-xs bg-white"},fe={class:"p-4 border-b"},ye={class:"font-medium text-sm leading-6 capitalize mb-4"},ge={class:"pl-3"},xe={class:"flex items-center gap-3 relative"},Ne={class:"absolute top-5 -left-3 text-sm w-[26px] h-[26px] leading-[26px] text-center rounded-full text-white bg-heading"},he=["src"],Ve={class:"flex-auto overflow-hidden"},ke={class:"text-sm overflow-hidden"},Se={class:"flex flex-wrap items-center justify-between gap-4"},De={class:"flex items-center gap-8"},we={class:"text-sm font-semibold"},Ae={class:"p-4"},ve={class:"rounded-xl border border-[#EFF0F6]"},Be={class:"flex flex-col gap-2 p-3 border-b border-dashed border-[#EFF0F6]"},_e={class:"flex items-center justify-between"},Ce={class:"text-sm leading-6 capitalize"},$e={class:"text-sm leading-6 capitalize"},ze={class:"flex items-center justify-between"},Pe={class:"text-sm leading-6 capitalize"},je={class:"text-sm leading-6 capitalize"},Ie={class:"flex items-center justify-between"},Oe={class:"text-sm leading-6 capitalize"},Te={class:"text-sm leading-6 capitalize font-medium text-[#1AB759]"},Fe={class:"flex items-center justify-between"},Le={class:"text-sm leading-6 capitalize"},Re={class:"text-sm leading-6 capitalize"},Je={class:"flex items-center justify-between p-3"},Ge={class:"text-sm leading-6 font-semibold capitalize"},Ue={class:"text-sm leading-6 font-semibold capitalize"};var He=r(5475),Ye=r(908),Ke=r(5570),Me=r(1978),We=r(970),qe={key:0,class:"w-full flex items-center justify-center pb-12 mt-8 mb-5"},Qe={class:"w-full flex items-center justify-center gap-1 relative"},Xe={class:"absolute top-10 left-1/2 -translate-x-1/2 w-14 sm:w-20 text-xs sm:text-sm leading-[18px] text-center capitalize"},Ze={key:1,class:"w-full flex items-center justify-center pb-12 mt-8 mb-5"},et={class:"w-full flex items-center justify-center gap-1 relative"},tt={class:"absolute top-10 left-1/2 -translate-x-1/2 w-14 sm:w-20 text-xs sm:text-sm leading-[18px] text-center capitalize"},rt={key:2,type:"button",class:"flex items-center justify-center gap-2 py-3 sm:py-4 px-7 sm:px-10 mx-auto mt-6 rounded-2xl border border-[#FB4E4E] text-[#FB4E4E] bg-white transition-all duration-500 hover:bg-[#FB4E4E] hover:text-white mb-5"},lt={class:"sm:text-lg font-bold capitalize whitespace-nowrap"},st={key:3,type:"button",class:"flex items-center justify-center gap-2 py-3 sm:py-4 px-7 sm:px-10 mx-auto mt-6 rounded-2xl border border-[#FB4E4E] text-[#FB4E4E] bg-white transition-all duration-500 hover:bg-[#FB4E4E] hover:text-white"},at={class:"sm:text-lg font-bold capitalize whitespace-nowrap"};function ot(e){return ot="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ot(e)}function nt(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=ot(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var l=r.call(e,t||"default");if("object"!=ot(l))return l;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==ot(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}const it={name:"OrderStatusComponent",props:{order:Object},data:function(){return{tracks:[{step:1,title:this.$t("label.order_pending")},{step:5,title:this.$t("label.order_confirmed")},{step:7,title:this.$t("label.order_on_the_way")},{step:10,title:this.$t("label.order_delivered")}],pickupTracks:[{step:1,title:this.$t("label.order_pending")},{step:5,title:this.$t("label.order_confirmed")},{step:10,title:this.$t("label.order_delivered")}],enums:{orderStatusEnum:Ye.A,orderTypeEnum:Me.A,orderStatusEnumArray:nt(nt(nt(nt(nt(nt({},Ye.A.PENDING,this.$t("label.pending")),Ye.A.CONFIRMED,this.$t("label.confirmed")),Ye.A.ON_THE_WAY,this.$t("label.on_the_way")),Ye.A.DELIVERED,this.$t("label.delivered")),Ye.A.CANCELED,this.$t("label.canceled")),Ye.A.REJECTED,this.$t("label.rejected"))}}},computed:{setting:function(){return this.$store.getters["frontendSetting/lists"]}}};var ct=r(6262);const dt=(0,ct.A)(it,[["render",function(e,t,r,s,a,o){return(0,l.openBlock)(),(0,l.createElementBlock)(l.Fragment,null,[r.order.status!==a.enums.orderStatusEnum.CANCELED&&r.order.status!==a.enums.orderStatusEnum.REJECTED&&r.order.order_type!==a.enums.orderTypeEnum.PICK_UP?((0,l.openBlock)(),(0,l.createElementBlock)("ul",qe,[((0,l.openBlock)(!0),(0,l.createElementBlock)(l.Fragment,null,(0,l.renderList)(a.tracks,function(e){return(0,l.openBlock)(),(0,l.createElementBlock)("li",Qe,[(0,l.createElementVNode)("hr",{class:(0,l.normalizeClass)([{"!bg-success":e.step<=r.order.status},"block border-none w-full h-1 rounded-xl bg-gray-200"])},null,2),(0,l.createElementVNode)("i",{class:(0,l.normalizeClass)([{"lab-fill-save !bg-success text-white":e.step<=r.order.status},"flex-shrink-0 w-7 h-7 !leading-7 text-center rounded-full bg-gray-200 lab-font-size-16"])},null,2),(0,l.createElementVNode)("hr",{class:(0,l.normalizeClass)([{"!bg-success":e.step<=r.order.status},"block border-none w-full h-1 rounded-xl bg-gray-200"])},null,2),(0,l.createElementVNode)("span",Xe,(0,l.toDisplayString)(e.title),1)])}),256))])):(0,l.createCommentVNode)("",!0),r.order.status!==a.enums.orderStatusEnum.CANCELED&&r.order.status!==a.enums.orderStatusEnum.REJECTED&&r.order.order_type===a.enums.orderTypeEnum.PICK_UP?((0,l.openBlock)(),(0,l.createElementBlock)("ul",Ze,[((0,l.openBlock)(!0),(0,l.createElementBlock)(l.Fragment,null,(0,l.renderList)(a.pickupTracks,function(e){return(0,l.openBlock)(),(0,l.createElementBlock)("li",et,[(0,l.createElementVNode)("hr",{class:(0,l.normalizeClass)([{"!bg-success":e.step<=r.order.status},"block border-none w-full h-1 rounded-xl bg-gray-200"])},null,2),(0,l.createElementVNode)("i",{class:(0,l.normalizeClass)([{"lab-fill-save !bg-success text-white":e.step<=r.order.status},"flex-shrink-0 w-7 h-7 !leading-7 text-center rounded-full bg-gray-200 lab-font-size-16"])},null,2),(0,l.createElementVNode)("hr",{class:(0,l.normalizeClass)([{"!bg-success":e.step<=r.order.status},"block border-none w-full h-1 rounded-xl bg-gray-200"])},null,2),(0,l.createElementVNode)("span",tt,(0,l.toDisplayString)(e.title),1)])}),256))])):(0,l.createCommentVNode)("",!0),r.order.status===a.enums.orderStatusEnum.CANCELED?((0,l.openBlock)(),(0,l.createElementBlock)("button",rt,[t[0]||(t[0]=(0,l.createElementVNode)("i",{class:"lab-fill-close-circle sm:text-xl"},null,-1)),(0,l.createElementVNode)("span",lt,(0,l.toDisplayString)(e.$t("label.order_cancelled")),1)])):(0,l.createCommentVNode)("",!0),r.order.status===a.enums.orderStatusEnum.REJECTED?((0,l.openBlock)(),(0,l.createElementBlock)("button",st,[t[1]||(t[1]=(0,l.createElementVNode)("i",{class:"lab-fill-close-circle sm:text-xl"},null,-1)),(0,l.createElementVNode)("span",at,(0,l.toDisplayString)(e.$t("label.order_rejected")),1)])):(0,l.createCommentVNode)("",!0)],64)}]]);var mt=r(8655);function pt(e){return pt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},pt(e)}function ut(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=pt(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var l=r.call(e,t||"default");if("object"!=pt(l))return l;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==pt(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}const Et={name:"OrderDetailsComponent",components:{LoadingComponent:He.A,OrderStatusComponent:dt},props:{order:Object,orderProducts:Object,orderAddresses:Object,outletAddress:Object},data:function(){return{loading:{isActive:!1},enums:{orderStatusEnum:Ye.A,orderTypeEnum:Me.A,paymentStatusEnum:We.A,addressTypeEnum:Ke.A,orderStatusEnumArray:ut(ut(ut(ut(ut(ut({},Ye.A.PENDING,this.$t("label.pending")),Ye.A.CONFIRMED,this.$t("label.confirmed")),Ye.A.ON_THE_WAY,this.$t("label.on_the_way")),Ye.A.DELIVERED,this.$t("label.delivered")),Ye.A.CANCELED,this.$t("label.canceled")),Ye.A.REJECTED,this.$t("label.rejected")),paymentStatusEnumArray:ut(ut({},We.A.PAID,this.$t("label.paid")),We.A.UNPAID,this.$t("label.unpaid")),orderTypeEnumArray:ut(ut({},Me.A.DELIVERY,this.$t("label.delivery")),Me.A.PICK_UP,this.$t("label.pick_up"))}}},methods:{orderStatusClass:function(e){return mt.A.orderStatusClass(e)}}},bt=(0,ct.A)(Et,[["render",function(e,t,r,He,Ye,Ke){var Me=(0,l.resolveComponent)("router-link"),We=(0,l.resolveComponent)("OrderStatusComponent");return(0,l.openBlock)(),(0,l.createElementBlock)("section",s,[(0,l.createVNode)(Me,{to:"",onClick:t[0]||(t[0]=function(t){return e.$router.go(-1)}),class:"mb-3 inline-flex items-center gap-2 text-primary"},{default:(0,l.withCtx)(function(){return[t[1]||(t[1]=(0,l.createElementVNode)("i",{class:"lab lab-line-undo lab-font-size-16"},null,-1)),(0,l.createElementVNode)("span",a,(0,l.toDisplayString)(e.$t("label.back_to_orders")),1)]}),_:1,__:[1]}),(0,l.createElementVNode)("div",o,[(0,l.createElementVNode)("div",n,[(0,l.createElementVNode)("div",i,[(0,l.createElementVNode)("h3",c,[(0,l.createTextVNode)((0,l.toDisplayString)(e.$t("label.order_id"))+": ",1),(0,l.createElementVNode)("span",d,"#"+(0,l.toDisplayString)(r.order.order_serial_no),1)]),(0,l.createElementVNode)("p",m,(0,l.toDisplayString)(e.$t("label.order_date"))+": "+(0,l.toDisplayString)(r.order.order_datetime),1),(0,l.createElementVNode)("p",p,(0,l.toDisplayString)(e.$t("label.order_type"))+": "+(0,l.toDisplayString)(Ye.enums.orderTypeEnumArray[r.order.order_type]),1),(0,l.createElementVNode)("p",u,[(0,l.createTextVNode)((0,l.toDisplayString)(e.$t("label.order_status"))+": ",1),(0,l.createElementVNode)("span",{class:(0,l.normalizeClass)(Ke.orderStatusClass(r.order.status))},(0,l.toDisplayString)(Ye.enums.orderStatusEnumArray[r.order.status]),3)]),(0,l.createVNode)(We,{order:r.order},null,8,["order"]),parseInt(r.order.status)===parseInt(Ye.enums.orderStatusEnum.REJECTED)?((0,l.openBlock)(),(0,l.createElementBlock)("div",E,[(0,l.createElementVNode)("h3",b,(0,l.toDisplayString)(e.$t("label.reason"))+":",1),(0,l.createElementVNode)("p",f,(0,l.toDisplayString)(r.order.reason),1)])):(0,l.createCommentVNode)("",!0)]),r.order.order_type===Ye.enums.orderTypeEnum.DELIVERY?((0,l.openBlock)(),(0,l.createElementBlock)("div",y,[((0,l.openBlock)(!0),(0,l.createElementBlock)(l.Fragment,null,(0,l.renderList)(r.orderAddresses,function(t){return(0,l.openBlock)(),(0,l.createElementBlock)("div",g,[t.address_type===Ye.enums.addressTypeEnum.SHIPPING?((0,l.openBlock)(),(0,l.createElementBlock)("h3",x,(0,l.toDisplayString)(e.$t("label.shipping_address")),1)):((0,l.openBlock)(),(0,l.createElementBlock)("h3",N,(0,l.toDisplayString)(e.$t("label.billing_address")),1)),(0,l.createElementVNode)("div",h,[(0,l.createElementVNode)("ul",V,[(0,l.createElementVNode)("li",k,[(0,l.createElementVNode)("span",S,(0,l.toDisplayString)(e.$t("label.name")),1),(0,l.createElementVNode)("span",D,(0,l.toDisplayString)(t.full_name),1)]),(0,l.createElementVNode)("li",w,[(0,l.createElementVNode)("span",A,(0,l.toDisplayString)(e.$t("label.phone")),1),(0,l.createElementVNode)("span",v,(0,l.toDisplayString)(t.country_code)+(0,l.toDisplayString)(t.phone),1)]),(0,l.createElementVNode)("li",B,[(0,l.createElementVNode)("span",_,(0,l.toDisplayString)(e.$t("label.email")),1),(0,l.createElementVNode)("span",C,(0,l.toDisplayString)(t.email),1)]),(0,l.createElementVNode)("li",$,[(0,l.createElementVNode)("span",z,(0,l.toDisplayString)(e.$t("label.address")),1),(0,l.createElementVNode)("span",P,[t.address?((0,l.openBlock)(),(0,l.createElementBlock)("span",j,(0,l.toDisplayString)(t.address)+",",1)):(0,l.createCommentVNode)("",!0),t.state?((0,l.openBlock)(),(0,l.createElementBlock)("span",I,(0,l.toDisplayString)(t.state)+",",1)):(0,l.createCommentVNode)("",!0),t.city?((0,l.openBlock)(),(0,l.createElementBlock)("span",O,(0,l.toDisplayString)(t.city)+",",1)):(0,l.createCommentVNode)("",!0),t.country?((0,l.openBlock)(),(0,l.createElementBlock)("span",T,(0,l.toDisplayString)(t.country)+",",1)):(0,l.createCommentVNode)("",!0),t.zip_code?((0,l.openBlock)(),(0,l.createElementBlock)("span",F,(0,l.toDisplayString)(t.zip_code),1)):(0,l.createCommentVNode)("",!0)])])])])])}),256))])):((0,l.openBlock)(),(0,l.createElementBlock)("div",L,[(0,l.createElementVNode)("div",R,[(0,l.createElementVNode)("h3",J,(0,l.toDisplayString)(e.$t("label.pick_up_address")),1),(0,l.createElementVNode)("div",G,[(0,l.createElementVNode)("ul",U,[(0,l.createElementVNode)("li",H,[(0,l.createElementVNode)("span",Y,(0,l.toDisplayString)(e.$t("label.name")),1),(0,l.createElementVNode)("span",K,(0,l.toDisplayString)(r.outletAddress.name),1)]),r.outletAddress.phone?((0,l.openBlock)(),(0,l.createElementBlock)("li",M,[(0,l.createElementVNode)("span",W,(0,l.toDisplayString)(e.$t("label.phone")),1),(0,l.createElementVNode)("span",q,(0,l.toDisplayString)(r.outletAddress.phone),1)])):(0,l.createCommentVNode)("",!0),r.outletAddress.email?((0,l.openBlock)(),(0,l.createElementBlock)("li",Q,[(0,l.createElementVNode)("span",X,(0,l.toDisplayString)(e.$t("label.email")),1),(0,l.createElementVNode)("span",Z,(0,l.toDisplayString)(r.outletAddress.email),1)])):(0,l.createCommentVNode)("",!0),(0,l.createElementVNode)("li",ee,[(0,l.createElementVNode)("span",te,(0,l.toDisplayString)(e.$t("label.address")),1),(0,l.createElementVNode)("span",re,[r.outletAddress.address?((0,l.openBlock)(),(0,l.createElementBlock)("span",le,(0,l.toDisplayString)(r.outletAddress.address),1)):(0,l.createCommentVNode)("",!0),(0,l.createElementVNode)("span",{class:(0,l.normalizeClass)(["block",r.outletAddress.address?"mt-2":""])},[r.outletAddress.city?((0,l.openBlock)(),(0,l.createElementBlock)("span",se,(0,l.toDisplayString)(r.outletAddress.city)+",",1)):(0,l.createCommentVNode)("",!0),r.outletAddress.state?((0,l.openBlock)(),(0,l.createElementBlock)("span",ae,(0,l.toDisplayString)(r.outletAddress.state)+",",1)):(0,l.createCommentVNode)("",!0),r.outletAddress.zip_code?((0,l.openBlock)(),(0,l.createElementBlock)("span",oe,(0,l.toDisplayString)(r.outletAddress.zip_code),1)):(0,l.createCommentVNode)("",!0)],2)])])])])])])),parseInt(r.order.status)!==parseInt(Ye.enums.orderStatusEnum.REJECTED)&&parseInt(r.order.status)!==parseInt(Ye.enums.orderStatusEnum.CANCELED)?((0,l.openBlock)(),(0,l.createElementBlock)("div",ne,[(0,l.createElementVNode)("h3",ie,(0,l.toDisplayString)(e.$t("label.payment_info")),1),(0,l.createElementVNode)("ul",ce,[(0,l.createElementVNode)("li",de,[(0,l.createElementVNode)("span",me,(0,l.toDisplayString)(e.$t("label.method"))+":",1),(0,l.createElementVNode)("span",pe,(0,l.toDisplayString)(r.order.payment_method_name),1)]),(0,l.createElementVNode)("li",ue,[(0,l.createElementVNode)("span",Ee,(0,l.toDisplayString)(e.$t("label.status"))+":",1),(0,l.createElementVNode)("span",{class:(0,l.normalizeClass)(["capitalize text-sm leading-6",Ye.enums.paymentStatusEnum.PAID===r.order.payment_status?"text-[#2AC769]":"text-[#FB4E4E]"])},(0,l.toDisplayString)(Ye.enums.paymentStatusEnumArray[r.order.payment_status]),3)])])])):(0,l.createCommentVNode)("",!0)]),(0,l.createElementVNode)("div",be,[(0,l.createElementVNode)("div",fe,[(0,l.createElementVNode)("h3",ye,(0,l.toDisplayString)(e.$t("label.order_details")),1),(0,l.createElementVNode)("div",ge,[r.orderProducts.length>0?((0,l.openBlock)(!0),(0,l.createElementBlock)(l.Fragment,{key:0},(0,l.renderList)(r.orderProducts,function(e){return(0,l.openBlock)(),(0,l.createElementBlock)("div",{class:"mb-3 pb-3 border-b last:mb-0 last:pb-0 last:border-b-0 border-gray-2",key:e},[(0,l.createElementVNode)("div",xe,[(0,l.createElementVNode)("h3",Ne,(0,l.toDisplayString)(e.quantity),1),(0,l.createElementVNode)("img",{class:"w-16 h-16 rounded-lg flex-shrink-0",src:e.product_image,alt:"thumbnail"},null,8,he),(0,l.createElementVNode)("div",Ve,[(0,l.createElementVNode)("h4",{class:(0,l.normalizeClass)([e.variation_names?"":"mb-4","text-sm capitalize whitespace-nowrap overflow-hidden text-ellipsis"])},(0,l.toDisplayString)(e.product_name),3),(0,l.createElementVNode)("p",ke,(0,l.toDisplayString)(e.variation_names),1),(0,l.createElementVNode)("div",Se,[(0,l.createElementVNode)("div",De,[(0,l.createElementVNode)("span",we,(0,l.toDisplayString)(e.total_currency_price),1)])])])])])}),128)):(0,l.createCommentVNode)("",!0)])]),(0,l.createElementVNode)("div",Ae,[(0,l.createElementVNode)("div",ve,[(0,l.createElementVNode)("ul",Be,[(0,l.createElementVNode)("li",_e,[(0,l.createElementVNode)("span",Ce,(0,l.toDisplayString)(e.$t("label.subtotal")),1),(0,l.createElementVNode)("span",$e,(0,l.toDisplayString)(r.order.subtotal_currency_price),1)]),(0,l.createElementVNode)("li",ze,[(0,l.createElementVNode)("span",Pe,(0,l.toDisplayString)(e.$t("label.tax_fee")),1),(0,l.createElementVNode)("span",je,(0,l.toDisplayString)(r.order.tax_currency_price),1)]),(0,l.createElementVNode)("li",Ie,[(0,l.createElementVNode)("span",Oe,(0,l.toDisplayString)(e.$t("label.shipping_charge")),1),(0,l.createElementVNode)("span",Te,(0,l.toDisplayString)(r.order.shipping_charge_currency_price),1)]),(0,l.createElementVNode)("li",Fe,[(0,l.createElementVNode)("span",Le,(0,l.toDisplayString)(e.$t("label.discount")),1),(0,l.createElementVNode)("span",Re,(0,l.toDisplayString)(r.order.discount_currency_price),1)])]),(0,l.createElementVNode)("div",Je,[(0,l.createElementVNode)("h4",Ge,(0,l.toDisplayString)(e.$t("label.total")),1),(0,l.createElementVNode)("h5",Ue,(0,l.toDisplayString)(r.order.total_currency_price),1)])])])])])])}]])},5570:(e,t,r)=>{r.d(t,{A:()=>l});const l=Object.freeze({SHIPPING:5,BILLING:10})}}]);