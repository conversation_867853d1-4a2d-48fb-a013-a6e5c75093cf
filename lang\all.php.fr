<?php

return [
    'label'   => [
        'name'                       => 'Nom',
        'email'                      => 'E-mail',
        'phone'                      => 'Téléphone',
        'product_name'               => 'Nom du produit',
        'product_category_id'        => 'Catégorie',
        'product_brand_id'           => 'Marque',
        'barcode_id'                 => 'Code-barres',
        'unit_id'                    => 'Unité',
        'tax_id'                     => 'Taxe',
        'tax'                        => 'Taxe',
        'buying_price'               => 'Prix d\'achat',
        'selling_price'              => 'Prix de vente',
        'can_purchasable'            => 'Peut être acheté',
        'purchasable'                => 'Achetable',
        'show_stock_out'             => 'Afficher rupture de stock',
        'maximum_purchase_quantity'  => 'Quantité maximale d\'achat',
        'low_stock_quantity_warning' => 'Alerte de stock faible',
        'weight'                     => 'Poids',
        'refundable'                 => 'Remboursable',
        'code'                       => 'Code',
        'sku'                        => 'Référence',
        'discount'                   => 'Remise',
        'discount_type'              => 'Type de remise',
        'start_date'                 => 'Date de début',
        'end_date'                   => 'Date de fin',
        'amount'                     => 'Montant',
        'status'                     => 'Statut',
        'price'                      => 'Prix',
        'role'                       => 'Rôle',
        'order_serial_no'            => 'N° de série de commande',
        'date'                       => 'Date',
        'total'                      => 'Total',
        'shipping_charge'            => 'Frais de livraison',
        'payment_type'               => 'Type de paiement',
        'payment_status'             => 'Statut du paiement',
        'quantity'                   => 'Quantité',
        'sold_quantity'              => 'Quantité vendue',
        'order_type'                 => 'Type de commande',
        'customer'                   => 'Client',
        'confirm'                    => 'Confirmer',
        'congratulations'            => 'Félicitations !',
        'transaction_id'             => 'ID de transaction',
        'back_to_home'               => 'Retour à l\'accueil',
        'back_to_payment'            => 'Retour au paiement',
        'payment_method'             => 'Méthode de paiement',
        'now'                        => 'Maintenant',
        'balance'                    => 'Solde',
        'advance'                    => 'Avance',
        'all_roles'                  => 'Tous les rôles',
        'all_users'                  => 'Tous les utilisateurs',
        'user'                       => 'Utilisateur',
        'title'                      => 'Titre',
        'all_items'                  => 'Tous les articles',
        'slug'                       => 'Slug',
        'type'                       => 'Type',
        "supplier"                   => "Fournisseur",
        "reference_no"               => "N° de référence",
        "note"                       => "Note",
        "stock"                      => "Stock",
        "reason"                     => "Raison",
        "total_return_price"         => "Prix total de retour",
        "source"                     => "Source",
        "parent_category"            => "Catégorie parente",
        "sales_report"               => "Rapport des ventes",
        "product_report"             => "Rapport des produits",
        "order_id"                   => "ID de commande",
        "category"                   => "Catégorie",
        "credit_balance_report"      => "Rapport de solde créditeur"
    ],
    'message' => [
        'validation_error'                 => 'Erreur de validation.',
        'credentials_error'                => 'Erreurs d\'identifiants.',
        'credentials_invalid'              => 'Identifiants invalides ou vous êtes bloqué',
        'role_error'                       => 'Erreurs de rôle.',
        'role_exist'                       => 'Le rôle n\'existe pas.',
        'login_success'                    => 'Connexion réussie.',
        'logout_success'                   => 'Déconnexion réussie.',
        'invalid_api_key'                  => 'Clé API invalide.',
        'user_match'                       => 'Utilisateur non correspondant.',
        'something_wrong'                  => 'Quelque chose ne va pas.',
        'permission_denied'                => 'Permission refusée.',
        'product_match'                    => 'Produit non correspondant.',
        'offer_product_exist'              => 'Le produit est déjà ajouté dans une autre offre. Lorsque l\'offre sera terminée, vous pourrez ajouter cet article à nouveau.',
        'minimum_order_amount'             => 'Le montant minimum de commande est ',
        'coupon_date_expired'              => 'La période du coupon est expirée',
        'coupon_not_exist'                 => 'Le coupon n\'existe pas',
        'code_is_invalid'                  => 'Le code est invalide.',
        'code_is_expired'                  => 'Le code est expiré.',
        'you_can_reset_your_password'      => 'Vous pouvez maintenant réinitialiser votre mot de passe.',
        'check_your_email_for_code'        => 'Veuillez vérifier votre e-mail pour le code.',
        'check_your_phone_for_code'        => 'Veuillez vérifier votre téléphone pour le code.',
        'token_created_fail'               => 'Échec de création du jeton.',
        'email_does_not_exist'             => 'Cet e-mail n\'existe pas.',
        'phone_does_not_exist'             => 'Ce numéro de téléphone n\'existe pas.',
        'user_does_not_exist'              => 'Cet utilisateur n\'existe pas.',
        'phone_exist'                      => 'Ce numéro de téléphone existe déjà.',
        'otp_verify_success'               => 'Vérification OTP réussie.',
        'user_verify_success'              => 'Vérification de l\'utilisateur réussie.',
        'register_successfully'            => 'Inscription réussie.',
        'reset_successfully'               => 'Votre mot de passe a été réinitialisé avec succès.',
        'select_your_payment_method'       => 'Sélectionnez votre méthode de paiement',
        'payment_successful'               => 'Votre paiement a été traité avec succès',
        'payment_canceled'                 => 'Le paiement est annulé',
        'payment_gateway_disable'          => 'La passerelle de paiement est désactivée',
        'token_save'                       => 'Le jeton a été enregistré avec succès.',
        'token_is_invalid'                 => 'Le jeton est invalide.',
        'order_accept'                     => 'La commande a déjà été acceptée.',
        'order_confirmed'                  => 'La commande a déjà été confirmée.',
        'feature_disable'                  => 'Cette fonctionnalité est désactivée pour la démo.',
        'time_slot_exist'                  => 'Les créneaux horaires existent déjà.',
        'attribute_invalid'                => 'Cet attribut est invalide. Créez un attribut ou une option d\'attribut.',
        'price_invalid'                    => 'Ce montant de prix fourni est invalide.',
        'product_attribute_invalid'        => 'Cet identifiant d\'attribut de produit fourni est invalide.',
        'product_attribute_option_invalid' => 'Cet identifiant d\'option d\'attribut de produit fourni est invalide.',
        'sku_exist'                        => 'Cette référence a déjà été prise.',
        'resource_already_used'            => 'Impossible de supprimer définitivement cette ressource. Elle est liée à une autre ressource.',
        'variation_sku_required'           => 'Le champ référence est obligatoire.',
        'email_or_phone_required'          => 'Le champ e-mail ou téléphone est obligatoire.',
        'country_exist'                    => 'Ce pays a déjà été pris.',
        'product_tax_invalid'              => 'Le champ taxe est obligatoire pour tous les produits.',
        'product_quantity_invalid'         => 'Le champ quantité est obligatoire pour tous les produits.',
        'product_invalid'                  => 'Le champ produit est obligatoire.',
        'product_price_invalid'            => 'Le champ coût unitaire est obligatoire pour tous les produits.',
        'product_price_total_invalid'      => 'Le coût unitaire total du produit est invalide.',
        'supplier_invalid'                 => 'Le fournisseur est invalide.',
        'status_invalid'                   => 'Le statut est invalide.',
        'return_request_exist'             => 'La demande de retour existe déjà.',
        'minimum_one_outlet_required'      => 'Le dernier point de vente ne peut pas être supprimé',
        "email_send"                       => "E-mail envoyé avec succès.",
        "price_total_invalid"              => "Le montant est invalide.",
        "the_form_is_valid"                => "Le formulaire est valide",
        'account_delete_success'           => 'Compte supprimé avec succès.',
        'account_not_delete'               => 'Vous ne pouvez pas supprimer votre compte si vous avez une commande active.',
        "only_customer_delete"             => 'Seuls les comptes clients peuvent être supprimés',
        "phone_not_verified"               => "Le téléphone n'est pas vérifié",
        "email_not_verified"               => "L'e-mail n'est pas vérifié",
        'coupon_limit_exceeded'            => 'La limite du coupon a été dépassée',
        "register_not_completed"           => "Votre inscription n'a pas été complétée."
    ]
];
