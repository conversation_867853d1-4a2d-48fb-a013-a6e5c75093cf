<?php
// This file was auto-generated from sdk-root/src/data/lambda/2015-03-31/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2015-03-31', 'endpointPrefix' => 'lambda', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceFullName' => 'AWS Lambda', 'serviceId' => 'Lambda', 'signatureVersion' => 'v4', 'uid' => 'lambda-2015-03-31', 'auth' => [ 'aws.auth#sigv4', ], ], 'operations' => [ 'AddLayerVersionPermission' => [ 'name' => 'AddLayerVersionPermission', 'http' => [ 'method' => 'POST', 'requestUri' => '/2018-10-31/layers/{LayerName}/versions/{VersionNumber}/policy', 'responseCode' => 201, ], 'input' => [ 'shape' => 'AddLayerVersionPermissionRequest', ], 'output' => [ 'shape' => 'AddLayerVersionPermissionResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceConflictException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'PolicyLengthExceededException', ], [ 'shape' => 'PreconditionFailedException', ], ], ], 'AddPermission' => [ 'name' => 'AddPermission', 'http' => [ 'method' => 'POST', 'requestUri' => '/2015-03-31/functions/{FunctionName}/policy', 'responseCode' => 201, ], 'input' => [ 'shape' => 'AddPermissionRequest', ], 'output' => [ 'shape' => 'AddPermissionResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceConflictException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'PolicyLengthExceededException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'PreconditionFailedException', ], ], ], 'CreateAlias' => [ 'name' => 'CreateAlias', 'http' => [ 'method' => 'POST', 'requestUri' => '/2015-03-31/functions/{FunctionName}/aliases', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateAliasRequest', ], 'output' => [ 'shape' => 'AliasConfiguration', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceConflictException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'CreateCodeSigningConfig' => [ 'name' => 'CreateCodeSigningConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-04-22/code-signing-configs/', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateCodeSigningConfigRequest', ], 'output' => [ 'shape' => 'CreateCodeSigningConfigResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidParameterValueException', ], ], ], 'CreateEventSourceMapping' => [ 'name' => 'CreateEventSourceMapping', 'http' => [ 'method' => 'POST', 'requestUri' => '/2015-03-31/event-source-mappings/', 'responseCode' => 202, ], 'input' => [ 'shape' => 'CreateEventSourceMappingRequest', ], 'output' => [ 'shape' => 'EventSourceMappingConfiguration', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceConflictException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'CreateFunction' => [ 'name' => 'CreateFunction', 'http' => [ 'method' => 'POST', 'requestUri' => '/2015-03-31/functions', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateFunctionRequest', ], 'output' => [ 'shape' => 'FunctionConfiguration', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceConflictException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'CodeStorageExceededException', ], [ 'shape' => 'CodeVerificationFailedException', ], [ 'shape' => 'InvalidCodeSignatureException', ], [ 'shape' => 'CodeSigningConfigNotFoundException', ], ], ], 'CreateFunctionUrlConfig' => [ 'name' => 'CreateFunctionUrlConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/2021-10-31/functions/{FunctionName}/url', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateFunctionUrlConfigRequest', ], 'output' => [ 'shape' => 'CreateFunctionUrlConfigResponse', ], 'errors' => [ [ 'shape' => 'ResourceConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'DeleteAlias' => [ 'name' => 'DeleteAlias', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2015-03-31/functions/{FunctionName}/aliases/{Name}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteAliasRequest', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceConflictException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'DeleteCodeSigningConfig' => [ 'name' => 'DeleteCodeSigningConfig', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2020-04-22/code-signing-configs/{CodeSigningConfigArn}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteCodeSigningConfigRequest', ], 'output' => [ 'shape' => 'DeleteCodeSigningConfigResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceConflictException', ], ], ], 'DeleteEventSourceMapping' => [ 'name' => 'DeleteEventSourceMapping', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2015-03-31/event-source-mappings/{UUID}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'DeleteEventSourceMappingRequest', ], 'output' => [ 'shape' => 'EventSourceMappingConfiguration', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ResourceConflictException', ], [ 'shape' => 'ResourceInUseException', ], ], ], 'DeleteFunction' => [ 'name' => 'DeleteFunction', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2015-03-31/functions/{FunctionName}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteFunctionRequest', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceConflictException', ], ], ], 'DeleteFunctionCodeSigningConfig' => [ 'name' => 'DeleteFunctionCodeSigningConfig', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2020-06-30/functions/{FunctionName}/code-signing-config', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteFunctionCodeSigningConfigRequest', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'CodeSigningConfigNotFoundException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ResourceConflictException', ], ], ], 'DeleteFunctionConcurrency' => [ 'name' => 'DeleteFunctionConcurrency', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2017-10-31/functions/{FunctionName}/concurrency', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteFunctionConcurrencyRequest', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceConflictException', ], ], ], 'DeleteFunctionEventInvokeConfig' => [ 'name' => 'DeleteFunctionEventInvokeConfig', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2019-09-25/functions/{FunctionName}/event-invoke-config', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteFunctionEventInvokeConfigRequest', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ResourceConflictException', ], ], ], 'DeleteFunctionUrlConfig' => [ 'name' => 'DeleteFunctionUrlConfig', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2021-10-31/functions/{FunctionName}/url', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteFunctionUrlConfigRequest', ], 'errors' => [ [ 'shape' => 'ResourceConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'DeleteLayerVersion' => [ 'name' => 'DeleteLayerVersion', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2018-10-31/layers/{LayerName}/versions/{VersionNumber}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteLayerVersionRequest', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'DeleteProvisionedConcurrencyConfig' => [ 'name' => 'DeleteProvisionedConcurrencyConfig', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2019-09-30/functions/{FunctionName}/provisioned-concurrency', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteProvisionedConcurrencyConfigRequest', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ServiceException', ], ], ], 'GetAccountSettings' => [ 'name' => 'GetAccountSettings', 'http' => [ 'method' => 'GET', 'requestUri' => '/2016-08-19/account-settings/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetAccountSettingsRequest', ], 'output' => [ 'shape' => 'GetAccountSettingsResponse', ], 'errors' => [ [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ServiceException', ], ], ], 'GetAlias' => [ 'name' => 'GetAlias', 'http' => [ 'method' => 'GET', 'requestUri' => '/2015-03-31/functions/{FunctionName}/aliases/{Name}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetAliasRequest', ], 'output' => [ 'shape' => 'AliasConfiguration', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'GetCodeSigningConfig' => [ 'name' => 'GetCodeSigningConfig', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-04-22/code-signing-configs/{CodeSigningConfigArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetCodeSigningConfigRequest', ], 'output' => [ 'shape' => 'GetCodeSigningConfigResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetEventSourceMapping' => [ 'name' => 'GetEventSourceMapping', 'http' => [ 'method' => 'GET', 'requestUri' => '/2015-03-31/event-source-mappings/{UUID}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetEventSourceMappingRequest', ], 'output' => [ 'shape' => 'EventSourceMappingConfiguration', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'GetFunction' => [ 'name' => 'GetFunction', 'http' => [ 'method' => 'GET', 'requestUri' => '/2015-03-31/functions/{FunctionName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetFunctionRequest', ], 'output' => [ 'shape' => 'GetFunctionResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InvalidParameterValueException', ], ], ], 'GetFunctionCodeSigningConfig' => [ 'name' => 'GetFunctionCodeSigningConfig', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-06-30/functions/{FunctionName}/code-signing-config', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetFunctionCodeSigningConfigRequest', ], 'output' => [ 'shape' => 'GetFunctionCodeSigningConfigResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'GetFunctionConcurrency' => [ 'name' => 'GetFunctionConcurrency', 'http' => [ 'method' => 'GET', 'requestUri' => '/2019-09-30/functions/{FunctionName}/concurrency', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetFunctionConcurrencyRequest', ], 'output' => [ 'shape' => 'GetFunctionConcurrencyResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ServiceException', ], ], ], 'GetFunctionConfiguration' => [ 'name' => 'GetFunctionConfiguration', 'http' => [ 'method' => 'GET', 'requestUri' => '/2015-03-31/functions/{FunctionName}/configuration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetFunctionConfigurationRequest', ], 'output' => [ 'shape' => 'FunctionConfiguration', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InvalidParameterValueException', ], ], ], 'GetFunctionEventInvokeConfig' => [ 'name' => 'GetFunctionEventInvokeConfig', 'http' => [ 'method' => 'GET', 'requestUri' => '/2019-09-25/functions/{FunctionName}/event-invoke-config', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetFunctionEventInvokeConfigRequest', ], 'output' => [ 'shape' => 'FunctionEventInvokeConfig', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'GetFunctionRecursionConfig' => [ 'name' => 'GetFunctionRecursionConfig', 'http' => [ 'method' => 'GET', 'requestUri' => '/2024-08-31/functions/{FunctionName}/recursion-config', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetFunctionRecursionConfigRequest', ], 'output' => [ 'shape' => 'GetFunctionRecursionConfigResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ServiceException', ], ], ], 'GetFunctionUrlConfig' => [ 'name' => 'GetFunctionUrlConfig', 'http' => [ 'method' => 'GET', 'requestUri' => '/2021-10-31/functions/{FunctionName}/url', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetFunctionUrlConfigRequest', ], 'output' => [ 'shape' => 'GetFunctionUrlConfigResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'GetLayerVersion' => [ 'name' => 'GetLayerVersion', 'http' => [ 'method' => 'GET', 'requestUri' => '/2018-10-31/layers/{LayerName}/versions/{VersionNumber}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetLayerVersionRequest', ], 'output' => [ 'shape' => 'GetLayerVersionResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetLayerVersionByArn' => [ 'name' => 'GetLayerVersionByArn', 'http' => [ 'method' => 'GET', 'requestUri' => '/2018-10-31/layers?find=LayerVersion', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetLayerVersionByArnRequest', ], 'output' => [ 'shape' => 'GetLayerVersionResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetLayerVersionPolicy' => [ 'name' => 'GetLayerVersionPolicy', 'http' => [ 'method' => 'GET', 'requestUri' => '/2018-10-31/layers/{LayerName}/versions/{VersionNumber}/policy', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetLayerVersionPolicyRequest', ], 'output' => [ 'shape' => 'GetLayerVersionPolicyResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InvalidParameterValueException', ], ], ], 'GetPolicy' => [ 'name' => 'GetPolicy', 'http' => [ 'method' => 'GET', 'requestUri' => '/2015-03-31/functions/{FunctionName}/policy', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetPolicyRequest', ], 'output' => [ 'shape' => 'GetPolicyResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InvalidParameterValueException', ], ], ], 'GetProvisionedConcurrencyConfig' => [ 'name' => 'GetProvisionedConcurrencyConfig', 'http' => [ 'method' => 'GET', 'requestUri' => '/2019-09-30/functions/{FunctionName}/provisioned-concurrency', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetProvisionedConcurrencyConfigRequest', ], 'output' => [ 'shape' => 'GetProvisionedConcurrencyConfigResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'ProvisionedConcurrencyConfigNotFoundException', ], ], ], 'GetRuntimeManagementConfig' => [ 'name' => 'GetRuntimeManagementConfig', 'http' => [ 'method' => 'GET', 'requestUri' => '/2021-07-20/functions/{FunctionName}/runtime-management-config', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetRuntimeManagementConfigRequest', ], 'output' => [ 'shape' => 'GetRuntimeManagementConfigResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'Invoke' => [ 'name' => 'Invoke', 'http' => [ 'method' => 'POST', 'requestUri' => '/2015-03-31/functions/{FunctionName}/invocations', ], 'input' => [ 'shape' => 'InvocationRequest', ], 'output' => [ 'shape' => 'InvocationResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestContentException', ], [ 'shape' => 'RequestTooLargeException', ], [ 'shape' => 'UnsupportedMediaTypeException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'EC2UnexpectedException', ], [ 'shape' => 'SubnetIPAddressLimitReachedException', ], [ 'shape' => 'ENILimitReachedException', ], [ 'shape' => 'EFSMountConnectivityException', ], [ 'shape' => 'EFSMountFailureException', ], [ 'shape' => 'EFSMountTimeoutException', ], [ 'shape' => 'EFSIOException', ], [ 'shape' => 'SnapStartException', ], [ 'shape' => 'SnapStartTimeoutException', ], [ 'shape' => 'SnapStartNotReadyException', ], [ 'shape' => 'EC2ThrottledException', ], [ 'shape' => 'EC2AccessDeniedException', ], [ 'shape' => 'InvalidSubnetIDException', ], [ 'shape' => 'InvalidSecurityGroupIDException', ], [ 'shape' => 'InvalidZipFileException', ], [ 'shape' => 'KMSDisabledException', ], [ 'shape' => 'KMSInvalidStateException', ], [ 'shape' => 'KMSAccessDeniedException', ], [ 'shape' => 'KMSNotFoundException', ], [ 'shape' => 'InvalidRuntimeException', ], [ 'shape' => 'ResourceConflictException', ], [ 'shape' => 'ResourceNotReadyException', ], [ 'shape' => 'RecursiveInvocationException', ], ], ], 'InvokeAsync' => [ 'name' => 'InvokeAsync', 'http' => [ 'method' => 'POST', 'requestUri' => '/2014-11-13/functions/{FunctionName}/invoke-async/', 'responseCode' => 202, ], 'input' => [ 'shape' => 'InvokeAsyncRequest', ], 'output' => [ 'shape' => 'InvokeAsyncResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestContentException', ], [ 'shape' => 'InvalidRuntimeException', ], [ 'shape' => 'ResourceConflictException', ], ], 'deprecated' => true, ], 'InvokeWithResponseStream' => [ 'name' => 'InvokeWithResponseStream', 'http' => [ 'method' => 'POST', 'requestUri' => '/2021-11-15/functions/{FunctionName}/response-streaming-invocations', ], 'input' => [ 'shape' => 'InvokeWithResponseStreamRequest', ], 'output' => [ 'shape' => 'InvokeWithResponseStreamResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestContentException', ], [ 'shape' => 'RequestTooLargeException', ], [ 'shape' => 'UnsupportedMediaTypeException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'EC2UnexpectedException', ], [ 'shape' => 'SubnetIPAddressLimitReachedException', ], [ 'shape' => 'ENILimitReachedException', ], [ 'shape' => 'EFSMountConnectivityException', ], [ 'shape' => 'EFSMountFailureException', ], [ 'shape' => 'EFSMountTimeoutException', ], [ 'shape' => 'EFSIOException', ], [ 'shape' => 'SnapStartException', ], [ 'shape' => 'SnapStartTimeoutException', ], [ 'shape' => 'SnapStartNotReadyException', ], [ 'shape' => 'EC2ThrottledException', ], [ 'shape' => 'EC2AccessDeniedException', ], [ 'shape' => 'InvalidSubnetIDException', ], [ 'shape' => 'InvalidSecurityGroupIDException', ], [ 'shape' => 'InvalidZipFileException', ], [ 'shape' => 'KMSDisabledException', ], [ 'shape' => 'KMSInvalidStateException', ], [ 'shape' => 'KMSAccessDeniedException', ], [ 'shape' => 'KMSNotFoundException', ], [ 'shape' => 'InvalidRuntimeException', ], [ 'shape' => 'ResourceConflictException', ], [ 'shape' => 'ResourceNotReadyException', ], [ 'shape' => 'RecursiveInvocationException', ], ], ], 'ListAliases' => [ 'name' => 'ListAliases', 'http' => [ 'method' => 'GET', 'requestUri' => '/2015-03-31/functions/{FunctionName}/aliases', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAliasesRequest', ], 'output' => [ 'shape' => 'ListAliasesResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'ListCodeSigningConfigs' => [ 'name' => 'ListCodeSigningConfigs', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-04-22/code-signing-configs/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListCodeSigningConfigsRequest', ], 'output' => [ 'shape' => 'ListCodeSigningConfigsResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidParameterValueException', ], ], ], 'ListEventSourceMappings' => [ 'name' => 'ListEventSourceMappings', 'http' => [ 'method' => 'GET', 'requestUri' => '/2015-03-31/event-source-mappings/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListEventSourceMappingsRequest', ], 'output' => [ 'shape' => 'ListEventSourceMappingsResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'ListFunctionEventInvokeConfigs' => [ 'name' => 'ListFunctionEventInvokeConfigs', 'http' => [ 'method' => 'GET', 'requestUri' => '/2019-09-25/functions/{FunctionName}/event-invoke-config/list', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListFunctionEventInvokeConfigsRequest', ], 'output' => [ 'shape' => 'ListFunctionEventInvokeConfigsResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ServiceException', ], ], ], 'ListFunctionUrlConfigs' => [ 'name' => 'ListFunctionUrlConfigs', 'http' => [ 'method' => 'GET', 'requestUri' => '/2021-10-31/functions/{FunctionName}/urls', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListFunctionUrlConfigsRequest', ], 'output' => [ 'shape' => 'ListFunctionUrlConfigsResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'ListFunctions' => [ 'name' => 'ListFunctions', 'http' => [ 'method' => 'GET', 'requestUri' => '/2015-03-31/functions/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListFunctionsRequest', ], 'output' => [ 'shape' => 'ListFunctionsResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InvalidParameterValueException', ], ], ], 'ListFunctionsByCodeSigningConfig' => [ 'name' => 'ListFunctionsByCodeSigningConfig', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-04-22/code-signing-configs/{CodeSigningConfigArn}/functions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListFunctionsByCodeSigningConfigRequest', ], 'output' => [ 'shape' => 'ListFunctionsByCodeSigningConfigResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListLayerVersions' => [ 'name' => 'ListLayerVersions', 'http' => [ 'method' => 'GET', 'requestUri' => '/2018-10-31/layers/{LayerName}/versions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListLayerVersionsRequest', ], 'output' => [ 'shape' => 'ListLayerVersionsResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'ListLayers' => [ 'name' => 'ListLayers', 'http' => [ 'method' => 'GET', 'requestUri' => '/2018-10-31/layers', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListLayersRequest', ], 'output' => [ 'shape' => 'ListLayersResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'ListProvisionedConcurrencyConfigs' => [ 'name' => 'ListProvisionedConcurrencyConfigs', 'http' => [ 'method' => 'GET', 'requestUri' => '/2019-09-30/functions/{FunctionName}/provisioned-concurrency?List=ALL', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListProvisionedConcurrencyConfigsRequest', ], 'output' => [ 'shape' => 'ListProvisionedConcurrencyConfigsResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ServiceException', ], ], ], 'ListTags' => [ 'name' => 'ListTags', 'http' => [ 'method' => 'GET', 'requestUri' => '/2017-03-31/tags/{ARN}', ], 'input' => [ 'shape' => 'ListTagsRequest', ], 'output' => [ 'shape' => 'ListTagsResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'ListVersionsByFunction' => [ 'name' => 'ListVersionsByFunction', 'http' => [ 'method' => 'GET', 'requestUri' => '/2015-03-31/functions/{FunctionName}/versions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListVersionsByFunctionRequest', ], 'output' => [ 'shape' => 'ListVersionsByFunctionResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'PublishLayerVersion' => [ 'name' => 'PublishLayerVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/2018-10-31/layers/{LayerName}/versions', 'responseCode' => 201, ], 'input' => [ 'shape' => 'PublishLayerVersionRequest', ], 'output' => [ 'shape' => 'PublishLayerVersionResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'CodeStorageExceededException', ], ], ], 'PublishVersion' => [ 'name' => 'PublishVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/2015-03-31/functions/{FunctionName}/versions', 'responseCode' => 201, ], 'input' => [ 'shape' => 'PublishVersionRequest', ], 'output' => [ 'shape' => 'FunctionConfiguration', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'CodeStorageExceededException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'ResourceConflictException', ], ], ], 'PutFunctionCodeSigningConfig' => [ 'name' => 'PutFunctionCodeSigningConfig', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2020-06-30/functions/{FunctionName}/code-signing-config', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutFunctionCodeSigningConfigRequest', ], 'output' => [ 'shape' => 'PutFunctionCodeSigningConfigResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ResourceConflictException', ], [ 'shape' => 'CodeSigningConfigNotFoundException', ], ], ], 'PutFunctionConcurrency' => [ 'name' => 'PutFunctionConcurrency', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2017-10-31/functions/{FunctionName}/concurrency', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutFunctionConcurrencyRequest', ], 'output' => [ 'shape' => 'Concurrency', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ResourceConflictException', ], ], ], 'PutFunctionEventInvokeConfig' => [ 'name' => 'PutFunctionEventInvokeConfig', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2019-09-25/functions/{FunctionName}/event-invoke-config', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutFunctionEventInvokeConfigRequest', ], 'output' => [ 'shape' => 'FunctionEventInvokeConfig', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ResourceConflictException', ], ], ], 'PutFunctionRecursionConfig' => [ 'name' => 'PutFunctionRecursionConfig', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2024-08-31/functions/{FunctionName}/recursion-config', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutFunctionRecursionConfigRequest', ], 'output' => [ 'shape' => 'PutFunctionRecursionConfigResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ServiceException', ], ], ], 'PutProvisionedConcurrencyConfig' => [ 'name' => 'PutProvisionedConcurrencyConfig', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2019-09-30/functions/{FunctionName}/provisioned-concurrency', 'responseCode' => 202, ], 'input' => [ 'shape' => 'PutProvisionedConcurrencyConfigRequest', ], 'output' => [ 'shape' => 'PutProvisionedConcurrencyConfigResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceConflictException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ServiceException', ], ], ], 'PutRuntimeManagementConfig' => [ 'name' => 'PutRuntimeManagementConfig', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2021-07-20/functions/{FunctionName}/runtime-management-config', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutRuntimeManagementConfigRequest', ], 'output' => [ 'shape' => 'PutRuntimeManagementConfigResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceConflictException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'RemoveLayerVersionPermission' => [ 'name' => 'RemoveLayerVersionPermission', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2018-10-31/layers/{LayerName}/versions/{VersionNumber}/policy/{StatementId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'RemoveLayerVersionPermissionRequest', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'PreconditionFailedException', ], ], ], 'RemovePermission' => [ 'name' => 'RemovePermission', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2015-03-31/functions/{FunctionName}/policy/{StatementId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'RemovePermissionRequest', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'PreconditionFailedException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/2017-03-31/tags/{ARN}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ResourceConflictException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2017-03-31/tags/{ARN}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ResourceConflictException', ], ], ], 'UpdateAlias' => [ 'name' => 'UpdateAlias', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2015-03-31/functions/{FunctionName}/aliases/{Name}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateAliasRequest', ], 'output' => [ 'shape' => 'AliasConfiguration', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'ResourceConflictException', ], ], ], 'UpdateCodeSigningConfig' => [ 'name' => 'UpdateCodeSigningConfig', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2020-04-22/code-signing-configs/{CodeSigningConfigArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateCodeSigningConfigRequest', ], 'output' => [ 'shape' => 'UpdateCodeSigningConfigResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UpdateEventSourceMapping' => [ 'name' => 'UpdateEventSourceMapping', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2015-03-31/event-source-mappings/{UUID}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'UpdateEventSourceMappingRequest', ], 'output' => [ 'shape' => 'EventSourceMappingConfiguration', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ResourceConflictException', ], [ 'shape' => 'ResourceInUseException', ], ], ], 'UpdateFunctionCode' => [ 'name' => 'UpdateFunctionCode', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2015-03-31/functions/{FunctionName}/code', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateFunctionCodeRequest', ], 'output' => [ 'shape' => 'FunctionConfiguration', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'CodeStorageExceededException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'ResourceConflictException', ], [ 'shape' => 'CodeVerificationFailedException', ], [ 'shape' => 'InvalidCodeSignatureException', ], [ 'shape' => 'CodeSigningConfigNotFoundException', ], ], ], 'UpdateFunctionConfiguration' => [ 'name' => 'UpdateFunctionConfiguration', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2015-03-31/functions/{FunctionName}/configuration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateFunctionConfigurationRequest', ], 'output' => [ 'shape' => 'FunctionConfiguration', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ResourceConflictException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'CodeVerificationFailedException', ], [ 'shape' => 'InvalidCodeSignatureException', ], [ 'shape' => 'CodeSigningConfigNotFoundException', ], ], ], 'UpdateFunctionEventInvokeConfig' => [ 'name' => 'UpdateFunctionEventInvokeConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/2019-09-25/functions/{FunctionName}/event-invoke-config', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateFunctionEventInvokeConfigRequest', ], 'output' => [ 'shape' => 'FunctionEventInvokeConfig', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ResourceConflictException', ], ], ], 'UpdateFunctionUrlConfig' => [ 'name' => 'UpdateFunctionUrlConfig', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2021-10-31/functions/{FunctionName}/url', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateFunctionUrlConfigRequest', ], 'output' => [ 'shape' => 'UpdateFunctionUrlConfigResponse', ], 'errors' => [ [ 'shape' => 'ResourceConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], ], 'shapes' => [ 'AccountLimit' => [ 'type' => 'structure', 'members' => [ 'TotalCodeSize' => [ 'shape' => 'Long', ], 'CodeSizeUnzipped' => [ 'shape' => 'Long', ], 'CodeSizeZipped' => [ 'shape' => 'Long', ], 'ConcurrentExecutions' => [ 'shape' => 'Integer', ], 'UnreservedConcurrentExecutions' => [ 'shape' => 'UnreservedConcurrentExecutions', ], ], ], 'AccountUsage' => [ 'type' => 'structure', 'members' => [ 'TotalCodeSize' => [ 'shape' => 'Long', ], 'FunctionCount' => [ 'shape' => 'Long', ], ], ], 'Action' => [ 'type' => 'string', 'pattern' => '(lambda:[*]|lambda:[a-zA-Z]+|[*])', ], 'AddLayerVersionPermissionRequest' => [ 'type' => 'structure', 'required' => [ 'LayerName', 'VersionNumber', 'StatementId', 'Action', 'Principal', ], 'members' => [ 'LayerName' => [ 'shape' => 'LayerName', 'location' => 'uri', 'locationName' => 'LayerName', ], 'VersionNumber' => [ 'shape' => 'LayerVersionNumber', 'location' => 'uri', 'locationName' => 'VersionNumber', ], 'StatementId' => [ 'shape' => 'StatementId', ], 'Action' => [ 'shape' => 'LayerPermissionAllowedAction', ], 'Principal' => [ 'shape' => 'LayerPermissionAllowedPrincipal', ], 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'RevisionId' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'RevisionId', ], ], ], 'AddLayerVersionPermissionResponse' => [ 'type' => 'structure', 'members' => [ 'Statement' => [ 'shape' => 'String', ], 'RevisionId' => [ 'shape' => 'String', ], ], ], 'AddPermissionRequest' => [ 'type' => 'structure', 'required' => [ 'FunctionName', 'StatementId', 'Action', 'Principal', ], 'members' => [ 'FunctionName' => [ 'shape' => 'FunctionName', 'location' => 'uri', 'locationName' => 'FunctionName', ], 'StatementId' => [ 'shape' => 'StatementId', ], 'Action' => [ 'shape' => 'Action', ], 'Principal' => [ 'shape' => 'Principal', ], 'SourceArn' => [ 'shape' => 'Arn', ], 'SourceAccount' => [ 'shape' => 'SourceOwner', ], 'EventSourceToken' => [ 'shape' => 'EventSourceToken', ], 'Qualifier' => [ 'shape' => 'Qualifier', 'location' => 'querystring', 'locationName' => 'Qualifier', ], 'RevisionId' => [ 'shape' => 'String', ], 'PrincipalOrgID' => [ 'shape' => 'PrincipalOrgID', ], 'FunctionUrlAuthType' => [ 'shape' => 'FunctionUrlAuthType', ], ], ], 'AddPermissionResponse' => [ 'type' => 'structure', 'members' => [ 'Statement' => [ 'shape' => 'String', ], ], ], 'AdditionalVersion' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '[0-9]+', ], 'AdditionalVersionWeights' => [ 'type' => 'map', 'key' => [ 'shape' => 'AdditionalVersion', ], 'value' => [ 'shape' => 'Weight', ], ], 'Alias' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '(?!^[0-9]+$)([a-zA-Z0-9-_]+)', ], 'AliasConfiguration' => [ 'type' => 'structure', 'members' => [ 'AliasArn' => [ 'shape' => 'FunctionArn', ], 'Name' => [ 'shape' => 'Alias', ], 'FunctionVersion' => [ 'shape' => 'Version', ], 'Description' => [ 'shape' => 'Description', ], 'RoutingConfig' => [ 'shape' => 'AliasRoutingConfiguration', ], 'RevisionId' => [ 'shape' => 'String', ], ], ], 'AliasList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AliasConfiguration', ], ], 'AliasRoutingConfiguration' => [ 'type' => 'structure', 'members' => [ 'AdditionalVersionWeights' => [ 'shape' => 'AdditionalVersionWeights', ], ], ], 'AllowCredentials' => [ 'type' => 'boolean', ], 'AllowMethodsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Method', ], 'max' => 6, ], 'AllowOriginsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Origin', ], 'max' => 100, ], 'AllowedPublishers' => [ 'type' => 'structure', 'required' => [ 'SigningProfileVersionArns', ], 'members' => [ 'SigningProfileVersionArns' => [ 'shape' => 'SigningProfileVersionArns', ], ], ], 'AmazonManagedKafkaEventSourceConfig' => [ 'type' => 'structure', 'members' => [ 'ConsumerGroupId' => [ 'shape' => 'URI', ], ], ], 'ApplicationLogLevel' => [ 'type' => 'string', 'enum' => [ 'TRACE', 'DEBUG', 'INFO', 'WARN', 'ERROR', 'FATAL', ], ], 'Architecture' => [ 'type' => 'string', 'enum' => [ 'x86_64', 'arm64', ], ], 'ArchitecturesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Architecture', ], 'max' => 1, 'min' => 1, ], 'Arn' => [ 'type' => 'string', 'pattern' => 'arn:(aws[a-zA-Z0-9-]*):([a-zA-Z0-9\\-])+:([a-z]{2}(-gov)?-[a-z]+-\\d{1})?:(\\d{12})?:(.*)', ], 'BatchSize' => [ 'type' => 'integer', 'max' => 10000, 'min' => 1, ], 'BisectBatchOnFunctionError' => [ 'type' => 'boolean', ], 'Blob' => [ 'type' => 'blob', 'sensitive' => true, ], 'BlobStream' => [ 'type' => 'blob', 'streaming' => true, ], 'Boolean' => [ 'type' => 'boolean', ], 'CodeSigningConfig' => [ 'type' => 'structure', 'required' => [ 'CodeSigningConfigId', 'CodeSigningConfigArn', 'AllowedPublishers', 'CodeSigningPolicies', 'LastModified', ], 'members' => [ 'CodeSigningConfigId' => [ 'shape' => 'CodeSigningConfigId', ], 'CodeSigningConfigArn' => [ 'shape' => 'CodeSigningConfigArn', ], 'Description' => [ 'shape' => 'Description', ], 'AllowedPublishers' => [ 'shape' => 'AllowedPublishers', ], 'CodeSigningPolicies' => [ 'shape' => 'CodeSigningPolicies', ], 'LastModified' => [ 'shape' => 'Timestamp', ], ], ], 'CodeSigningConfigArn' => [ 'type' => 'string', 'max' => 200, 'pattern' => 'arn:(aws[a-zA-Z-]*)?:lambda:[a-z]{2}((-gov)|(-iso(b?)))?-[a-z]+-\\d{1}:\\d{12}:code-signing-config:csc-[a-z0-9]{17}', ], 'CodeSigningConfigId' => [ 'type' => 'string', 'pattern' => 'csc-[a-zA-Z0-9-_\\.]{17}', ], 'CodeSigningConfigList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CodeSigningConfig', ], ], 'CodeSigningConfigNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'String', ], 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'CodeSigningPolicies' => [ 'type' => 'structure', 'members' => [ 'UntrustedArtifactOnDeployment' => [ 'shape' => 'CodeSigningPolicy', ], ], ], 'CodeSigningPolicy' => [ 'type' => 'string', 'enum' => [ 'Warn', 'Enforce', ], ], 'CodeStorageExceededException' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'String', ], 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'CodeVerificationFailedException' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'String', ], 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'CollectionName' => [ 'type' => 'string', 'max' => 57, 'min' => 1, 'pattern' => '(^(?!(system\\x2e)))(^[_a-zA-Z0-9])([^$]*)', ], 'CompatibleArchitectures' => [ 'type' => 'list', 'member' => [ 'shape' => 'Architecture', ], 'max' => 2, ], 'CompatibleRuntimes' => [ 'type' => 'list', 'member' => [ 'shape' => 'Runtime', ], 'max' => 15, ], 'Concurrency' => [ 'type' => 'structure', 'members' => [ 'ReservedConcurrentExecutions' => [ 'shape' => 'ReservedConcurrentExecutions', ], ], ], 'Cors' => [ 'type' => 'structure', 'members' => [ 'AllowCredentials' => [ 'shape' => 'AllowCredentials', ], 'AllowHeaders' => [ 'shape' => 'HeadersList', ], 'AllowMethods' => [ 'shape' => 'AllowMethodsList', ], 'AllowOrigins' => [ 'shape' => 'AllowOriginsList', ], 'ExposeHeaders' => [ 'shape' => 'HeadersList', ], 'MaxAge' => [ 'shape' => 'MaxAge', ], ], ], 'CreateAliasRequest' => [ 'type' => 'structure', 'required' => [ 'FunctionName', 'Name', 'FunctionVersion', ], 'members' => [ 'FunctionName' => [ 'shape' => 'FunctionName', 'location' => 'uri', 'locationName' => 'FunctionName', ], 'Name' => [ 'shape' => 'Alias', ], 'FunctionVersion' => [ 'shape' => 'Version', ], 'Description' => [ 'shape' => 'Description', ], 'RoutingConfig' => [ 'shape' => 'AliasRoutingConfiguration', ], ], ], 'CreateCodeSigningConfigRequest' => [ 'type' => 'structure', 'required' => [ 'AllowedPublishers', ], 'members' => [ 'Description' => [ 'shape' => 'Description', ], 'AllowedPublishers' => [ 'shape' => 'AllowedPublishers', ], 'CodeSigningPolicies' => [ 'shape' => 'CodeSigningPolicies', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'CreateCodeSigningConfigResponse' => [ 'type' => 'structure', 'required' => [ 'CodeSigningConfig', ], 'members' => [ 'CodeSigningConfig' => [ 'shape' => 'CodeSigningConfig', ], ], ], 'CreateEventSourceMappingRequest' => [ 'type' => 'structure', 'required' => [ 'FunctionName', ], 'members' => [ 'EventSourceArn' => [ 'shape' => 'Arn', ], 'FunctionName' => [ 'shape' => 'FunctionName', ], 'Enabled' => [ 'shape' => 'Enabled', ], 'BatchSize' => [ 'shape' => 'BatchSize', ], 'FilterCriteria' => [ 'shape' => 'FilterCriteria', ], 'MaximumBatchingWindowInSeconds' => [ 'shape' => 'MaximumBatchingWindowInSeconds', ], 'ParallelizationFactor' => [ 'shape' => 'ParallelizationFactor', ], 'StartingPosition' => [ 'shape' => 'EventSourcePosition', ], 'StartingPositionTimestamp' => [ 'shape' => 'Date', ], 'DestinationConfig' => [ 'shape' => 'DestinationConfig', ], 'MaximumRecordAgeInSeconds' => [ 'shape' => 'MaximumRecordAgeInSeconds', ], 'BisectBatchOnFunctionError' => [ 'shape' => 'BisectBatchOnFunctionError', ], 'MaximumRetryAttempts' => [ 'shape' => 'MaximumRetryAttemptsEventSourceMapping', ], 'Tags' => [ 'shape' => 'Tags', ], 'TumblingWindowInSeconds' => [ 'shape' => 'TumblingWindowInSeconds', ], 'Topics' => [ 'shape' => 'Topics', ], 'Queues' => [ 'shape' => 'Queues', ], 'SourceAccessConfigurations' => [ 'shape' => 'SourceAccessConfigurations', ], 'SelfManagedEventSource' => [ 'shape' => 'SelfManagedEventSource', ], 'FunctionResponseTypes' => [ 'shape' => 'FunctionResponseTypeList', ], 'AmazonManagedKafkaEventSourceConfig' => [ 'shape' => 'AmazonManagedKafkaEventSourceConfig', ], 'SelfManagedKafkaEventSourceConfig' => [ 'shape' => 'SelfManagedKafkaEventSourceConfig', ], 'ScalingConfig' => [ 'shape' => 'ScalingConfig', ], 'DocumentDBEventSourceConfig' => [ 'shape' => 'DocumentDBEventSourceConfig', ], 'KMSKeyArn' => [ 'shape' => 'KMSKeyArn', ], 'MetricsConfig' => [ 'shape' => 'EventSourceMappingMetricsConfig', ], 'ProvisionedPollerConfig' => [ 'shape' => 'ProvisionedPollerConfig', ], ], ], 'CreateFunctionRequest' => [ 'type' => 'structure', 'required' => [ 'FunctionName', 'Role', 'Code', ], 'members' => [ 'FunctionName' => [ 'shape' => 'FunctionName', ], 'Runtime' => [ 'shape' => 'Runtime', ], 'Role' => [ 'shape' => 'RoleArn', ], 'Handler' => [ 'shape' => 'Handler', ], 'Code' => [ 'shape' => 'FunctionCode', ], 'Description' => [ 'shape' => 'Description', ], 'Timeout' => [ 'shape' => 'Timeout', ], 'MemorySize' => [ 'shape' => 'MemorySize', ], 'Publish' => [ 'shape' => 'Boolean', ], 'VpcConfig' => [ 'shape' => 'VpcConfig', ], 'PackageType' => [ 'shape' => 'PackageType', ], 'DeadLetterConfig' => [ 'shape' => 'DeadLetterConfig', ], 'Environment' => [ 'shape' => 'Environment', ], 'KMSKeyArn' => [ 'shape' => 'KMSKeyArn', ], 'TracingConfig' => [ 'shape' => 'TracingConfig', ], 'Tags' => [ 'shape' => 'Tags', ], 'Layers' => [ 'shape' => 'LayerList', ], 'FileSystemConfigs' => [ 'shape' => 'FileSystemConfigList', ], 'ImageConfig' => [ 'shape' => 'ImageConfig', ], 'CodeSigningConfigArn' => [ 'shape' => 'CodeSigningConfigArn', ], 'Architectures' => [ 'shape' => 'ArchitecturesList', ], 'EphemeralStorage' => [ 'shape' => 'EphemeralStorage', ], 'SnapStart' => [ 'shape' => 'SnapStart', ], 'LoggingConfig' => [ 'shape' => 'LoggingConfig', ], ], ], 'CreateFunctionUrlConfigRequest' => [ 'type' => 'structure', 'required' => [ 'FunctionName', 'AuthType', ], 'members' => [ 'FunctionName' => [ 'shape' => 'FunctionName', 'location' => 'uri', 'locationName' => 'FunctionName', ], 'Qualifier' => [ 'shape' => 'FunctionUrlQualifier', 'location' => 'querystring', 'locationName' => 'Qualifier', ], 'AuthType' => [ 'shape' => 'FunctionUrlAuthType', ], 'Cors' => [ 'shape' => 'Cors', ], 'InvokeMode' => [ 'shape' => 'InvokeMode', ], ], ], 'CreateFunctionUrlConfigResponse' => [ 'type' => 'structure', 'required' => [ 'FunctionUrl', 'FunctionArn', 'AuthType', 'CreationTime', ], 'members' => [ 'FunctionUrl' => [ 'shape' => 'FunctionUrl', ], 'FunctionArn' => [ 'shape' => 'FunctionArn', ], 'AuthType' => [ 'shape' => 'FunctionUrlAuthType', ], 'Cors' => [ 'shape' => 'Cors', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'InvokeMode' => [ 'shape' => 'InvokeMode', ], ], ], 'DatabaseName' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '[^ /\\.$\\x22]*', ], 'Date' => [ 'type' => 'timestamp', ], 'DeadLetterConfig' => [ 'type' => 'structure', 'members' => [ 'TargetArn' => [ 'shape' => 'ResourceArn', ], ], ], 'DeleteAliasRequest' => [ 'type' => 'structure', 'required' => [ 'FunctionName', 'Name', ], 'members' => [ 'FunctionName' => [ 'shape' => 'FunctionName', 'location' => 'uri', 'locationName' => 'FunctionName', ], 'Name' => [ 'shape' => 'Alias', 'location' => 'uri', 'locationName' => 'Name', ], ], ], 'DeleteCodeSigningConfigRequest' => [ 'type' => 'structure', 'required' => [ 'CodeSigningConfigArn', ], 'members' => [ 'CodeSigningConfigArn' => [ 'shape' => 'CodeSigningConfigArn', 'location' => 'uri', 'locationName' => 'CodeSigningConfigArn', ], ], ], 'DeleteCodeSigningConfigResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteEventSourceMappingRequest' => [ 'type' => 'structure', 'required' => [ 'UUID', ], 'members' => [ 'UUID' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'UUID', ], ], ], 'DeleteFunctionCodeSigningConfigRequest' => [ 'type' => 'structure', 'required' => [ 'FunctionName', ], 'members' => [ 'FunctionName' => [ 'shape' => 'FunctionName', 'location' => 'uri', 'locationName' => 'FunctionName', ], ], ], 'DeleteFunctionConcurrencyRequest' => [ 'type' => 'structure', 'required' => [ 'FunctionName', ], 'members' => [ 'FunctionName' => [ 'shape' => 'FunctionName', 'location' => 'uri', 'locationName' => 'FunctionName', ], ], ], 'DeleteFunctionEventInvokeConfigRequest' => [ 'type' => 'structure', 'required' => [ 'FunctionName', ], 'members' => [ 'FunctionName' => [ 'shape' => 'FunctionName', 'location' => 'uri', 'locationName' => 'FunctionName', ], 'Qualifier' => [ 'shape' => 'Qualifier', 'location' => 'querystring', 'locationName' => 'Qualifier', ], ], ], 'DeleteFunctionRequest' => [ 'type' => 'structure', 'required' => [ 'FunctionName', ], 'members' => [ 'FunctionName' => [ 'shape' => 'FunctionName', 'location' => 'uri', 'locationName' => 'FunctionName', ], 'Qualifier' => [ 'shape' => 'Qualifier', 'location' => 'querystring', 'locationName' => 'Qualifier', ], ], ], 'DeleteFunctionUrlConfigRequest' => [ 'type' => 'structure', 'required' => [ 'FunctionName', ], 'members' => [ 'FunctionName' => [ 'shape' => 'FunctionName', 'location' => 'uri', 'locationName' => 'FunctionName', ], 'Qualifier' => [ 'shape' => 'FunctionUrlQualifier', 'location' => 'querystring', 'locationName' => 'Qualifier', ], ], ], 'DeleteLayerVersionRequest' => [ 'type' => 'structure', 'required' => [ 'LayerName', 'VersionNumber', ], 'members' => [ 'LayerName' => [ 'shape' => 'LayerName', 'location' => 'uri', 'locationName' => 'LayerName', ], 'VersionNumber' => [ 'shape' => 'LayerVersionNumber', 'location' => 'uri', 'locationName' => 'VersionNumber', ], ], ], 'DeleteProvisionedConcurrencyConfigRequest' => [ 'type' => 'structure', 'required' => [ 'FunctionName', 'Qualifier', ], 'members' => [ 'FunctionName' => [ 'shape' => 'FunctionName', 'location' => 'uri', 'locationName' => 'FunctionName', ], 'Qualifier' => [ 'shape' => 'Qualifier', 'location' => 'querystring', 'locationName' => 'Qualifier', ], ], ], 'Description' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'DestinationArn' => [ 'type' => 'string', 'max' => 350, 'min' => 0, 'pattern' => '^$|arn:(aws[a-zA-Z0-9-]*):([a-zA-Z0-9\\-])+:([a-z]{2}(-gov)?-[a-z]+-\\d{1})?:(\\d{12})?:(.*)', ], 'DestinationConfig' => [ 'type' => 'structure', 'members' => [ 'OnSuccess' => [ 'shape' => 'OnSuccess', ], 'OnFailure' => [ 'shape' => 'OnFailure', ], ], ], 'DocumentDBEventSourceConfig' => [ 'type' => 'structure', 'members' => [ 'DatabaseName' => [ 'shape' => 'DatabaseName', ], 'CollectionName' => [ 'shape' => 'CollectionName', ], 'FullDocument' => [ 'shape' => 'FullDocument', ], ], ], 'EC2AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'String', ], 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 502, ], 'exception' => true, ], 'EC2ThrottledException' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'String', ], 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 502, ], 'exception' => true, ], 'EC2UnexpectedException' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'String', ], 'Message' => [ 'shape' => 'String', ], 'EC2ErrorCode' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 502, ], 'exception' => true, ], 'EFSIOException' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'String', ], 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 410, ], 'exception' => true, ], 'EFSMountConnectivityException' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'String', ], 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 408, ], 'exception' => true, ], 'EFSMountFailureException' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'String', ], 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 403, ], 'exception' => true, ], 'EFSMountTimeoutException' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'String', ], 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 408, ], 'exception' => true, ], 'ENILimitReachedException' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'String', ], 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 502, ], 'exception' => true, ], 'Enabled' => [ 'type' => 'boolean', ], 'EndPointType' => [ 'type' => 'string', 'enum' => [ 'KAFKA_BOOTSTRAP_SERVERS', ], ], 'Endpoint' => [ 'type' => 'string', 'max' => 300, 'min' => 1, 'pattern' => '^(([a-zA-Z0-9]|[a-zA-Z0-9][a-zA-Z0-9\\-]*[a-zA-Z0-9])\\.)*([A-Za-z0-9]|[A-Za-z0-9][A-Za-z0-9\\-]*[A-Za-z0-9]):[0-9]{1,5}', ], 'EndpointLists' => [ 'type' => 'list', 'member' => [ 'shape' => 'Endpoint', ], 'max' => 10, 'min' => 1, ], 'Endpoints' => [ 'type' => 'map', 'key' => [ 'shape' => 'EndPointType', ], 'value' => [ 'shape' => 'EndpointLists', ], 'max' => 2, 'min' => 1, ], 'Environment' => [ 'type' => 'structure', 'members' => [ 'Variables' => [ 'shape' => 'EnvironmentVariables', ], ], ], 'EnvironmentError' => [ 'type' => 'structure', 'members' => [ 'ErrorCode' => [ 'shape' => 'String', ], 'Message' => [ 'shape' => 'SensitiveString', ], ], ], 'EnvironmentResponse' => [ 'type' => 'structure', 'members' => [ 'Variables' => [ 'shape' => 'EnvironmentVariables', ], 'Error' => [ 'shape' => 'EnvironmentError', ], ], ], 'EnvironmentVariableName' => [ 'type' => 'string', 'pattern' => '[a-zA-Z]([a-zA-Z0-9_])+', 'sensitive' => true, ], 'EnvironmentVariableValue' => [ 'type' => 'string', 'sensitive' => true, ], 'EnvironmentVariables' => [ 'type' => 'map', 'key' => [ 'shape' => 'EnvironmentVariableName', ], 'value' => [ 'shape' => 'EnvironmentVariableValue', ], 'sensitive' => true, ], 'EphemeralStorage' => [ 'type' => 'structure', 'required' => [ 'Size', ], 'members' => [ 'Size' => [ 'shape' => 'EphemeralStorageSize', ], ], ], 'EphemeralStorageSize' => [ 'type' => 'integer', 'max' => 10240, 'min' => 512, ], 'EventSourceMappingArn' => [ 'type' => 'string', 'max' => 120, 'min' => 85, 'pattern' => 'arn:(aws[a-zA-Z-]*)?:lambda:[a-z]{2}((-gov)|(-iso([a-z]?)))?-[a-z]+-\\d{1}:\\d{12}:event-source-mapping:[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}', ], 'EventSourceMappingConfiguration' => [ 'type' => 'structure', 'members' => [ 'UUID' => [ 'shape' => 'String', ], 'StartingPosition' => [ 'shape' => 'EventSourcePosition', ], 'StartingPositionTimestamp' => [ 'shape' => 'Date', ], 'BatchSize' => [ 'shape' => 'BatchSize', ], 'MaximumBatchingWindowInSeconds' => [ 'shape' => 'MaximumBatchingWindowInSeconds', ], 'ParallelizationFactor' => [ 'shape' => 'ParallelizationFactor', ], 'EventSourceArn' => [ 'shape' => 'Arn', ], 'FilterCriteria' => [ 'shape' => 'FilterCriteria', ], 'FunctionArn' => [ 'shape' => 'FunctionArn', ], 'LastModified' => [ 'shape' => 'Date', ], 'LastProcessingResult' => [ 'shape' => 'String', ], 'State' => [ 'shape' => 'String', ], 'StateTransitionReason' => [ 'shape' => 'String', ], 'DestinationConfig' => [ 'shape' => 'DestinationConfig', ], 'Topics' => [ 'shape' => 'Topics', ], 'Queues' => [ 'shape' => 'Queues', ], 'SourceAccessConfigurations' => [ 'shape' => 'SourceAccessConfigurations', ], 'SelfManagedEventSource' => [ 'shape' => 'SelfManagedEventSource', ], 'MaximumRecordAgeInSeconds' => [ 'shape' => 'MaximumRecordAgeInSeconds', ], 'BisectBatchOnFunctionError' => [ 'shape' => 'BisectBatchOnFunctionError', ], 'MaximumRetryAttempts' => [ 'shape' => 'MaximumRetryAttemptsEventSourceMapping', ], 'TumblingWindowInSeconds' => [ 'shape' => 'TumblingWindowInSeconds', ], 'FunctionResponseTypes' => [ 'shape' => 'FunctionResponseTypeList', ], 'AmazonManagedKafkaEventSourceConfig' => [ 'shape' => 'AmazonManagedKafkaEventSourceConfig', ], 'SelfManagedKafkaEventSourceConfig' => [ 'shape' => 'SelfManagedKafkaEventSourceConfig', ], 'ScalingConfig' => [ 'shape' => 'ScalingConfig', ], 'DocumentDBEventSourceConfig' => [ 'shape' => 'DocumentDBEventSourceConfig', ], 'KMSKeyArn' => [ 'shape' => 'KMSKeyArn', ], 'FilterCriteriaError' => [ 'shape' => 'FilterCriteriaError', ], 'EventSourceMappingArn' => [ 'shape' => 'EventSourceMappingArn', ], 'MetricsConfig' => [ 'shape' => 'EventSourceMappingMetricsConfig', ], 'ProvisionedPollerConfig' => [ 'shape' => 'ProvisionedPollerConfig', ], ], ], 'EventSourceMappingMetric' => [ 'type' => 'string', 'enum' => [ 'EventCount', ], ], 'EventSourceMappingMetricList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EventSourceMappingMetric', ], 'max' => 1, 'min' => 0, ], 'EventSourceMappingMetricsConfig' => [ 'type' => 'structure', 'members' => [ 'Metrics' => [ 'shape' => 'EventSourceMappingMetricList', ], ], ], 'EventSourceMappingsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EventSourceMappingConfiguration', ], ], 'EventSourcePosition' => [ 'type' => 'string', 'enum' => [ 'TRIM_HORIZON', 'LATEST', 'AT_TIMESTAMP', ], ], 'EventSourceToken' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '[a-zA-Z0-9._\\-]+', ], 'FileSystemArn' => [ 'type' => 'string', 'max' => 200, 'pattern' => 'arn:aws[a-zA-Z-]*:elasticfilesystem:[a-z]{2}((-gov)|(-iso(b?)))?-[a-z]+-\\d{1}:\\d{12}:access-point/fsap-[a-f0-9]{17}', ], 'FileSystemConfig' => [ 'type' => 'structure', 'required' => [ 'Arn', 'LocalMountPath', ], 'members' => [ 'Arn' => [ 'shape' => 'FileSystemArn', ], 'LocalMountPath' => [ 'shape' => 'LocalMountPath', ], ], ], 'FileSystemConfigList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FileSystemConfig', ], 'max' => 1, ], 'Filter' => [ 'type' => 'structure', 'members' => [ 'Pattern' => [ 'shape' => 'Pattern', ], ], ], 'FilterCriteria' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'FilterList', ], ], ], 'FilterCriteriaError' => [ 'type' => 'structure', 'members' => [ 'ErrorCode' => [ 'shape' => 'FilterCriteriaErrorCode', ], 'Message' => [ 'shape' => 'FilterCriteriaErrorMessage', ], ], ], 'FilterCriteriaErrorCode' => [ 'type' => 'string', 'max' => 50, 'min' => 10, 'pattern' => '[A-Za-z]+Exception', ], 'FilterCriteriaErrorMessage' => [ 'type' => 'string', 'max' => 2048, 'min' => 10, 'pattern' => '.*', ], 'FilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Filter', ], ], 'FullDocument' => [ 'type' => 'string', 'enum' => [ 'UpdateLookup', 'Default', ], ], 'FunctionArn' => [ 'type' => 'string', 'pattern' => 'arn:(aws[a-zA-Z-]*)?:lambda:[a-z]{2}(-gov)?-[a-z]+-\\d{1}:\\d{12}:function:[a-zA-Z0-9-_]+(:(\\$LATEST|[a-zA-Z0-9-_]+))?', ], 'FunctionArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FunctionArn', ], ], 'FunctionCode' => [ 'type' => 'structure', 'members' => [ 'ZipFile' => [ 'shape' => 'Blob', ], 'S3Bucket' => [ 'shape' => 'S3Bucket', ], 'S3Key' => [ 'shape' => 'S3Key', ], 'S3ObjectVersion' => [ 'shape' => 'S3ObjectVersion', ], 'ImageUri' => [ 'shape' => 'String', ], 'SourceKMSKeyArn' => [ 'shape' => 'KMSKeyArn', ], ], ], 'FunctionCodeLocation' => [ 'type' => 'structure', 'members' => [ 'RepositoryType' => [ 'shape' => 'String', ], 'Location' => [ 'shape' => 'String', ], 'ImageUri' => [ 'shape' => 'String', ], 'ResolvedImageUri' => [ 'shape' => 'String', ], 'SourceKMSKeyArn' => [ 'shape' => 'String', ], ], ], 'FunctionConfiguration' => [ 'type' => 'structure', 'members' => [ 'FunctionName' => [ 'shape' => 'NamespacedFunctionName', ], 'FunctionArn' => [ 'shape' => 'NameSpacedFunctionArn', ], 'Runtime' => [ 'shape' => 'Runtime', ], 'Role' => [ 'shape' => 'RoleArn', ], 'Handler' => [ 'shape' => 'Handler', ], 'CodeSize' => [ 'shape' => 'Long', ], 'Description' => [ 'shape' => 'Description', ], 'Timeout' => [ 'shape' => 'Timeout', ], 'MemorySize' => [ 'shape' => 'MemorySize', ], 'LastModified' => [ 'shape' => 'Timestamp', ], 'CodeSha256' => [ 'shape' => 'String', ], 'Version' => [ 'shape' => 'Version', ], 'VpcConfig' => [ 'shape' => 'VpcConfigResponse', ], 'DeadLetterConfig' => [ 'shape' => 'DeadLetterConfig', ], 'Environment' => [ 'shape' => 'EnvironmentResponse', ], 'KMSKeyArn' => [ 'shape' => 'KMSKeyArn', ], 'TracingConfig' => [ 'shape' => 'TracingConfigResponse', ], 'MasterArn' => [ 'shape' => 'FunctionArn', ], 'RevisionId' => [ 'shape' => 'String', ], 'Layers' => [ 'shape' => 'LayersReferenceList', ], 'State' => [ 'shape' => 'State', ], 'StateReason' => [ 'shape' => 'StateReason', ], 'StateReasonCode' => [ 'shape' => 'StateReasonCode', ], 'LastUpdateStatus' => [ 'shape' => 'LastUpdateStatus', ], 'LastUpdateStatusReason' => [ 'shape' => 'LastUpdateStatusReason', ], 'LastUpdateStatusReasonCode' => [ 'shape' => 'LastUpdateStatusReasonCode', ], 'FileSystemConfigs' => [ 'shape' => 'FileSystemConfigList', ], 'PackageType' => [ 'shape' => 'PackageType', ], 'ImageConfigResponse' => [ 'shape' => 'ImageConfigResponse', ], 'SigningProfileVersionArn' => [ 'shape' => 'Arn', ], 'SigningJobArn' => [ 'shape' => 'Arn', ], 'Architectures' => [ 'shape' => 'ArchitecturesList', ], 'EphemeralStorage' => [ 'shape' => 'EphemeralStorage', ], 'SnapStart' => [ 'shape' => 'SnapStartResponse', ], 'RuntimeVersionConfig' => [ 'shape' => 'RuntimeVersionConfig', ], 'LoggingConfig' => [ 'shape' => 'LoggingConfig', ], ], ], 'FunctionEventInvokeConfig' => [ 'type' => 'structure', 'members' => [ 'LastModified' => [ 'shape' => 'Date', ], 'FunctionArn' => [ 'shape' => 'FunctionArn', ], 'MaximumRetryAttempts' => [ 'shape' => 'MaximumRetryAttempts', ], 'MaximumEventAgeInSeconds' => [ 'shape' => 'MaximumEventAgeInSeconds', ], 'DestinationConfig' => [ 'shape' => 'DestinationConfig', ], ], ], 'FunctionEventInvokeConfigList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FunctionEventInvokeConfig', ], ], 'FunctionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FunctionConfiguration', ], ], 'FunctionName' => [ 'type' => 'string', 'max' => 140, 'min' => 1, 'pattern' => '(arn:(aws[a-zA-Z-]*)?:lambda:)?([a-z]{2}(-gov)?-[a-z]+-\\d{1}:)?(\\d{12}:)?(function:)?([a-zA-Z0-9-_]+)(:(\\$LATEST|[a-zA-Z0-9-_]+))?', ], 'FunctionResponseType' => [ 'type' => 'string', 'enum' => [ 'ReportBatchItemFailures', ], ], 'FunctionResponseTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FunctionResponseType', ], 'max' => 1, 'min' => 0, ], 'FunctionUrl' => [ 'type' => 'string', 'max' => 100, 'min' => 40, ], 'FunctionUrlAuthType' => [ 'type' => 'string', 'enum' => [ 'NONE', 'AWS_IAM', ], ], 'FunctionUrlConfig' => [ 'type' => 'structure', 'required' => [ 'FunctionUrl', 'FunctionArn', 'CreationTime', 'LastModifiedTime', 'AuthType', ], 'members' => [ 'FunctionUrl' => [ 'shape' => 'FunctionUrl', ], 'FunctionArn' => [ 'shape' => 'FunctionArn', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'Cors' => [ 'shape' => 'Cors', ], 'AuthType' => [ 'shape' => 'FunctionUrlAuthType', ], 'InvokeMode' => [ 'shape' => 'InvokeMode', ], ], ], 'FunctionUrlConfigList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FunctionUrlConfig', ], ], 'FunctionUrlQualifier' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '(^\\$LATEST$)|((?!^[0-9]+$)([a-zA-Z0-9-_]+))', ], 'FunctionVersion' => [ 'type' => 'string', 'enum' => [ 'ALL', ], ], 'GetAccountSettingsRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetAccountSettingsResponse' => [ 'type' => 'structure', 'members' => [ 'AccountLimit' => [ 'shape' => 'AccountLimit', ], 'AccountUsage' => [ 'shape' => 'AccountUsage', ], ], ], 'GetAliasRequest' => [ 'type' => 'structure', 'required' => [ 'FunctionName', 'Name', ], 'members' => [ 'FunctionName' => [ 'shape' => 'FunctionName', 'location' => 'uri', 'locationName' => 'FunctionName', ], 'Name' => [ 'shape' => 'Alias', 'location' => 'uri', 'locationName' => 'Name', ], ], ], 'GetCodeSigningConfigRequest' => [ 'type' => 'structure', 'required' => [ 'CodeSigningConfigArn', ], 'members' => [ 'CodeSigningConfigArn' => [ 'shape' => 'CodeSigningConfigArn', 'location' => 'uri', 'locationName' => 'CodeSigningConfigArn', ], ], ], 'GetCodeSigningConfigResponse' => [ 'type' => 'structure', 'required' => [ 'CodeSigningConfig', ], 'members' => [ 'CodeSigningConfig' => [ 'shape' => 'CodeSigningConfig', ], ], ], 'GetEventSourceMappingRequest' => [ 'type' => 'structure', 'required' => [ 'UUID', ], 'members' => [ 'UUID' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'UUID', ], ], ], 'GetFunctionCodeSigningConfigRequest' => [ 'type' => 'structure', 'required' => [ 'FunctionName', ], 'members' => [ 'FunctionName' => [ 'shape' => 'FunctionName', 'location' => 'uri', 'locationName' => 'FunctionName', ], ], ], 'GetFunctionCodeSigningConfigResponse' => [ 'type' => 'structure', 'required' => [ 'CodeSigningConfigArn', 'FunctionName', ], 'members' => [ 'CodeSigningConfigArn' => [ 'shape' => 'CodeSigningConfigArn', ], 'FunctionName' => [ 'shape' => 'FunctionName', ], ], ], 'GetFunctionConcurrencyRequest' => [ 'type' => 'structure', 'required' => [ 'FunctionName', ], 'members' => [ 'FunctionName' => [ 'shape' => 'FunctionName', 'location' => 'uri', 'locationName' => 'FunctionName', ], ], ], 'GetFunctionConcurrencyResponse' => [ 'type' => 'structure', 'members' => [ 'ReservedConcurrentExecutions' => [ 'shape' => 'ReservedConcurrentExecutions', ], ], ], 'GetFunctionConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'FunctionName', ], 'members' => [ 'FunctionName' => [ 'shape' => 'NamespacedFunctionName', 'location' => 'uri', 'locationName' => 'FunctionName', ], 'Qualifier' => [ 'shape' => 'Qualifier', 'location' => 'querystring', 'locationName' => 'Qualifier', ], ], ], 'GetFunctionEventInvokeConfigRequest' => [ 'type' => 'structure', 'required' => [ 'FunctionName', ], 'members' => [ 'FunctionName' => [ 'shape' => 'FunctionName', 'location' => 'uri', 'locationName' => 'FunctionName', ], 'Qualifier' => [ 'shape' => 'Qualifier', 'location' => 'querystring', 'locationName' => 'Qualifier', ], ], ], 'GetFunctionRecursionConfigRequest' => [ 'type' => 'structure', 'required' => [ 'FunctionName', ], 'members' => [ 'FunctionName' => [ 'shape' => 'UnqualifiedFunctionName', 'location' => 'uri', 'locationName' => 'FunctionName', ], ], ], 'GetFunctionRecursionConfigResponse' => [ 'type' => 'structure', 'members' => [ 'RecursiveLoop' => [ 'shape' => 'RecursiveLoop', ], ], ], 'GetFunctionRequest' => [ 'type' => 'structure', 'required' => [ 'FunctionName', ], 'members' => [ 'FunctionName' => [ 'shape' => 'NamespacedFunctionName', 'location' => 'uri', 'locationName' => 'FunctionName', ], 'Qualifier' => [ 'shape' => 'Qualifier', 'location' => 'querystring', 'locationName' => 'Qualifier', ], ], ], 'GetFunctionResponse' => [ 'type' => 'structure', 'members' => [ 'Configuration' => [ 'shape' => 'FunctionConfiguration', ], 'Code' => [ 'shape' => 'FunctionCodeLocation', ], 'Tags' => [ 'shape' => 'Tags', ], 'TagsError' => [ 'shape' => 'TagsError', ], 'Concurrency' => [ 'shape' => 'Concurrency', ], ], ], 'GetFunctionUrlConfigRequest' => [ 'type' => 'structure', 'required' => [ 'FunctionName', ], 'members' => [ 'FunctionName' => [ 'shape' => 'FunctionName', 'location' => 'uri', 'locationName' => 'FunctionName', ], 'Qualifier' => [ 'shape' => 'FunctionUrlQualifier', 'location' => 'querystring', 'locationName' => 'Qualifier', ], ], ], 'GetFunctionUrlConfigResponse' => [ 'type' => 'structure', 'required' => [ 'FunctionUrl', 'FunctionArn', 'AuthType', 'CreationTime', 'LastModifiedTime', ], 'members' => [ 'FunctionUrl' => [ 'shape' => 'FunctionUrl', ], 'FunctionArn' => [ 'shape' => 'FunctionArn', ], 'AuthType' => [ 'shape' => 'FunctionUrlAuthType', ], 'Cors' => [ 'shape' => 'Cors', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'InvokeMode' => [ 'shape' => 'InvokeMode', ], ], ], 'GetLayerVersionByArnRequest' => [ 'type' => 'structure', 'required' => [ 'Arn', ], 'members' => [ 'Arn' => [ 'shape' => 'LayerVersionArn', 'location' => 'querystring', 'locationName' => 'Arn', ], ], ], 'GetLayerVersionPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'LayerName', 'VersionNumber', ], 'members' => [ 'LayerName' => [ 'shape' => 'LayerName', 'location' => 'uri', 'locationName' => 'LayerName', ], 'VersionNumber' => [ 'shape' => 'LayerVersionNumber', 'location' => 'uri', 'locationName' => 'VersionNumber', ], ], ], 'GetLayerVersionPolicyResponse' => [ 'type' => 'structure', 'members' => [ 'Policy' => [ 'shape' => 'String', ], 'RevisionId' => [ 'shape' => 'String', ], ], ], 'GetLayerVersionRequest' => [ 'type' => 'structure', 'required' => [ 'LayerName', 'VersionNumber', ], 'members' => [ 'LayerName' => [ 'shape' => 'LayerName', 'location' => 'uri', 'locationName' => 'LayerName', ], 'VersionNumber' => [ 'shape' => 'LayerVersionNumber', 'location' => 'uri', 'locationName' => 'VersionNumber', ], ], ], 'GetLayerVersionResponse' => [ 'type' => 'structure', 'members' => [ 'Content' => [ 'shape' => 'LayerVersionContentOutput', ], 'LayerArn' => [ 'shape' => 'LayerArn', ], 'LayerVersionArn' => [ 'shape' => 'LayerVersionArn', ], 'Description' => [ 'shape' => 'Description', ], 'CreatedDate' => [ 'shape' => 'Timestamp', ], 'Version' => [ 'shape' => 'LayerVersionNumber', ], 'CompatibleRuntimes' => [ 'shape' => 'CompatibleRuntimes', ], 'LicenseInfo' => [ 'shape' => 'LicenseInfo', ], 'CompatibleArchitectures' => [ 'shape' => 'CompatibleArchitectures', ], ], ], 'GetPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'FunctionName', ], 'members' => [ 'FunctionName' => [ 'shape' => 'NamespacedFunctionName', 'location' => 'uri', 'locationName' => 'FunctionName', ], 'Qualifier' => [ 'shape' => 'Qualifier', 'location' => 'querystring', 'locationName' => 'Qualifier', ], ], ], 'GetPolicyResponse' => [ 'type' => 'structure', 'members' => [ 'Policy' => [ 'shape' => 'String', ], 'RevisionId' => [ 'shape' => 'String', ], ], ], 'GetProvisionedConcurrencyConfigRequest' => [ 'type' => 'structure', 'required' => [ 'FunctionName', 'Qualifier', ], 'members' => [ 'FunctionName' => [ 'shape' => 'FunctionName', 'location' => 'uri', 'locationName' => 'FunctionName', ], 'Qualifier' => [ 'shape' => 'Qualifier', 'location' => 'querystring', 'locationName' => 'Qualifier', ], ], ], 'GetProvisionedConcurrencyConfigResponse' => [ 'type' => 'structure', 'members' => [ 'RequestedProvisionedConcurrentExecutions' => [ 'shape' => 'PositiveInteger', ], 'AvailableProvisionedConcurrentExecutions' => [ 'shape' => 'NonNegativeInteger', ], 'AllocatedProvisionedConcurrentExecutions' => [ 'shape' => 'NonNegativeInteger', ], 'Status' => [ 'shape' => 'ProvisionedConcurrencyStatusEnum', ], 'StatusReason' => [ 'shape' => 'String', ], 'LastModified' => [ 'shape' => 'Timestamp', ], ], ], 'GetRuntimeManagementConfigRequest' => [ 'type' => 'structure', 'required' => [ 'FunctionName', ], 'members' => [ 'FunctionName' => [ 'shape' => 'NamespacedFunctionName', 'location' => 'uri', 'locationName' => 'FunctionName', ], 'Qualifier' => [ 'shape' => 'Qualifier', 'location' => 'querystring', 'locationName' => 'Qualifier', ], ], ], 'GetRuntimeManagementConfigResponse' => [ 'type' => 'structure', 'members' => [ 'UpdateRuntimeOn' => [ 'shape' => 'UpdateRuntimeOn', ], 'RuntimeVersionArn' => [ 'shape' => 'RuntimeVersionArn', ], 'FunctionArn' => [ 'shape' => 'NameSpacedFunctionArn', ], ], ], 'Handler' => [ 'type' => 'string', 'max' => 128, 'pattern' => '[^\\s]+', ], 'Header' => [ 'type' => 'string', 'max' => 1024, 'pattern' => '.*', ], 'HeadersList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Header', ], 'max' => 100, ], 'HttpStatus' => [ 'type' => 'integer', ], 'ImageConfig' => [ 'type' => 'structure', 'members' => [ 'EntryPoint' => [ 'shape' => 'StringList', ], 'Command' => [ 'shape' => 'StringList', ], 'WorkingDirectory' => [ 'shape' => 'WorkingDirectory', ], ], ], 'ImageConfigError' => [ 'type' => 'structure', 'members' => [ 'ErrorCode' => [ 'shape' => 'String', ], 'Message' => [ 'shape' => 'SensitiveString', ], ], ], 'ImageConfigResponse' => [ 'type' => 'structure', 'members' => [ 'ImageConfig' => [ 'shape' => 'ImageConfig', ], 'Error' => [ 'shape' => 'ImageConfigError', ], ], ], 'Integer' => [ 'type' => 'integer', ], 'InvalidCodeSignatureException' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'String', ], 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'InvalidParameterValueException' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'String', ], 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'InvalidRequestContentException' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'String', ], 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'InvalidRuntimeException' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'String', ], 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 502, ], 'exception' => true, ], 'InvalidSecurityGroupIDException' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'String', ], 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 502, ], 'exception' => true, ], 'InvalidSubnetIDException' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'String', ], 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 502, ], 'exception' => true, ], 'InvalidZipFileException' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'String', ], 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 502, ], 'exception' => true, ], 'InvocationRequest' => [ 'type' => 'structure', 'required' => [ 'FunctionName', ], 'members' => [ 'FunctionName' => [ 'shape' => 'NamespacedFunctionName', 'location' => 'uri', 'locationName' => 'FunctionName', ], 'InvocationType' => [ 'shape' => 'InvocationType', 'location' => 'header', 'locationName' => 'X-Amz-Invocation-Type', ], 'LogType' => [ 'shape' => 'LogType', 'location' => 'header', 'locationName' => 'X-Amz-Log-Type', ], 'ClientContext' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'X-Amz-Client-Context', ], 'Payload' => [ 'shape' => 'Blob', ], 'Qualifier' => [ 'shape' => 'Qualifier', 'location' => 'querystring', 'locationName' => 'Qualifier', ], ], 'payload' => 'Payload', ], 'InvocationResponse' => [ 'type' => 'structure', 'members' => [ 'StatusCode' => [ 'shape' => 'Integer', 'location' => 'statusCode', ], 'FunctionError' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'X-Amz-Function-Error', ], 'LogResult' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'X-Amz-Log-Result', ], 'Payload' => [ 'shape' => 'Blob', ], 'ExecutedVersion' => [ 'shape' => 'Version', 'location' => 'header', 'locationName' => 'X-Amz-Executed-Version', ], ], 'payload' => 'Payload', ], 'InvocationType' => [ 'type' => 'string', 'enum' => [ 'Event', 'RequestResponse', 'DryRun', ], ], 'InvokeAsyncRequest' => [ 'type' => 'structure', 'required' => [ 'FunctionName', 'InvokeArgs', ], 'members' => [ 'FunctionName' => [ 'shape' => 'NamespacedFunctionName', 'location' => 'uri', 'locationName' => 'FunctionName', ], 'InvokeArgs' => [ 'shape' => 'BlobStream', ], ], 'deprecated' => true, 'payload' => 'InvokeArgs', ], 'InvokeAsyncResponse' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'HttpStatus', 'location' => 'statusCode', ], ], 'deprecated' => true, ], 'InvokeMode' => [ 'type' => 'string', 'enum' => [ 'BUFFERED', 'RESPONSE_STREAM', ], ], 'InvokeResponseStreamUpdate' => [ 'type' => 'structure', 'members' => [ 'Payload' => [ 'shape' => 'Blob', 'eventpayload' => true, ], ], 'event' => true, ], 'InvokeWithResponseStreamCompleteEvent' => [ 'type' => 'structure', 'members' => [ 'ErrorCode' => [ 'shape' => 'String', ], 'ErrorDetails' => [ 'shape' => 'String', ], 'LogResult' => [ 'shape' => 'String', ], ], 'event' => true, ], 'InvokeWithResponseStreamRequest' => [ 'type' => 'structure', 'required' => [ 'FunctionName', ], 'members' => [ 'FunctionName' => [ 'shape' => 'NamespacedFunctionName', 'location' => 'uri', 'locationName' => 'FunctionName', ], 'InvocationType' => [ 'shape' => 'ResponseStreamingInvocationType', 'location' => 'header', 'locationName' => 'X-Amz-Invocation-Type', ], 'LogType' => [ 'shape' => 'LogType', 'location' => 'header', 'locationName' => 'X-Amz-Log-Type', ], 'ClientContext' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'X-Amz-Client-Context', ], 'Qualifier' => [ 'shape' => 'Qualifier', 'location' => 'querystring', 'locationName' => 'Qualifier', ], 'Payload' => [ 'shape' => 'Blob', ], ], 'payload' => 'Payload', ], 'InvokeWithResponseStreamResponse' => [ 'type' => 'structure', 'members' => [ 'StatusCode' => [ 'shape' => 'Integer', 'location' => 'statusCode', ], 'ExecutedVersion' => [ 'shape' => 'Version', 'location' => 'header', 'locationName' => 'X-Amz-Executed-Version', ], 'EventStream' => [ 'shape' => 'InvokeWithResponseStreamResponseEvent', ], 'ResponseStreamContentType' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'Content-Type', ], ], 'payload' => 'EventStream', ], 'InvokeWithResponseStreamResponseEvent' => [ 'type' => 'structure', 'members' => [ 'PayloadChunk' => [ 'shape' => 'InvokeResponseStreamUpdate', ], 'InvokeComplete' => [ 'shape' => 'InvokeWithResponseStreamCompleteEvent', ], ], 'eventstream' => true, ], 'KMSAccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'String', ], 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 502, ], 'exception' => true, ], 'KMSDisabledException' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'String', ], 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 502, ], 'exception' => true, ], 'KMSInvalidStateException' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'String', ], 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 502, ], 'exception' => true, ], 'KMSKeyArn' => [ 'type' => 'string', 'pattern' => '(arn:(aws[a-zA-Z-]*)?:[a-z0-9-.]+:.*)|()', ], 'KMSNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'String', ], 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 502, ], 'exception' => true, ], 'LastUpdateStatus' => [ 'type' => 'string', 'enum' => [ 'Successful', 'Failed', 'InProgress', ], ], 'LastUpdateStatusReason' => [ 'type' => 'string', ], 'LastUpdateStatusReasonCode' => [ 'type' => 'string', 'enum' => [ 'EniLimitExceeded', 'InsufficientRolePermissions', 'InvalidConfiguration', 'InternalError', 'SubnetOutOfIPAddresses', 'InvalidSubnet', 'InvalidSecurityGroup', 'ImageDeleted', 'ImageAccessDenied', 'InvalidImage', 'KMSKeyAccessDenied', 'KMSKeyNotFound', 'InvalidStateKMSKey', 'DisabledKMSKey', 'EFSIOError', 'EFSMountConnectivityError', 'EFSMountFailure', 'EFSMountTimeout', 'InvalidRuntime', 'InvalidZipFileException', 'FunctionError', ], ], 'Layer' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'LayerVersionArn', ], 'CodeSize' => [ 'shape' => 'Long', ], 'SigningProfileVersionArn' => [ 'shape' => 'Arn', ], 'SigningJobArn' => [ 'shape' => 'Arn', ], ], ], 'LayerArn' => [ 'type' => 'string', 'max' => 140, 'min' => 1, 'pattern' => 'arn:[a-zA-Z0-9-]+:lambda:[a-zA-Z0-9-]+:\\d{12}:layer:[a-zA-Z0-9-_]+', ], 'LayerList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LayerVersionArn', ], ], 'LayerName' => [ 'type' => 'string', 'max' => 140, 'min' => 1, 'pattern' => '(arn:[a-zA-Z0-9-]+:lambda:[a-zA-Z0-9-]+:\\d{12}:layer:[a-zA-Z0-9-_]+)|[a-zA-Z0-9-_]+', ], 'LayerPermissionAllowedAction' => [ 'type' => 'string', 'max' => 22, 'pattern' => 'lambda:GetLayerVersion', ], 'LayerPermissionAllowedPrincipal' => [ 'type' => 'string', 'pattern' => '\\d{12}|\\*|arn:(aws[a-zA-Z-]*):iam::\\d{12}:root', ], 'LayerVersionArn' => [ 'type' => 'string', 'max' => 140, 'min' => 1, 'pattern' => 'arn:[a-zA-Z0-9-]+:lambda:[a-zA-Z0-9-]+:\\d{12}:layer:[a-zA-Z0-9-_]+:[0-9]+', ], 'LayerVersionContentInput' => [ 'type' => 'structure', 'members' => [ 'S3Bucket' => [ 'shape' => 'S3Bucket', ], 'S3Key' => [ 'shape' => 'S3Key', ], 'S3ObjectVersion' => [ 'shape' => 'S3ObjectVersion', ], 'ZipFile' => [ 'shape' => 'Blob', ], ], ], 'LayerVersionContentOutput' => [ 'type' => 'structure', 'members' => [ 'Location' => [ 'shape' => 'String', ], 'CodeSha256' => [ 'shape' => 'String', ], 'CodeSize' => [ 'shape' => 'Long', ], 'SigningProfileVersionArn' => [ 'shape' => 'String', ], 'SigningJobArn' => [ 'shape' => 'String', ], ], ], 'LayerVersionNumber' => [ 'type' => 'long', ], 'LayerVersionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LayerVersionsListItem', ], ], 'LayerVersionsListItem' => [ 'type' => 'structure', 'members' => [ 'LayerVersionArn' => [ 'shape' => 'LayerVersionArn', ], 'Version' => [ 'shape' => 'LayerVersionNumber', ], 'Description' => [ 'shape' => 'Description', ], 'CreatedDate' => [ 'shape' => 'Timestamp', ], 'CompatibleRuntimes' => [ 'shape' => 'CompatibleRuntimes', ], 'LicenseInfo' => [ 'shape' => 'LicenseInfo', ], 'CompatibleArchitectures' => [ 'shape' => 'CompatibleArchitectures', ], ], ], 'LayersList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LayersListItem', ], ], 'LayersListItem' => [ 'type' => 'structure', 'members' => [ 'LayerName' => [ 'shape' => 'LayerName', ], 'LayerArn' => [ 'shape' => 'LayerArn', ], 'LatestMatchingVersion' => [ 'shape' => 'LayerVersionsListItem', ], ], ], 'LayersReferenceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Layer', ], ], 'LicenseInfo' => [ 'type' => 'string', 'max' => 512, ], 'ListAliasesRequest' => [ 'type' => 'structure', 'required' => [ 'FunctionName', ], 'members' => [ 'FunctionName' => [ 'shape' => 'FunctionName', 'location' => 'uri', 'locationName' => 'FunctionName', ], 'FunctionVersion' => [ 'shape' => 'Version', 'location' => 'querystring', 'locationName' => 'FunctionVersion', ], 'Marker' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'Marker', ], 'MaxItems' => [ 'shape' => 'MaxListItems', 'location' => 'querystring', 'locationName' => 'MaxItems', ], ], ], 'ListAliasesResponse' => [ 'type' => 'structure', 'members' => [ 'NextMarker' => [ 'shape' => 'String', ], 'Aliases' => [ 'shape' => 'AliasList', ], ], ], 'ListCodeSigningConfigsRequest' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'Marker', ], 'MaxItems' => [ 'shape' => 'MaxListItems', 'location' => 'querystring', 'locationName' => 'MaxItems', ], ], ], 'ListCodeSigningConfigsResponse' => [ 'type' => 'structure', 'members' => [ 'NextMarker' => [ 'shape' => 'String', ], 'CodeSigningConfigs' => [ 'shape' => 'CodeSigningConfigList', ], ], ], 'ListEventSourceMappingsRequest' => [ 'type' => 'structure', 'members' => [ 'EventSourceArn' => [ 'shape' => 'Arn', 'location' => 'querystring', 'locationName' => 'EventSourceArn', ], 'FunctionName' => [ 'shape' => 'FunctionName', 'location' => 'querystring', 'locationName' => 'FunctionName', ], 'Marker' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'Marker', ], 'MaxItems' => [ 'shape' => 'MaxListItems', 'location' => 'querystring', 'locationName' => 'MaxItems', ], ], ], 'ListEventSourceMappingsResponse' => [ 'type' => 'structure', 'members' => [ 'NextMarker' => [ 'shape' => 'String', ], 'EventSourceMappings' => [ 'shape' => 'EventSourceMappingsList', ], ], ], 'ListFunctionEventInvokeConfigsRequest' => [ 'type' => 'structure', 'required' => [ 'FunctionName', ], 'members' => [ 'FunctionName' => [ 'shape' => 'FunctionName', 'location' => 'uri', 'locationName' => 'FunctionName', ], 'Marker' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'Marker', ], 'MaxItems' => [ 'shape' => 'MaxFunctionEventInvokeConfigListItems', 'location' => 'querystring', 'locationName' => 'MaxItems', ], ], ], 'ListFunctionEventInvokeConfigsResponse' => [ 'type' => 'structure', 'members' => [ 'FunctionEventInvokeConfigs' => [ 'shape' => 'FunctionEventInvokeConfigList', ], 'NextMarker' => [ 'shape' => 'String', ], ], ], 'ListFunctionUrlConfigsRequest' => [ 'type' => 'structure', 'required' => [ 'FunctionName', ], 'members' => [ 'FunctionName' => [ 'shape' => 'FunctionName', 'location' => 'uri', 'locationName' => 'FunctionName', ], 'Marker' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'Marker', ], 'MaxItems' => [ 'shape' => 'MaxItems', 'location' => 'querystring', 'locationName' => 'MaxItems', ], ], ], 'ListFunctionUrlConfigsResponse' => [ 'type' => 'structure', 'required' => [ 'FunctionUrlConfigs', ], 'members' => [ 'FunctionUrlConfigs' => [ 'shape' => 'FunctionUrlConfigList', ], 'NextMarker' => [ 'shape' => 'String', ], ], ], 'ListFunctionsByCodeSigningConfigRequest' => [ 'type' => 'structure', 'required' => [ 'CodeSigningConfigArn', ], 'members' => [ 'CodeSigningConfigArn' => [ 'shape' => 'CodeSigningConfigArn', 'location' => 'uri', 'locationName' => 'CodeSigningConfigArn', ], 'Marker' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'Marker', ], 'MaxItems' => [ 'shape' => 'MaxListItems', 'location' => 'querystring', 'locationName' => 'MaxItems', ], ], ], 'ListFunctionsByCodeSigningConfigResponse' => [ 'type' => 'structure', 'members' => [ 'NextMarker' => [ 'shape' => 'String', ], 'FunctionArns' => [ 'shape' => 'FunctionArnList', ], ], ], 'ListFunctionsRequest' => [ 'type' => 'structure', 'members' => [ 'MasterRegion' => [ 'shape' => 'MasterRegion', 'location' => 'querystring', 'locationName' => 'MasterRegion', ], 'FunctionVersion' => [ 'shape' => 'FunctionVersion', 'location' => 'querystring', 'locationName' => 'FunctionVersion', ], 'Marker' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'Marker', ], 'MaxItems' => [ 'shape' => 'MaxListItems', 'location' => 'querystring', 'locationName' => 'MaxItems', ], ], ], 'ListFunctionsResponse' => [ 'type' => 'structure', 'members' => [ 'NextMarker' => [ 'shape' => 'String', ], 'Functions' => [ 'shape' => 'FunctionList', ], ], ], 'ListLayerVersionsRequest' => [ 'type' => 'structure', 'required' => [ 'LayerName', ], 'members' => [ 'CompatibleRuntime' => [ 'shape' => 'Runtime', 'location' => 'querystring', 'locationName' => 'CompatibleRuntime', ], 'LayerName' => [ 'shape' => 'LayerName', 'location' => 'uri', 'locationName' => 'LayerName', ], 'Marker' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'Marker', ], 'MaxItems' => [ 'shape' => 'MaxLayerListItems', 'location' => 'querystring', 'locationName' => 'MaxItems', ], 'CompatibleArchitecture' => [ 'shape' => 'Architecture', 'location' => 'querystring', 'locationName' => 'CompatibleArchitecture', ], ], ], 'ListLayerVersionsResponse' => [ 'type' => 'structure', 'members' => [ 'NextMarker' => [ 'shape' => 'String', ], 'LayerVersions' => [ 'shape' => 'LayerVersionsList', ], ], ], 'ListLayersRequest' => [ 'type' => 'structure', 'members' => [ 'CompatibleRuntime' => [ 'shape' => 'Runtime', 'location' => 'querystring', 'locationName' => 'CompatibleRuntime', ], 'Marker' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'Marker', ], 'MaxItems' => [ 'shape' => 'MaxLayerListItems', 'location' => 'querystring', 'locationName' => 'MaxItems', ], 'CompatibleArchitecture' => [ 'shape' => 'Architecture', 'location' => 'querystring', 'locationName' => 'CompatibleArchitecture', ], ], ], 'ListLayersResponse' => [ 'type' => 'structure', 'members' => [ 'NextMarker' => [ 'shape' => 'String', ], 'Layers' => [ 'shape' => 'LayersList', ], ], ], 'ListProvisionedConcurrencyConfigsRequest' => [ 'type' => 'structure', 'required' => [ 'FunctionName', ], 'members' => [ 'FunctionName' => [ 'shape' => 'FunctionName', 'location' => 'uri', 'locationName' => 'FunctionName', ], 'Marker' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'Marker', ], 'MaxItems' => [ 'shape' => 'MaxProvisionedConcurrencyConfigListItems', 'location' => 'querystring', 'locationName' => 'MaxItems', ], ], ], 'ListProvisionedConcurrencyConfigsResponse' => [ 'type' => 'structure', 'members' => [ 'ProvisionedConcurrencyConfigs' => [ 'shape' => 'ProvisionedConcurrencyConfigList', ], 'NextMarker' => [ 'shape' => 'String', ], ], ], 'ListTagsRequest' => [ 'type' => 'structure', 'required' => [ 'Resource', ], 'members' => [ 'Resource' => [ 'shape' => 'TaggableResource', 'location' => 'uri', 'locationName' => 'ARN', ], ], ], 'ListTagsResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'Tags', ], ], ], 'ListVersionsByFunctionRequest' => [ 'type' => 'structure', 'required' => [ 'FunctionName', ], 'members' => [ 'FunctionName' => [ 'shape' => 'NamespacedFunctionName', 'location' => 'uri', 'locationName' => 'FunctionName', ], 'Marker' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'Marker', ], 'MaxItems' => [ 'shape' => 'MaxListItems', 'location' => 'querystring', 'locationName' => 'MaxItems', ], ], ], 'ListVersionsByFunctionResponse' => [ 'type' => 'structure', 'members' => [ 'NextMarker' => [ 'shape' => 'String', ], 'Versions' => [ 'shape' => 'FunctionList', ], ], ], 'LocalMountPath' => [ 'type' => 'string', 'max' => 160, 'pattern' => '^/mnt/[a-zA-Z0-9-_.]+$', ], 'LogFormat' => [ 'type' => 'string', 'enum' => [ 'JSON', 'Text', ], ], 'LogGroup' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => '[\\.\\-_/#A-Za-z0-9]+', ], 'LogType' => [ 'type' => 'string', 'enum' => [ 'None', 'Tail', ], ], 'LoggingConfig' => [ 'type' => 'structure', 'members' => [ 'LogFormat' => [ 'shape' => 'LogFormat', ], 'ApplicationLogLevel' => [ 'shape' => 'ApplicationLogLevel', ], 'SystemLogLevel' => [ 'shape' => 'SystemLogLevel', ], 'LogGroup' => [ 'shape' => 'LogGroup', ], ], ], 'Long' => [ 'type' => 'long', ], 'MasterRegion' => [ 'type' => 'string', 'pattern' => 'ALL|[a-z]{2}(-gov)?-[a-z]+-\\d{1}', ], 'MaxAge' => [ 'type' => 'integer', 'max' => 86400, 'min' => 0, ], 'MaxFunctionEventInvokeConfigListItems' => [ 'type' => 'integer', 'max' => 50, 'min' => 1, ], 'MaxItems' => [ 'type' => 'integer', 'max' => 50, 'min' => 1, ], 'MaxLayerListItems' => [ 'type' => 'integer', 'max' => 50, 'min' => 1, ], 'MaxListItems' => [ 'type' => 'integer', 'max' => 10000, 'min' => 1, ], 'MaxProvisionedConcurrencyConfigListItems' => [ 'type' => 'integer', 'max' => 50, 'min' => 1, ], 'MaximumBatchingWindowInSeconds' => [ 'type' => 'integer', 'max' => 300, 'min' => 0, ], 'MaximumConcurrency' => [ 'type' => 'integer', 'max' => 1000, 'min' => 2, ], 'MaximumEventAgeInSeconds' => [ 'type' => 'integer', 'max' => 21600, 'min' => 60, ], 'MaximumNumberOfPollers' => [ 'type' => 'integer', 'max' => 2000, 'min' => 1, ], 'MaximumRecordAgeInSeconds' => [ 'type' => 'integer', 'max' => 604800, 'min' => -1, ], 'MaximumRetryAttempts' => [ 'type' => 'integer', 'max' => 2, 'min' => 0, ], 'MaximumRetryAttemptsEventSourceMapping' => [ 'type' => 'integer', 'max' => 10000, 'min' => -1, ], 'MemorySize' => [ 'type' => 'integer', 'max' => 10240, 'min' => 128, ], 'Method' => [ 'type' => 'string', 'max' => 6, 'pattern' => '.*', ], 'MinimumNumberOfPollers' => [ 'type' => 'integer', 'max' => 200, 'min' => 1, ], 'NameSpacedFunctionArn' => [ 'type' => 'string', 'pattern' => 'arn:(aws[a-zA-Z-]*)?:lambda:[a-z]{2}(-gov)?-[a-z]+-\\d{1}:\\d{12}:function:[a-zA-Z0-9-_\\.]+(:(\\$LATEST|[a-zA-Z0-9-_]+))?', ], 'NamespacedFunctionName' => [ 'type' => 'string', 'max' => 170, 'min' => 1, 'pattern' => '(arn:(aws[a-zA-Z-]*)?:lambda:)?([a-z]{2}(-gov)?-[a-z]+-\\d{1}:)?(\\d{12}:)?(function:)?([a-zA-Z0-9-_\\.]+)(:(\\$LATEST|[a-zA-Z0-9-_]+))?', ], 'NamespacedStatementId' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '([a-zA-Z0-9-_.]+)', ], 'NonNegativeInteger' => [ 'type' => 'integer', 'min' => 0, ], 'NullableBoolean' => [ 'type' => 'boolean', ], 'OnFailure' => [ 'type' => 'structure', 'members' => [ 'Destination' => [ 'shape' => 'DestinationArn', ], ], ], 'OnSuccess' => [ 'type' => 'structure', 'members' => [ 'Destination' => [ 'shape' => 'DestinationArn', ], ], ], 'OrganizationId' => [ 'type' => 'string', 'max' => 34, 'pattern' => 'o-[a-z0-9]{10,32}', ], 'Origin' => [ 'type' => 'string', 'max' => 253, 'min' => 1, 'pattern' => '.*', ], 'PackageType' => [ 'type' => 'string', 'enum' => [ 'Zip', 'Image', ], ], 'ParallelizationFactor' => [ 'type' => 'integer', 'max' => 10, 'min' => 1, ], 'Pattern' => [ 'type' => 'string', 'max' => 4096, 'min' => 0, 'pattern' => '.*', ], 'PolicyLengthExceededException' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'String', ], 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'PositiveInteger' => [ 'type' => 'integer', 'min' => 1, ], 'PreconditionFailedException' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'String', ], 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 412, ], 'exception' => true, ], 'Principal' => [ 'type' => 'string', 'pattern' => '[^\\s]+', ], 'PrincipalOrgID' => [ 'type' => 'string', 'max' => 34, 'min' => 12, 'pattern' => '^o-[a-z0-9]{10,32}$', ], 'ProvisionedConcurrencyConfigList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProvisionedConcurrencyConfigListItem', ], ], 'ProvisionedConcurrencyConfigListItem' => [ 'type' => 'structure', 'members' => [ 'FunctionArn' => [ 'shape' => 'FunctionArn', ], 'RequestedProvisionedConcurrentExecutions' => [ 'shape' => 'PositiveInteger', ], 'AvailableProvisionedConcurrentExecutions' => [ 'shape' => 'NonNegativeInteger', ], 'AllocatedProvisionedConcurrentExecutions' => [ 'shape' => 'NonNegativeInteger', ], 'Status' => [ 'shape' => 'ProvisionedConcurrencyStatusEnum', ], 'StatusReason' => [ 'shape' => 'String', ], 'LastModified' => [ 'shape' => 'Timestamp', ], ], ], 'ProvisionedConcurrencyConfigNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'String', ], 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'ProvisionedConcurrencyStatusEnum' => [ 'type' => 'string', 'enum' => [ 'IN_PROGRESS', 'READY', 'FAILED', ], ], 'ProvisionedPollerConfig' => [ 'type' => 'structure', 'members' => [ 'MinimumPollers' => [ 'shape' => 'MinimumNumberOfPollers', ], 'MaximumPollers' => [ 'shape' => 'MaximumNumberOfPollers', ], ], ], 'PublishLayerVersionRequest' => [ 'type' => 'structure', 'required' => [ 'LayerName', 'Content', ], 'members' => [ 'LayerName' => [ 'shape' => 'LayerName', 'location' => 'uri', 'locationName' => 'LayerName', ], 'Description' => [ 'shape' => 'Description', ], 'Content' => [ 'shape' => 'LayerVersionContentInput', ], 'CompatibleRuntimes' => [ 'shape' => 'CompatibleRuntimes', ], 'LicenseInfo' => [ 'shape' => 'LicenseInfo', ], 'CompatibleArchitectures' => [ 'shape' => 'CompatibleArchitectures', ], ], ], 'PublishLayerVersionResponse' => [ 'type' => 'structure', 'members' => [ 'Content' => [ 'shape' => 'LayerVersionContentOutput', ], 'LayerArn' => [ 'shape' => 'LayerArn', ], 'LayerVersionArn' => [ 'shape' => 'LayerVersionArn', ], 'Description' => [ 'shape' => 'Description', ], 'CreatedDate' => [ 'shape' => 'Timestamp', ], 'Version' => [ 'shape' => 'LayerVersionNumber', ], 'CompatibleRuntimes' => [ 'shape' => 'CompatibleRuntimes', ], 'LicenseInfo' => [ 'shape' => 'LicenseInfo', ], 'CompatibleArchitectures' => [ 'shape' => 'CompatibleArchitectures', ], ], ], 'PublishVersionRequest' => [ 'type' => 'structure', 'required' => [ 'FunctionName', ], 'members' => [ 'FunctionName' => [ 'shape' => 'FunctionName', 'location' => 'uri', 'locationName' => 'FunctionName', ], 'CodeSha256' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'Description', ], 'RevisionId' => [ 'shape' => 'String', ], ], ], 'PutFunctionCodeSigningConfigRequest' => [ 'type' => 'structure', 'required' => [ 'CodeSigningConfigArn', 'FunctionName', ], 'members' => [ 'CodeSigningConfigArn' => [ 'shape' => 'CodeSigningConfigArn', ], 'FunctionName' => [ 'shape' => 'FunctionName', 'location' => 'uri', 'locationName' => 'FunctionName', ], ], ], 'PutFunctionCodeSigningConfigResponse' => [ 'type' => 'structure', 'required' => [ 'CodeSigningConfigArn', 'FunctionName', ], 'members' => [ 'CodeSigningConfigArn' => [ 'shape' => 'CodeSigningConfigArn', ], 'FunctionName' => [ 'shape' => 'FunctionName', ], ], ], 'PutFunctionConcurrencyRequest' => [ 'type' => 'structure', 'required' => [ 'FunctionName', 'ReservedConcurrentExecutions', ], 'members' => [ 'FunctionName' => [ 'shape' => 'FunctionName', 'location' => 'uri', 'locationName' => 'FunctionName', ], 'ReservedConcurrentExecutions' => [ 'shape' => 'ReservedConcurrentExecutions', ], ], ], 'PutFunctionEventInvokeConfigRequest' => [ 'type' => 'structure', 'required' => [ 'FunctionName', ], 'members' => [ 'FunctionName' => [ 'shape' => 'FunctionName', 'location' => 'uri', 'locationName' => 'FunctionName', ], 'Qualifier' => [ 'shape' => 'Qualifier', 'location' => 'querystring', 'locationName' => 'Qualifier', ], 'MaximumRetryAttempts' => [ 'shape' => 'MaximumRetryAttempts', ], 'MaximumEventAgeInSeconds' => [ 'shape' => 'MaximumEventAgeInSeconds', ], 'DestinationConfig' => [ 'shape' => 'DestinationConfig', ], ], ], 'PutFunctionRecursionConfigRequest' => [ 'type' => 'structure', 'required' => [ 'FunctionName', 'RecursiveLoop', ], 'members' => [ 'FunctionName' => [ 'shape' => 'UnqualifiedFunctionName', 'location' => 'uri', 'locationName' => 'FunctionName', ], 'RecursiveLoop' => [ 'shape' => 'RecursiveLoop', ], ], ], 'PutFunctionRecursionConfigResponse' => [ 'type' => 'structure', 'members' => [ 'RecursiveLoop' => [ 'shape' => 'RecursiveLoop', ], ], ], 'PutProvisionedConcurrencyConfigRequest' => [ 'type' => 'structure', 'required' => [ 'FunctionName', 'Qualifier', 'ProvisionedConcurrentExecutions', ], 'members' => [ 'FunctionName' => [ 'shape' => 'FunctionName', 'location' => 'uri', 'locationName' => 'FunctionName', ], 'Qualifier' => [ 'shape' => 'Qualifier', 'location' => 'querystring', 'locationName' => 'Qualifier', ], 'ProvisionedConcurrentExecutions' => [ 'shape' => 'PositiveInteger', ], ], ], 'PutProvisionedConcurrencyConfigResponse' => [ 'type' => 'structure', 'members' => [ 'RequestedProvisionedConcurrentExecutions' => [ 'shape' => 'PositiveInteger', ], 'AvailableProvisionedConcurrentExecutions' => [ 'shape' => 'NonNegativeInteger', ], 'AllocatedProvisionedConcurrentExecutions' => [ 'shape' => 'NonNegativeInteger', ], 'Status' => [ 'shape' => 'ProvisionedConcurrencyStatusEnum', ], 'StatusReason' => [ 'shape' => 'String', ], 'LastModified' => [ 'shape' => 'Timestamp', ], ], ], 'PutRuntimeManagementConfigRequest' => [ 'type' => 'structure', 'required' => [ 'FunctionName', 'UpdateRuntimeOn', ], 'members' => [ 'FunctionName' => [ 'shape' => 'FunctionName', 'location' => 'uri', 'locationName' => 'FunctionName', ], 'Qualifier' => [ 'shape' => 'Qualifier', 'location' => 'querystring', 'locationName' => 'Qualifier', ], 'UpdateRuntimeOn' => [ 'shape' => 'UpdateRuntimeOn', ], 'RuntimeVersionArn' => [ 'shape' => 'RuntimeVersionArn', ], ], ], 'PutRuntimeManagementConfigResponse' => [ 'type' => 'structure', 'required' => [ 'UpdateRuntimeOn', 'FunctionArn', ], 'members' => [ 'UpdateRuntimeOn' => [ 'shape' => 'UpdateRuntimeOn', ], 'FunctionArn' => [ 'shape' => 'FunctionArn', ], 'RuntimeVersionArn' => [ 'shape' => 'RuntimeVersionArn', ], ], ], 'Qualifier' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '(|[a-zA-Z0-9$_-]+)', ], 'Queue' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, 'pattern' => '[\\s\\S]*', ], 'Queues' => [ 'type' => 'list', 'member' => [ 'shape' => 'Queue', ], 'max' => 1, 'min' => 1, ], 'RecursiveInvocationException' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'String', ], 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'RecursiveLoop' => [ 'type' => 'string', 'enum' => [ 'Allow', 'Terminate', ], ], 'RemoveLayerVersionPermissionRequest' => [ 'type' => 'structure', 'required' => [ 'LayerName', 'VersionNumber', 'StatementId', ], 'members' => [ 'LayerName' => [ 'shape' => 'LayerName', 'location' => 'uri', 'locationName' => 'LayerName', ], 'VersionNumber' => [ 'shape' => 'LayerVersionNumber', 'location' => 'uri', 'locationName' => 'VersionNumber', ], 'StatementId' => [ 'shape' => 'StatementId', 'location' => 'uri', 'locationName' => 'StatementId', ], 'RevisionId' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'RevisionId', ], ], ], 'RemovePermissionRequest' => [ 'type' => 'structure', 'required' => [ 'FunctionName', 'StatementId', ], 'members' => [ 'FunctionName' => [ 'shape' => 'FunctionName', 'location' => 'uri', 'locationName' => 'FunctionName', ], 'StatementId' => [ 'shape' => 'NamespacedStatementId', 'location' => 'uri', 'locationName' => 'StatementId', ], 'Qualifier' => [ 'shape' => 'Qualifier', 'location' => 'querystring', 'locationName' => 'Qualifier', ], 'RevisionId' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'RevisionId', ], ], ], 'RequestTooLargeException' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'String', ], 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 413, ], 'exception' => true, ], 'ReservedConcurrentExecutions' => [ 'type' => 'integer', 'min' => 0, ], 'ResourceArn' => [ 'type' => 'string', 'pattern' => '(arn:(aws[a-zA-Z-]*)?:[a-z0-9-.]+:.*)|()', ], 'ResourceConflictException' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'String', ], 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'ResourceInUseException' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'String', ], 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'String', ], 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'ResourceNotReadyException' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'String', ], 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 502, ], 'exception' => true, ], 'ResponseStreamingInvocationType' => [ 'type' => 'string', 'enum' => [ 'RequestResponse', 'DryRun', ], ], 'RoleArn' => [ 'type' => 'string', 'pattern' => 'arn:(aws[a-zA-Z-]*)?:iam::\\d{12}:role/?[a-zA-Z_0-9+=,.@\\-_/]+', ], 'Runtime' => [ 'type' => 'string', 'enum' => [ 'nodejs', 'nodejs4.3', 'nodejs6.10', 'nodejs8.10', 'nodejs10.x', 'nodejs12.x', 'nodejs14.x', 'nodejs16.x', 'java8', 'java8.al2', 'java11', 'python2.7', 'python3.6', 'python3.7', 'python3.8', 'python3.9', 'dotnetcore1.0', 'dotnetcore2.0', 'dotnetcore2.1', 'dotnetcore3.1', 'dotnet6', 'dotnet8', 'nodejs4.3-edge', 'go1.x', 'ruby2.5', 'ruby2.7', 'provided', 'provided.al2', 'nodejs18.x', 'python3.10', 'java17', 'ruby3.2', 'ruby3.3', 'ruby3.4', 'python3.11', 'nodejs20.x', 'provided.al2023', 'python3.12', 'java21', 'python3.13', 'nodejs22.x', ], ], 'RuntimeVersionArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 26, 'pattern' => '^arn:(aws[a-zA-Z-]*):lambda:[a-z]{2}((-gov)|(-iso(b?)))?-[a-z]+-\\d{1}::runtime:.+$', ], 'RuntimeVersionConfig' => [ 'type' => 'structure', 'members' => [ 'RuntimeVersionArn' => [ 'shape' => 'RuntimeVersionArn', ], 'Error' => [ 'shape' => 'RuntimeVersionError', ], ], ], 'RuntimeVersionError' => [ 'type' => 'structure', 'members' => [ 'ErrorCode' => [ 'shape' => 'String', ], 'Message' => [ 'shape' => 'SensitiveString', ], ], ], 'S3Bucket' => [ 'type' => 'string', 'max' => 63, 'min' => 3, 'pattern' => '^[0-9A-Za-z\\.\\-_]*(?<!\\.)$', ], 'S3Key' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'S3ObjectVersion' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'ScalingConfig' => [ 'type' => 'structure', 'members' => [ 'MaximumConcurrency' => [ 'shape' => 'MaximumConcurrency', ], ], ], 'SecurityGroupId' => [ 'type' => 'string', ], 'SecurityGroupIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecurityGroupId', ], 'max' => 5, ], 'SelfManagedEventSource' => [ 'type' => 'structure', 'members' => [ 'Endpoints' => [ 'shape' => 'Endpoints', ], ], ], 'SelfManagedKafkaEventSourceConfig' => [ 'type' => 'structure', 'members' => [ 'ConsumerGroupId' => [ 'shape' => 'URI', ], ], ], 'SensitiveString' => [ 'type' => 'string', 'sensitive' => true, ], 'ServiceException' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'String', ], 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, ], 'SigningProfileVersionArns' => [ 'type' => 'list', 'member' => [ 'shape' => 'Arn', ], 'max' => 20, 'min' => 1, ], 'SnapStart' => [ 'type' => 'structure', 'members' => [ 'ApplyOn' => [ 'shape' => 'SnapStartApplyOn', ], ], ], 'SnapStartApplyOn' => [ 'type' => 'string', 'enum' => [ 'PublishedVersions', 'None', ], ], 'SnapStartException' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'String', ], 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'SnapStartNotReadyException' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'String', ], 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'SnapStartOptimizationStatus' => [ 'type' => 'string', 'enum' => [ 'On', 'Off', ], ], 'SnapStartResponse' => [ 'type' => 'structure', 'members' => [ 'ApplyOn' => [ 'shape' => 'SnapStartApplyOn', ], 'OptimizationStatus' => [ 'shape' => 'SnapStartOptimizationStatus', ], ], ], 'SnapStartTimeoutException' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'String', ], 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 408, ], 'exception' => true, ], 'SourceAccessConfiguration' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'SourceAccessType', ], 'URI' => [ 'shape' => 'URI', ], ], ], 'SourceAccessConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'SourceAccessConfiguration', ], 'max' => 22, 'min' => 0, ], 'SourceAccessType' => [ 'type' => 'string', 'enum' => [ 'BASIC_AUTH', 'VPC_SUBNET', 'VPC_SECURITY_GROUP', 'SASL_SCRAM_512_AUTH', 'SASL_SCRAM_256_AUTH', 'VIRTUAL_HOST', 'CLIENT_CERTIFICATE_TLS_AUTH', 'SERVER_ROOT_CA_CERTIFICATE', ], ], 'SourceOwner' => [ 'type' => 'string', 'max' => 12, 'pattern' => '\\d{12}', ], 'State' => [ 'type' => 'string', 'enum' => [ 'Pending', 'Active', 'Inactive', 'Failed', ], ], 'StateReason' => [ 'type' => 'string', ], 'StateReasonCode' => [ 'type' => 'string', 'enum' => [ 'Idle', 'Creating', 'Restoring', 'EniLimitExceeded', 'InsufficientRolePermissions', 'InvalidConfiguration', 'InternalError', 'SubnetOutOfIPAddresses', 'InvalidSubnet', 'InvalidSecurityGroup', 'ImageDeleted', 'ImageAccessDenied', 'InvalidImage', 'KMSKeyAccessDenied', 'KMSKeyNotFound', 'InvalidStateKMSKey', 'DisabledKMSKey', 'EFSIOError', 'EFSMountConnectivityError', 'EFSMountFailure', 'EFSMountTimeout', 'InvalidRuntime', 'InvalidZipFileException', 'FunctionError', ], ], 'StatementId' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '([a-zA-Z0-9-_]+)', ], 'String' => [ 'type' => 'string', ], 'StringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], 'max' => 1500, ], 'SubnetIPAddressLimitReachedException' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'String', ], 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 502, ], 'exception' => true, ], 'SubnetId' => [ 'type' => 'string', ], 'SubnetIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'SubnetId', ], 'max' => 16, ], 'SystemLogLevel' => [ 'type' => 'string', 'enum' => [ 'DEBUG', 'INFO', 'WARN', ], ], 'TagKey' => [ 'type' => 'string', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'Resource', 'Tags', ], 'members' => [ 'Resource' => [ 'shape' => 'TaggableResource', 'location' => 'uri', 'locationName' => 'ARN', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'TagValue' => [ 'type' => 'string', ], 'TaggableResource' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => 'arn:(aws[a-zA-Z-]*):lambda:[a-z]{2}((-gov)|(-iso([a-z]?)))?-[a-z]+-\\d{1}:\\d{12}:(function:[a-zA-Z0-9-_]+(:(\\$LATEST|[a-zA-Z0-9-_]+))?|code-signing-config:csc-[a-z0-9]{17}|event-source-mapping:[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12})', ], 'Tags' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], ], 'TagsError' => [ 'type' => 'structure', 'required' => [ 'ErrorCode', 'Message', ], 'members' => [ 'ErrorCode' => [ 'shape' => 'TagsErrorCode', ], 'Message' => [ 'shape' => 'TagsErrorMessage', ], ], ], 'TagsErrorCode' => [ 'type' => 'string', 'max' => 21, 'min' => 10, 'pattern' => '[A-Za-z]+Exception', ], 'TagsErrorMessage' => [ 'type' => 'string', 'max' => 1000, 'min' => 84, 'pattern' => '^.*$', ], 'ThrottleReason' => [ 'type' => 'string', 'enum' => [ 'ConcurrentInvocationLimitExceeded', 'FunctionInvocationRateLimitExceeded', 'ReservedFunctionConcurrentInvocationLimitExceeded', 'ReservedFunctionInvocationRateLimitExceeded', 'CallerRateLimitExceeded', 'ConcurrentSnapshotCreateLimitExceeded', ], ], 'Timeout' => [ 'type' => 'integer', 'min' => 1, ], 'Timestamp' => [ 'type' => 'string', ], 'TooManyRequestsException' => [ 'type' => 'structure', 'members' => [ 'retryAfterSeconds' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'Retry-After', ], 'Type' => [ 'shape' => 'String', ], 'message' => [ 'shape' => 'String', ], 'Reason' => [ 'shape' => 'ThrottleReason', ], ], 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], 'Topic' => [ 'type' => 'string', 'max' => 249, 'min' => 1, 'pattern' => '^[^.]([a-zA-Z0-9\\-_.]+)', ], 'Topics' => [ 'type' => 'list', 'member' => [ 'shape' => 'Topic', ], 'max' => 1, 'min' => 1, ], 'TracingConfig' => [ 'type' => 'structure', 'members' => [ 'Mode' => [ 'shape' => 'TracingMode', ], ], ], 'TracingConfigResponse' => [ 'type' => 'structure', 'members' => [ 'Mode' => [ 'shape' => 'TracingMode', ], ], ], 'TracingMode' => [ 'type' => 'string', 'enum' => [ 'Active', 'PassThrough', ], ], 'TumblingWindowInSeconds' => [ 'type' => 'integer', 'max' => 900, 'min' => 0, ], 'URI' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '[a-zA-Z0-9-\\/*:_+=.@-]*', ], 'UnqualifiedFunctionName' => [ 'type' => 'string', 'max' => 140, 'min' => 1, 'pattern' => '(arn:(aws[a-zA-Z-]*)?:lambda:)?([a-z]{2}((-gov)|(-iso([a-z]?)))?-[a-z]+-\\d{1}:)?(\\d{12}:)?(function:)?([a-zA-Z0-9-_]+)', ], 'UnreservedConcurrentExecutions' => [ 'type' => 'integer', 'min' => 0, ], 'UnsupportedMediaTypeException' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'String', ], 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 415, ], 'exception' => true, ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'Resource', 'TagKeys', ], 'members' => [ 'Resource' => [ 'shape' => 'TaggableResource', 'location' => 'uri', 'locationName' => 'ARN', ], 'TagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UpdateAliasRequest' => [ 'type' => 'structure', 'required' => [ 'FunctionName', 'Name', ], 'members' => [ 'FunctionName' => [ 'shape' => 'FunctionName', 'location' => 'uri', 'locationName' => 'FunctionName', ], 'Name' => [ 'shape' => 'Alias', 'location' => 'uri', 'locationName' => 'Name', ], 'FunctionVersion' => [ 'shape' => 'Version', ], 'Description' => [ 'shape' => 'Description', ], 'RoutingConfig' => [ 'shape' => 'AliasRoutingConfiguration', ], 'RevisionId' => [ 'shape' => 'String', ], ], ], 'UpdateCodeSigningConfigRequest' => [ 'type' => 'structure', 'required' => [ 'CodeSigningConfigArn', ], 'members' => [ 'CodeSigningConfigArn' => [ 'shape' => 'CodeSigningConfigArn', 'location' => 'uri', 'locationName' => 'CodeSigningConfigArn', ], 'Description' => [ 'shape' => 'Description', ], 'AllowedPublishers' => [ 'shape' => 'AllowedPublishers', ], 'CodeSigningPolicies' => [ 'shape' => 'CodeSigningPolicies', ], ], ], 'UpdateCodeSigningConfigResponse' => [ 'type' => 'structure', 'required' => [ 'CodeSigningConfig', ], 'members' => [ 'CodeSigningConfig' => [ 'shape' => 'CodeSigningConfig', ], ], ], 'UpdateEventSourceMappingRequest' => [ 'type' => 'structure', 'required' => [ 'UUID', ], 'members' => [ 'UUID' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'UUID', ], 'FunctionName' => [ 'shape' => 'FunctionName', ], 'Enabled' => [ 'shape' => 'Enabled', ], 'BatchSize' => [ 'shape' => 'BatchSize', ], 'FilterCriteria' => [ 'shape' => 'FilterCriteria', ], 'MaximumBatchingWindowInSeconds' => [ 'shape' => 'MaximumBatchingWindowInSeconds', ], 'DestinationConfig' => [ 'shape' => 'DestinationConfig', ], 'MaximumRecordAgeInSeconds' => [ 'shape' => 'MaximumRecordAgeInSeconds', ], 'BisectBatchOnFunctionError' => [ 'shape' => 'BisectBatchOnFunctionError', ], 'MaximumRetryAttempts' => [ 'shape' => 'MaximumRetryAttemptsEventSourceMapping', ], 'ParallelizationFactor' => [ 'shape' => 'ParallelizationFactor', ], 'SourceAccessConfigurations' => [ 'shape' => 'SourceAccessConfigurations', ], 'TumblingWindowInSeconds' => [ 'shape' => 'TumblingWindowInSeconds', ], 'FunctionResponseTypes' => [ 'shape' => 'FunctionResponseTypeList', ], 'ScalingConfig' => [ 'shape' => 'ScalingConfig', ], 'DocumentDBEventSourceConfig' => [ 'shape' => 'DocumentDBEventSourceConfig', ], 'KMSKeyArn' => [ 'shape' => 'KMSKeyArn', ], 'MetricsConfig' => [ 'shape' => 'EventSourceMappingMetricsConfig', ], 'ProvisionedPollerConfig' => [ 'shape' => 'ProvisionedPollerConfig', ], ], ], 'UpdateFunctionCodeRequest' => [ 'type' => 'structure', 'required' => [ 'FunctionName', ], 'members' => [ 'FunctionName' => [ 'shape' => 'FunctionName', 'location' => 'uri', 'locationName' => 'FunctionName', ], 'ZipFile' => [ 'shape' => 'Blob', ], 'S3Bucket' => [ 'shape' => 'S3Bucket', ], 'S3Key' => [ 'shape' => 'S3Key', ], 'S3ObjectVersion' => [ 'shape' => 'S3ObjectVersion', ], 'ImageUri' => [ 'shape' => 'String', ], 'Publish' => [ 'shape' => 'Boolean', ], 'DryRun' => [ 'shape' => 'Boolean', ], 'RevisionId' => [ 'shape' => 'String', ], 'Architectures' => [ 'shape' => 'ArchitecturesList', ], 'SourceKMSKeyArn' => [ 'shape' => 'KMSKeyArn', ], ], ], 'UpdateFunctionConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'FunctionName', ], 'members' => [ 'FunctionName' => [ 'shape' => 'FunctionName', 'location' => 'uri', 'locationName' => 'FunctionName', ], 'Role' => [ 'shape' => 'RoleArn', ], 'Handler' => [ 'shape' => 'Handler', ], 'Description' => [ 'shape' => 'Description', ], 'Timeout' => [ 'shape' => 'Timeout', ], 'MemorySize' => [ 'shape' => 'MemorySize', ], 'VpcConfig' => [ 'shape' => 'VpcConfig', ], 'Environment' => [ 'shape' => 'Environment', ], 'Runtime' => [ 'shape' => 'Runtime', ], 'DeadLetterConfig' => [ 'shape' => 'DeadLetterConfig', ], 'KMSKeyArn' => [ 'shape' => 'KMSKeyArn', ], 'TracingConfig' => [ 'shape' => 'TracingConfig', ], 'RevisionId' => [ 'shape' => 'String', ], 'Layers' => [ 'shape' => 'LayerList', ], 'FileSystemConfigs' => [ 'shape' => 'FileSystemConfigList', ], 'ImageConfig' => [ 'shape' => 'ImageConfig', ], 'EphemeralStorage' => [ 'shape' => 'EphemeralStorage', ], 'SnapStart' => [ 'shape' => 'SnapStart', ], 'LoggingConfig' => [ 'shape' => 'LoggingConfig', ], ], ], 'UpdateFunctionEventInvokeConfigRequest' => [ 'type' => 'structure', 'required' => [ 'FunctionName', ], 'members' => [ 'FunctionName' => [ 'shape' => 'FunctionName', 'location' => 'uri', 'locationName' => 'FunctionName', ], 'Qualifier' => [ 'shape' => 'Qualifier', 'location' => 'querystring', 'locationName' => 'Qualifier', ], 'MaximumRetryAttempts' => [ 'shape' => 'MaximumRetryAttempts', ], 'MaximumEventAgeInSeconds' => [ 'shape' => 'MaximumEventAgeInSeconds', ], 'DestinationConfig' => [ 'shape' => 'DestinationConfig', ], ], ], 'UpdateFunctionUrlConfigRequest' => [ 'type' => 'structure', 'required' => [ 'FunctionName', ], 'members' => [ 'FunctionName' => [ 'shape' => 'FunctionName', 'location' => 'uri', 'locationName' => 'FunctionName', ], 'Qualifier' => [ 'shape' => 'FunctionUrlQualifier', 'location' => 'querystring', 'locationName' => 'Qualifier', ], 'AuthType' => [ 'shape' => 'FunctionUrlAuthType', ], 'Cors' => [ 'shape' => 'Cors', ], 'InvokeMode' => [ 'shape' => 'InvokeMode', ], ], ], 'UpdateFunctionUrlConfigResponse' => [ 'type' => 'structure', 'required' => [ 'FunctionUrl', 'FunctionArn', 'AuthType', 'CreationTime', 'LastModifiedTime', ], 'members' => [ 'FunctionUrl' => [ 'shape' => 'FunctionUrl', ], 'FunctionArn' => [ 'shape' => 'FunctionArn', ], 'AuthType' => [ 'shape' => 'FunctionUrlAuthType', ], 'Cors' => [ 'shape' => 'Cors', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'InvokeMode' => [ 'shape' => 'InvokeMode', ], ], ], 'UpdateRuntimeOn' => [ 'type' => 'string', 'enum' => [ 'Auto', 'Manual', 'FunctionUpdate', ], ], 'Version' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '(\\$LATEST|[0-9]+)', ], 'VpcConfig' => [ 'type' => 'structure', 'members' => [ 'SubnetIds' => [ 'shape' => 'SubnetIds', ], 'SecurityGroupIds' => [ 'shape' => 'SecurityGroupIds', ], 'Ipv6AllowedForDualStack' => [ 'shape' => 'NullableBoolean', ], ], ], 'VpcConfigResponse' => [ 'type' => 'structure', 'members' => [ 'SubnetIds' => [ 'shape' => 'SubnetIds', ], 'SecurityGroupIds' => [ 'shape' => 'SecurityGroupIds', ], 'VpcId' => [ 'shape' => 'VpcId', ], 'Ipv6AllowedForDualStack' => [ 'shape' => 'NullableBoolean', ], ], ], 'VpcId' => [ 'type' => 'string', ], 'Weight' => [ 'type' => 'double', 'max' => 1.0, 'min' => 0.0, ], 'WorkingDirectory' => [ 'type' => 'string', 'max' => 1000, ], ],];
