"use strict";(self.webpackChunk=self.webpackChunk||[]).push([[151],{151:(t,e,l)=>{l.r(e),l.d(e,{default:()=>o});var a=l(9726),n={class:"relative"},r={class:"capitalize text-lg font-bold mb-1"},s={class:"text-sm font-medium capitalize text-text"};const c={name:"HeaderComponent",computed:{carts:function(){return this.$store.getters["frontendCart/lists"]}}};const o=(0,l(6262).A)(c,[["render",function(t,e,l,c,o,i){return(0,a.openBlock)(),(0,a.createElementBlock)("dl",n,[(0,a.createElementVNode)("dt",r,(0,a.toDisplayString)(t.$t("label.your_shipping_cart")),1),(0,a.createElementVNode)("dd",s," ("+(0,a.toDisplayString)(i.carts.length)+") "+(0,a.toDisplayString)(i.carts.length>1?t.$t("label.products"):t.$t("label.product")),1)])}]])}}]);