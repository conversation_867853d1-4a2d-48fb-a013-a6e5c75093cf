"use strict";(self.webpackChunk=self.webpackChunk||[]).push([[5044],{4538:(e,t,r)=>{r.d(t,{A:()=>i});var n=r(9726),o={class:"db-breadcrumb"},a={class:"db-breadcrumb-list"},c={key:0,class:"db-breadcrumb-item"},u={class:"db-breadcrumb-item"},l={key:0},s={key:1};const m={name:"BreadcrumbComponent",data:function(){return{breadcrumbs:[]}},computed:{authDefaultPermission:function(){return this.$store.getters.authDefaultPermission}},watch:{$route:function(){this.route()}},created:function(){this.route()},methods:{route:function(){var e,t=[],r=this.$route.matched;if(r.length>0)for(e=0;e<r.length;e++)r[e].meta.breadcrumb&&(t[e]=r[e]);this.breadcrumbs=t}}};const i=(0,r(6262).A)(m,[["render",function(e,t,r,m,i,d){var b=(0,n.resolveComponent)("router-link");return(0,n.openBlock)(),(0,n.createElementBlock)("div",o,[(0,n.createElementVNode)("ul",a,[Object.keys(d.authDefaultPermission).length>0?((0,n.openBlock)(),(0,n.createElementBlock)("li",c,[(0,n.createVNode)(b,{class:"db-breadcrumb-link",to:"/admin/"+d.authDefaultPermission.url},{default:(0,n.withCtx)(function(){return[(0,n.createTextVNode)((0,n.toDisplayString)(e.$t("menu."+d.authDefaultPermission.name)),1)]}),_:1},8,["to"])])):(0,n.createCommentVNode)("",!0),((0,n.openBlock)(!0),(0,n.createElementBlock)(n.Fragment,null,(0,n.renderList)(i.breadcrumbs,function(t,r){return(0,n.openBlock)(),(0,n.createElementBlock)("li",u,[r!==Object.keys(i.breadcrumbs).length-1?((0,n.openBlock)(),(0,n.createElementBlock)("span",l,[(0,n.createVNode)(b,{class:"db-breadcrumb-link",to:t.path},{default:(0,n.withCtx)(function(){return[(0,n.createTextVNode)((0,n.toDisplayString)(e.$t("menu."+t.meta.breadcrumb)),1)]}),_:2},1032,["to"])])):((0,n.openBlock)(),(0,n.createElementBlock)("span",s,(0,n.toDisplayString)(e.$t("menu."+t.meta.breadcrumb)),1))])}),256))])])}]])},5044:(e,t,r)=>{r.r(t),r.d(t,{default:()=>u});var n=r(9726),o={class:"row"},a={class:"col-12"};const c={name:"CouponComponent",components:{BreadcrumbComponent:r(4538).A}};const u=(0,r(6262).A)(c,[["render",function(e,t,r,c,u,l){var s=(0,n.resolveComponent)("BreadcrumbComponent"),m=(0,n.resolveComponent)("router-view");return(0,n.openBlock)(),(0,n.createElementBlock)("div",o,[(0,n.createElementVNode)("div",a,[(0,n.createVNode)(s)]),(0,n.createVNode)(m)])}]])}}]);