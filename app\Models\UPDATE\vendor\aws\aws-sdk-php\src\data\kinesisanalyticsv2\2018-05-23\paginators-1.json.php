<?php
// This file was auto-generated from sdk-root/src/data/kinesisanalyticsv2/2018-05-23/paginators-1.json
return [ 'pagination' => [ 'ListApplicationOperations' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'Limit', 'result_key' => 'ApplicationOperationInfoList', ], 'ListApplicationSnapshots' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'Limit', 'result_key' => 'SnapshotSummaries', ], 'ListApplicationVersions' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'Limit', 'result_key' => 'ApplicationVersionSummaries', ], 'ListApplications' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'Limit', 'result_key' => 'ApplicationSummaries', ], ],];
