<?php
// This file was auto-generated from sdk-root/src/data/codecommit/2015-04-13/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2015-04-13', 'endpointPrefix' => 'codecommit', 'jsonVersion' => '1.1', 'protocol' => 'json', 'protocols' => [ 'json', ], 'serviceAbbreviation' => 'CodeCommit', 'serviceFullName' => 'AWS CodeCommit', 'serviceId' => 'CodeCommit', 'signatureVersion' => 'v4', 'targetPrefix' => 'CodeCommit_20150413', 'uid' => 'codecommit-2015-04-13', 'auth' => [ 'aws.auth#sigv4', ], ], 'operations' => [ 'AssociateApprovalRuleTemplateWithRepository' => [ 'name' => 'AssociateApprovalRuleTemplateWithRepository', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AssociateApprovalRuleTemplateWithRepositoryInput', ], 'errors' => [ [ 'shape' => 'ApprovalRuleTemplateNameRequiredException', ], [ 'shape' => 'InvalidApprovalRuleTemplateNameException', ], [ 'shape' => 'ApprovalRuleTemplateDoesNotExistException', ], [ 'shape' => 'MaximumRuleTemplatesAssociatedWithRepositoryException', ], [ 'shape' => 'RepositoryNameRequiredException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'RepositoryDoesNotExistException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'BatchAssociateApprovalRuleTemplateWithRepositories' => [ 'name' => 'BatchAssociateApprovalRuleTemplateWithRepositories', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchAssociateApprovalRuleTemplateWithRepositoriesInput', ], 'output' => [ 'shape' => 'BatchAssociateApprovalRuleTemplateWithRepositoriesOutput', ], 'errors' => [ [ 'shape' => 'ApprovalRuleTemplateNameRequiredException', ], [ 'shape' => 'InvalidApprovalRuleTemplateNameException', ], [ 'shape' => 'ApprovalRuleTemplateDoesNotExistException', ], [ 'shape' => 'RepositoryNamesRequiredException', ], [ 'shape' => 'MaximumRepositoryNamesExceededException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'BatchDescribeMergeConflicts' => [ 'name' => 'BatchDescribeMergeConflicts', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchDescribeMergeConflictsInput', ], 'output' => [ 'shape' => 'BatchDescribeMergeConflictsOutput', ], 'errors' => [ [ 'shape' => 'RepositoryNameRequiredException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'RepositoryDoesNotExistException', ], [ 'shape' => 'MergeOptionRequiredException', ], [ 'shape' => 'InvalidMergeOptionException', ], [ 'shape' => 'InvalidContinuationTokenException', ], [ 'shape' => 'CommitRequiredException', ], [ 'shape' => 'CommitDoesNotExistException', ], [ 'shape' => 'InvalidCommitException', ], [ 'shape' => 'TipsDivergenceExceededException', ], [ 'shape' => 'InvalidMaxConflictFilesException', ], [ 'shape' => 'InvalidMaxMergeHunksException', ], [ 'shape' => 'InvalidConflictDetailLevelException', ], [ 'shape' => 'InvalidConflictResolutionStrategyException', ], [ 'shape' => 'MaximumFileContentToLoadExceededException', ], [ 'shape' => 'MaximumItemsToCompareExceededException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'BatchDisassociateApprovalRuleTemplateFromRepositories' => [ 'name' => 'BatchDisassociateApprovalRuleTemplateFromRepositories', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchDisassociateApprovalRuleTemplateFromRepositoriesInput', ], 'output' => [ 'shape' => 'BatchDisassociateApprovalRuleTemplateFromRepositoriesOutput', ], 'errors' => [ [ 'shape' => 'ApprovalRuleTemplateNameRequiredException', ], [ 'shape' => 'InvalidApprovalRuleTemplateNameException', ], [ 'shape' => 'ApprovalRuleTemplateDoesNotExistException', ], [ 'shape' => 'RepositoryNamesRequiredException', ], [ 'shape' => 'MaximumRepositoryNamesExceededException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'BatchGetCommits' => [ 'name' => 'BatchGetCommits', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchGetCommitsInput', ], 'output' => [ 'shape' => 'BatchGetCommitsOutput', ], 'errors' => [ [ 'shape' => 'CommitIdsListRequiredException', ], [ 'shape' => 'CommitIdsLimitExceededException', ], [ 'shape' => 'RepositoryNameRequiredException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'RepositoryDoesNotExistException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'BatchGetRepositories' => [ 'name' => 'BatchGetRepositories', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchGetRepositoriesInput', ], 'output' => [ 'shape' => 'BatchGetRepositoriesOutput', ], 'errors' => [ [ 'shape' => 'RepositoryNamesRequiredException', ], [ 'shape' => 'MaximumRepositoryNamesExceededException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'CreateApprovalRuleTemplate' => [ 'name' => 'CreateApprovalRuleTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateApprovalRuleTemplateInput', ], 'output' => [ 'shape' => 'CreateApprovalRuleTemplateOutput', ], 'errors' => [ [ 'shape' => 'ApprovalRuleTemplateNameRequiredException', ], [ 'shape' => 'InvalidApprovalRuleTemplateNameException', ], [ 'shape' => 'ApprovalRuleTemplateNameAlreadyExistsException', ], [ 'shape' => 'ApprovalRuleTemplateContentRequiredException', ], [ 'shape' => 'InvalidApprovalRuleTemplateContentException', ], [ 'shape' => 'InvalidApprovalRuleTemplateDescriptionException', ], [ 'shape' => 'NumberOfRuleTemplatesExceededException', ], ], ], 'CreateBranch' => [ 'name' => 'CreateBranch', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateBranchInput', ], 'errors' => [ [ 'shape' => 'RepositoryNameRequiredException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'RepositoryDoesNotExistException', ], [ 'shape' => 'BranchNameRequiredException', ], [ 'shape' => 'BranchNameExistsException', ], [ 'shape' => 'InvalidBranchNameException', ], [ 'shape' => 'CommitIdRequiredException', ], [ 'shape' => 'CommitDoesNotExistException', ], [ 'shape' => 'InvalidCommitIdException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'CreateCommit' => [ 'name' => 'CreateCommit', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateCommitInput', ], 'output' => [ 'shape' => 'CreateCommitOutput', ], 'errors' => [ [ 'shape' => 'RepositoryNameRequiredException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'RepositoryDoesNotExistException', ], [ 'shape' => 'ParentCommitIdRequiredException', ], [ 'shape' => 'InvalidParentCommitIdException', ], [ 'shape' => 'ParentCommitDoesNotExistException', ], [ 'shape' => 'ParentCommitIdOutdatedException', ], [ 'shape' => 'BranchNameRequiredException', ], [ 'shape' => 'InvalidBranchNameException', ], [ 'shape' => 'BranchDoesNotExistException', ], [ 'shape' => 'BranchNameIsTagNameException', ], [ 'shape' => 'FileEntryRequiredException', ], [ 'shape' => 'MaximumFileEntriesExceededException', ], [ 'shape' => 'PutFileEntryConflictException', ], [ 'shape' => 'SourceFileOrContentRequiredException', ], [ 'shape' => 'FileContentAndSourceFileSpecifiedException', ], [ 'shape' => 'PathRequiredException', ], [ 'shape' => 'InvalidPathException', ], [ 'shape' => 'SamePathRequestException', ], [ 'shape' => 'FileDoesNotExistException', ], [ 'shape' => 'FileContentSizeLimitExceededException', ], [ 'shape' => 'FolderContentSizeLimitExceededException', ], [ 'shape' => 'InvalidDeletionParameterException', ], [ 'shape' => 'RestrictedSourceFileException', ], [ 'shape' => 'FileModeRequiredException', ], [ 'shape' => 'InvalidFileModeException', ], [ 'shape' => 'NameLengthExceededException', ], [ 'shape' => 'InvalidEmailException', ], [ 'shape' => 'CommitMessageLengthExceededException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], [ 'shape' => 'NoChangeException', ], [ 'shape' => 'FileNameConflictsWithDirectoryNameException', ], [ 'shape' => 'DirectoryNameConflictsWithFileNameException', ], [ 'shape' => 'FilePathConflictsWithSubmodulePathException', ], ], ], 'CreatePullRequest' => [ 'name' => 'CreatePullRequest', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreatePullRequestInput', ], 'output' => [ 'shape' => 'CreatePullRequestOutput', ], 'errors' => [ [ 'shape' => 'RepositoryNameRequiredException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'RepositoryDoesNotExistException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], [ 'shape' => 'ClientRequestTokenRequiredException', ], [ 'shape' => 'InvalidClientRequestTokenException', ], [ 'shape' => 'IdempotencyParameterMismatchException', ], [ 'shape' => 'ReferenceNameRequiredException', ], [ 'shape' => 'InvalidReferenceNameException', ], [ 'shape' => 'ReferenceDoesNotExistException', ], [ 'shape' => 'ReferenceTypeNotSupportedException', ], [ 'shape' => 'TitleRequiredException', ], [ 'shape' => 'InvalidTitleException', ], [ 'shape' => 'InvalidDescriptionException', ], [ 'shape' => 'TargetsRequiredException', ], [ 'shape' => 'InvalidTargetsException', ], [ 'shape' => 'TargetRequiredException', ], [ 'shape' => 'InvalidTargetException', ], [ 'shape' => 'MultipleRepositoriesInPullRequestException', ], [ 'shape' => 'MaximumOpenPullRequestsExceededException', ], [ 'shape' => 'SourceAndDestinationAreSameException', ], ], ], 'CreatePullRequestApprovalRule' => [ 'name' => 'CreatePullRequestApprovalRule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreatePullRequestApprovalRuleInput', ], 'output' => [ 'shape' => 'CreatePullRequestApprovalRuleOutput', ], 'errors' => [ [ 'shape' => 'ApprovalRuleNameRequiredException', ], [ 'shape' => 'InvalidApprovalRuleNameException', ], [ 'shape' => 'ApprovalRuleNameAlreadyExistsException', ], [ 'shape' => 'ApprovalRuleContentRequiredException', ], [ 'shape' => 'InvalidApprovalRuleContentException', ], [ 'shape' => 'NumberOfRulesExceededException', ], [ 'shape' => 'PullRequestDoesNotExistException', ], [ 'shape' => 'InvalidPullRequestIdException', ], [ 'shape' => 'PullRequestIdRequiredException', ], [ 'shape' => 'PullRequestAlreadyClosedException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'CreateRepository' => [ 'name' => 'CreateRepository', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateRepositoryInput', ], 'output' => [ 'shape' => 'CreateRepositoryOutput', ], 'errors' => [ [ 'shape' => 'RepositoryNameExistsException', ], [ 'shape' => 'RepositoryNameRequiredException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'InvalidRepositoryDescriptionException', ], [ 'shape' => 'RepositoryLimitExceededException', ], [ 'shape' => 'OperationNotAllowedException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], [ 'shape' => 'EncryptionKeyInvalidIdException', ], [ 'shape' => 'EncryptionKeyInvalidUsageException', ], [ 'shape' => 'InvalidTagsMapException', ], [ 'shape' => 'TooManyTagsException', ], [ 'shape' => 'InvalidSystemTagUsageException', ], [ 'shape' => 'TagPolicyException', ], ], ], 'CreateUnreferencedMergeCommit' => [ 'name' => 'CreateUnreferencedMergeCommit', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateUnreferencedMergeCommitInput', ], 'output' => [ 'shape' => 'CreateUnreferencedMergeCommitOutput', ], 'errors' => [ [ 'shape' => 'RepositoryNameRequiredException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'RepositoryDoesNotExistException', ], [ 'shape' => 'TipsDivergenceExceededException', ], [ 'shape' => 'CommitRequiredException', ], [ 'shape' => 'InvalidCommitException', ], [ 'shape' => 'CommitDoesNotExistException', ], [ 'shape' => 'MergeOptionRequiredException', ], [ 'shape' => 'InvalidMergeOptionException', ], [ 'shape' => 'InvalidConflictDetailLevelException', ], [ 'shape' => 'InvalidConflictResolutionStrategyException', ], [ 'shape' => 'InvalidConflictResolutionException', ], [ 'shape' => 'ManualMergeRequiredException', ], [ 'shape' => 'MaximumConflictResolutionEntriesExceededException', ], [ 'shape' => 'MultipleConflictResolutionEntriesException', ], [ 'shape' => 'ReplacementTypeRequiredException', ], [ 'shape' => 'InvalidReplacementTypeException', ], [ 'shape' => 'ReplacementContentRequiredException', ], [ 'shape' => 'InvalidReplacementContentException', ], [ 'shape' => 'PathRequiredException', ], [ 'shape' => 'InvalidPathException', ], [ 'shape' => 'FileContentSizeLimitExceededException', ], [ 'shape' => 'FolderContentSizeLimitExceededException', ], [ 'shape' => 'MaximumFileContentToLoadExceededException', ], [ 'shape' => 'MaximumItemsToCompareExceededException', ], [ 'shape' => 'ConcurrentReferenceUpdateException', ], [ 'shape' => 'FileModeRequiredException', ], [ 'shape' => 'InvalidFileModeException', ], [ 'shape' => 'NameLengthExceededException', ], [ 'shape' => 'InvalidEmailException', ], [ 'shape' => 'CommitMessageLengthExceededException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'DeleteApprovalRuleTemplate' => [ 'name' => 'DeleteApprovalRuleTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteApprovalRuleTemplateInput', ], 'output' => [ 'shape' => 'DeleteApprovalRuleTemplateOutput', ], 'errors' => [ [ 'shape' => 'ApprovalRuleTemplateNameRequiredException', ], [ 'shape' => 'InvalidApprovalRuleTemplateNameException', ], [ 'shape' => 'ApprovalRuleTemplateInUseException', ], ], ], 'DeleteBranch' => [ 'name' => 'DeleteBranch', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteBranchInput', ], 'output' => [ 'shape' => 'DeleteBranchOutput', ], 'errors' => [ [ 'shape' => 'RepositoryNameRequiredException', ], [ 'shape' => 'RepositoryDoesNotExistException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'BranchNameRequiredException', ], [ 'shape' => 'InvalidBranchNameException', ], [ 'shape' => 'DefaultBranchCannotBeDeletedException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'DeleteCommentContent' => [ 'name' => 'DeleteCommentContent', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteCommentContentInput', ], 'output' => [ 'shape' => 'DeleteCommentContentOutput', ], 'errors' => [ [ 'shape' => 'CommentDoesNotExistException', ], [ 'shape' => 'CommentIdRequiredException', ], [ 'shape' => 'InvalidCommentIdException', ], [ 'shape' => 'CommentDeletedException', ], ], ], 'DeleteFile' => [ 'name' => 'DeleteFile', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteFileInput', ], 'output' => [ 'shape' => 'DeleteFileOutput', ], 'errors' => [ [ 'shape' => 'RepositoryNameRequiredException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'RepositoryDoesNotExistException', ], [ 'shape' => 'ParentCommitIdRequiredException', ], [ 'shape' => 'InvalidParentCommitIdException', ], [ 'shape' => 'ParentCommitDoesNotExistException', ], [ 'shape' => 'ParentCommitIdOutdatedException', ], [ 'shape' => 'PathRequiredException', ], [ 'shape' => 'InvalidPathException', ], [ 'shape' => 'FileDoesNotExistException', ], [ 'shape' => 'BranchNameRequiredException', ], [ 'shape' => 'InvalidBranchNameException', ], [ 'shape' => 'BranchDoesNotExistException', ], [ 'shape' => 'BranchNameIsTagNameException', ], [ 'shape' => 'NameLengthExceededException', ], [ 'shape' => 'InvalidEmailException', ], [ 'shape' => 'CommitMessageLengthExceededException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'DeletePullRequestApprovalRule' => [ 'name' => 'DeletePullRequestApprovalRule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeletePullRequestApprovalRuleInput', ], 'output' => [ 'shape' => 'DeletePullRequestApprovalRuleOutput', ], 'errors' => [ [ 'shape' => 'PullRequestDoesNotExistException', ], [ 'shape' => 'InvalidPullRequestIdException', ], [ 'shape' => 'PullRequestIdRequiredException', ], [ 'shape' => 'PullRequestAlreadyClosedException', ], [ 'shape' => 'ApprovalRuleNameRequiredException', ], [ 'shape' => 'InvalidApprovalRuleNameException', ], [ 'shape' => 'CannotDeleteApprovalRuleFromTemplateException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'DeleteRepository' => [ 'name' => 'DeleteRepository', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteRepositoryInput', ], 'output' => [ 'shape' => 'DeleteRepositoryOutput', ], 'errors' => [ [ 'shape' => 'RepositoryNameRequiredException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'DescribeMergeConflicts' => [ 'name' => 'DescribeMergeConflicts', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeMergeConflictsInput', ], 'output' => [ 'shape' => 'DescribeMergeConflictsOutput', ], 'errors' => [ [ 'shape' => 'RepositoryNameRequiredException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'RepositoryDoesNotExistException', ], [ 'shape' => 'MergeOptionRequiredException', ], [ 'shape' => 'InvalidMergeOptionException', ], [ 'shape' => 'InvalidContinuationTokenException', ], [ 'shape' => 'CommitRequiredException', ], [ 'shape' => 'CommitDoesNotExistException', ], [ 'shape' => 'InvalidCommitException', ], [ 'shape' => 'TipsDivergenceExceededException', ], [ 'shape' => 'PathRequiredException', ], [ 'shape' => 'InvalidPathException', ], [ 'shape' => 'FileDoesNotExistException', ], [ 'shape' => 'InvalidMaxMergeHunksException', ], [ 'shape' => 'InvalidConflictDetailLevelException', ], [ 'shape' => 'InvalidConflictResolutionStrategyException', ], [ 'shape' => 'MaximumFileContentToLoadExceededException', ], [ 'shape' => 'MaximumItemsToCompareExceededException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'DescribePullRequestEvents' => [ 'name' => 'DescribePullRequestEvents', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribePullRequestEventsInput', ], 'output' => [ 'shape' => 'DescribePullRequestEventsOutput', ], 'errors' => [ [ 'shape' => 'PullRequestDoesNotExistException', ], [ 'shape' => 'InvalidPullRequestIdException', ], [ 'shape' => 'PullRequestIdRequiredException', ], [ 'shape' => 'InvalidPullRequestEventTypeException', ], [ 'shape' => 'InvalidActorArnException', ], [ 'shape' => 'ActorDoesNotExistException', ], [ 'shape' => 'InvalidMaxResultsException', ], [ 'shape' => 'InvalidContinuationTokenException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'DisassociateApprovalRuleTemplateFromRepository' => [ 'name' => 'DisassociateApprovalRuleTemplateFromRepository', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DisassociateApprovalRuleTemplateFromRepositoryInput', ], 'errors' => [ [ 'shape' => 'ApprovalRuleTemplateNameRequiredException', ], [ 'shape' => 'InvalidApprovalRuleTemplateNameException', ], [ 'shape' => 'ApprovalRuleTemplateDoesNotExistException', ], [ 'shape' => 'RepositoryNameRequiredException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'RepositoryDoesNotExistException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'EvaluatePullRequestApprovalRules' => [ 'name' => 'EvaluatePullRequestApprovalRules', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'EvaluatePullRequestApprovalRulesInput', ], 'output' => [ 'shape' => 'EvaluatePullRequestApprovalRulesOutput', ], 'errors' => [ [ 'shape' => 'PullRequestDoesNotExistException', ], [ 'shape' => 'InvalidPullRequestIdException', ], [ 'shape' => 'PullRequestIdRequiredException', ], [ 'shape' => 'InvalidRevisionIdException', ], [ 'shape' => 'RevisionIdRequiredException', ], [ 'shape' => 'RevisionNotCurrentException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'GetApprovalRuleTemplate' => [ 'name' => 'GetApprovalRuleTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetApprovalRuleTemplateInput', ], 'output' => [ 'shape' => 'GetApprovalRuleTemplateOutput', ], 'errors' => [ [ 'shape' => 'ApprovalRuleTemplateNameRequiredException', ], [ 'shape' => 'InvalidApprovalRuleTemplateNameException', ], [ 'shape' => 'ApprovalRuleTemplateDoesNotExistException', ], ], ], 'GetBlob' => [ 'name' => 'GetBlob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetBlobInput', ], 'output' => [ 'shape' => 'GetBlobOutput', ], 'errors' => [ [ 'shape' => 'RepositoryNameRequiredException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'RepositoryDoesNotExistException', ], [ 'shape' => 'BlobIdRequiredException', ], [ 'shape' => 'InvalidBlobIdException', ], [ 'shape' => 'BlobIdDoesNotExistException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], [ 'shape' => 'FileTooLargeException', ], ], ], 'GetBranch' => [ 'name' => 'GetBranch', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetBranchInput', ], 'output' => [ 'shape' => 'GetBranchOutput', ], 'errors' => [ [ 'shape' => 'RepositoryNameRequiredException', ], [ 'shape' => 'RepositoryDoesNotExistException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'BranchNameRequiredException', ], [ 'shape' => 'InvalidBranchNameException', ], [ 'shape' => 'BranchDoesNotExistException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'GetComment' => [ 'name' => 'GetComment', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetCommentInput', ], 'output' => [ 'shape' => 'GetCommentOutput', ], 'errors' => [ [ 'shape' => 'CommentDoesNotExistException', ], [ 'shape' => 'CommentDeletedException', ], [ 'shape' => 'CommentIdRequiredException', ], [ 'shape' => 'InvalidCommentIdException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'GetCommentReactions' => [ 'name' => 'GetCommentReactions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetCommentReactionsInput', ], 'output' => [ 'shape' => 'GetCommentReactionsOutput', ], 'errors' => [ [ 'shape' => 'CommentDoesNotExistException', ], [ 'shape' => 'CommentIdRequiredException', ], [ 'shape' => 'InvalidCommentIdException', ], [ 'shape' => 'InvalidReactionUserArnException', ], [ 'shape' => 'InvalidMaxResultsException', ], [ 'shape' => 'InvalidContinuationTokenException', ], [ 'shape' => 'CommentDeletedException', ], ], ], 'GetCommentsForComparedCommit' => [ 'name' => 'GetCommentsForComparedCommit', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetCommentsForComparedCommitInput', ], 'output' => [ 'shape' => 'GetCommentsForComparedCommitOutput', ], 'errors' => [ [ 'shape' => 'RepositoryNameRequiredException', ], [ 'shape' => 'RepositoryDoesNotExistException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'CommitIdRequiredException', ], [ 'shape' => 'InvalidCommitIdException', ], [ 'shape' => 'CommitDoesNotExistException', ], [ 'shape' => 'InvalidMaxResultsException', ], [ 'shape' => 'InvalidContinuationTokenException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'GetCommentsForPullRequest' => [ 'name' => 'GetCommentsForPullRequest', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetCommentsForPullRequestInput', ], 'output' => [ 'shape' => 'GetCommentsForPullRequestOutput', ], 'errors' => [ [ 'shape' => 'PullRequestIdRequiredException', ], [ 'shape' => 'PullRequestDoesNotExistException', ], [ 'shape' => 'InvalidPullRequestIdException', ], [ 'shape' => 'RepositoryNameRequiredException', ], [ 'shape' => 'RepositoryDoesNotExistException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'CommitIdRequiredException', ], [ 'shape' => 'InvalidCommitIdException', ], [ 'shape' => 'CommitDoesNotExistException', ], [ 'shape' => 'InvalidMaxResultsException', ], [ 'shape' => 'InvalidContinuationTokenException', ], [ 'shape' => 'RepositoryNotAssociatedWithPullRequestException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'GetCommit' => [ 'name' => 'GetCommit', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetCommitInput', ], 'output' => [ 'shape' => 'GetCommitOutput', ], 'errors' => [ [ 'shape' => 'RepositoryNameRequiredException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'RepositoryDoesNotExistException', ], [ 'shape' => 'CommitIdRequiredException', ], [ 'shape' => 'InvalidCommitIdException', ], [ 'shape' => 'CommitIdDoesNotExistException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'GetDifferences' => [ 'name' => 'GetDifferences', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDifferencesInput', ], 'output' => [ 'shape' => 'GetDifferencesOutput', ], 'errors' => [ [ 'shape' => 'RepositoryNameRequiredException', ], [ 'shape' => 'RepositoryDoesNotExistException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'InvalidContinuationTokenException', ], [ 'shape' => 'InvalidMaxResultsException', ], [ 'shape' => 'InvalidCommitIdException', ], [ 'shape' => 'CommitRequiredException', ], [ 'shape' => 'InvalidCommitException', ], [ 'shape' => 'CommitDoesNotExistException', ], [ 'shape' => 'InvalidPathException', ], [ 'shape' => 'PathDoesNotExistException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'GetFile' => [ 'name' => 'GetFile', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetFileInput', ], 'output' => [ 'shape' => 'GetFileOutput', ], 'errors' => [ [ 'shape' => 'RepositoryNameRequiredException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'RepositoryDoesNotExistException', ], [ 'shape' => 'InvalidCommitException', ], [ 'shape' => 'CommitDoesNotExistException', ], [ 'shape' => 'PathRequiredException', ], [ 'shape' => 'InvalidPathException', ], [ 'shape' => 'FileDoesNotExistException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], [ 'shape' => 'FileTooLargeException', ], ], ], 'GetFolder' => [ 'name' => 'GetFolder', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetFolderInput', ], 'output' => [ 'shape' => 'GetFolderOutput', ], 'errors' => [ [ 'shape' => 'RepositoryNameRequiredException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'RepositoryDoesNotExistException', ], [ 'shape' => 'InvalidCommitException', ], [ 'shape' => 'CommitDoesNotExistException', ], [ 'shape' => 'PathRequiredException', ], [ 'shape' => 'InvalidPathException', ], [ 'shape' => 'FolderDoesNotExistException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'GetMergeCommit' => [ 'name' => 'GetMergeCommit', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetMergeCommitInput', ], 'output' => [ 'shape' => 'GetMergeCommitOutput', ], 'errors' => [ [ 'shape' => 'RepositoryNameRequiredException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'RepositoryDoesNotExistException', ], [ 'shape' => 'CommitRequiredException', ], [ 'shape' => 'InvalidCommitException', ], [ 'shape' => 'CommitDoesNotExistException', ], [ 'shape' => 'InvalidConflictDetailLevelException', ], [ 'shape' => 'InvalidConflictResolutionStrategyException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'GetMergeConflicts' => [ 'name' => 'GetMergeConflicts', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetMergeConflictsInput', ], 'output' => [ 'shape' => 'GetMergeConflictsOutput', ], 'errors' => [ [ 'shape' => 'RepositoryNameRequiredException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'RepositoryDoesNotExistException', ], [ 'shape' => 'MergeOptionRequiredException', ], [ 'shape' => 'InvalidMergeOptionException', ], [ 'shape' => 'InvalidContinuationTokenException', ], [ 'shape' => 'CommitRequiredException', ], [ 'shape' => 'CommitDoesNotExistException', ], [ 'shape' => 'InvalidCommitException', ], [ 'shape' => 'TipsDivergenceExceededException', ], [ 'shape' => 'InvalidMaxConflictFilesException', ], [ 'shape' => 'InvalidConflictDetailLevelException', ], [ 'shape' => 'InvalidDestinationCommitSpecifierException', ], [ 'shape' => 'InvalidSourceCommitSpecifierException', ], [ 'shape' => 'InvalidConflictResolutionStrategyException', ], [ 'shape' => 'MaximumFileContentToLoadExceededException', ], [ 'shape' => 'MaximumItemsToCompareExceededException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'GetMergeOptions' => [ 'name' => 'GetMergeOptions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetMergeOptionsInput', ], 'output' => [ 'shape' => 'GetMergeOptionsOutput', ], 'errors' => [ [ 'shape' => 'RepositoryNameRequiredException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'RepositoryDoesNotExistException', ], [ 'shape' => 'CommitRequiredException', ], [ 'shape' => 'CommitDoesNotExistException', ], [ 'shape' => 'InvalidCommitException', ], [ 'shape' => 'TipsDivergenceExceededException', ], [ 'shape' => 'InvalidConflictDetailLevelException', ], [ 'shape' => 'InvalidConflictResolutionStrategyException', ], [ 'shape' => 'MaximumFileContentToLoadExceededException', ], [ 'shape' => 'MaximumItemsToCompareExceededException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'GetPullRequest' => [ 'name' => 'GetPullRequest', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetPullRequestInput', ], 'output' => [ 'shape' => 'GetPullRequestOutput', ], 'errors' => [ [ 'shape' => 'PullRequestDoesNotExistException', ], [ 'shape' => 'InvalidPullRequestIdException', ], [ 'shape' => 'PullRequestIdRequiredException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'GetPullRequestApprovalStates' => [ 'name' => 'GetPullRequestApprovalStates', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetPullRequestApprovalStatesInput', ], 'output' => [ 'shape' => 'GetPullRequestApprovalStatesOutput', ], 'errors' => [ [ 'shape' => 'PullRequestDoesNotExistException', ], [ 'shape' => 'InvalidPullRequestIdException', ], [ 'shape' => 'PullRequestIdRequiredException', ], [ 'shape' => 'InvalidRevisionIdException', ], [ 'shape' => 'RevisionIdRequiredException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'GetPullRequestOverrideState' => [ 'name' => 'GetPullRequestOverrideState', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetPullRequestOverrideStateInput', ], 'output' => [ 'shape' => 'GetPullRequestOverrideStateOutput', ], 'errors' => [ [ 'shape' => 'PullRequestDoesNotExistException', ], [ 'shape' => 'InvalidPullRequestIdException', ], [ 'shape' => 'PullRequestIdRequiredException', ], [ 'shape' => 'InvalidRevisionIdException', ], [ 'shape' => 'RevisionIdRequiredException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'GetRepository' => [ 'name' => 'GetRepository', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetRepositoryInput', ], 'output' => [ 'shape' => 'GetRepositoryOutput', ], 'errors' => [ [ 'shape' => 'RepositoryNameRequiredException', ], [ 'shape' => 'RepositoryDoesNotExistException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'GetRepositoryTriggers' => [ 'name' => 'GetRepositoryTriggers', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetRepositoryTriggersInput', ], 'output' => [ 'shape' => 'GetRepositoryTriggersOutput', ], 'errors' => [ [ 'shape' => 'RepositoryNameRequiredException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'RepositoryDoesNotExistException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'ListApprovalRuleTemplates' => [ 'name' => 'ListApprovalRuleTemplates', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListApprovalRuleTemplatesInput', ], 'output' => [ 'shape' => 'ListApprovalRuleTemplatesOutput', ], 'errors' => [ [ 'shape' => 'InvalidMaxResultsException', ], [ 'shape' => 'InvalidContinuationTokenException', ], ], ], 'ListAssociatedApprovalRuleTemplatesForRepository' => [ 'name' => 'ListAssociatedApprovalRuleTemplatesForRepository', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListAssociatedApprovalRuleTemplatesForRepositoryInput', ], 'output' => [ 'shape' => 'ListAssociatedApprovalRuleTemplatesForRepositoryOutput', ], 'errors' => [ [ 'shape' => 'RepositoryNameRequiredException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'RepositoryDoesNotExistException', ], [ 'shape' => 'InvalidMaxResultsException', ], [ 'shape' => 'InvalidContinuationTokenException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'ListBranches' => [ 'name' => 'ListBranches', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListBranchesInput', ], 'output' => [ 'shape' => 'ListBranchesOutput', ], 'errors' => [ [ 'shape' => 'RepositoryNameRequiredException', ], [ 'shape' => 'RepositoryDoesNotExistException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], [ 'shape' => 'InvalidContinuationTokenException', ], ], ], 'ListFileCommitHistory' => [ 'name' => 'ListFileCommitHistory', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListFileCommitHistoryRequest', ], 'output' => [ 'shape' => 'ListFileCommitHistoryResponse', ], 'errors' => [ [ 'shape' => 'RepositoryNameRequiredException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'RepositoryDoesNotExistException', ], [ 'shape' => 'InvalidContinuationTokenException', ], [ 'shape' => 'InvalidMaxResultsException', ], [ 'shape' => 'TipsDivergenceExceededException', ], [ 'shape' => 'CommitRequiredException', ], [ 'shape' => 'InvalidCommitException', ], [ 'shape' => 'CommitDoesNotExistException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'ListPullRequests' => [ 'name' => 'ListPullRequests', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListPullRequestsInput', ], 'output' => [ 'shape' => 'ListPullRequestsOutput', ], 'errors' => [ [ 'shape' => 'InvalidPullRequestStatusException', ], [ 'shape' => 'InvalidAuthorArnException', ], [ 'shape' => 'AuthorDoesNotExistException', ], [ 'shape' => 'RepositoryNameRequiredException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'RepositoryDoesNotExistException', ], [ 'shape' => 'InvalidMaxResultsException', ], [ 'shape' => 'InvalidContinuationTokenException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'ListRepositories' => [ 'name' => 'ListRepositories', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListRepositoriesInput', ], 'output' => [ 'shape' => 'ListRepositoriesOutput', ], 'errors' => [ [ 'shape' => 'InvalidSortByException', ], [ 'shape' => 'InvalidOrderException', ], [ 'shape' => 'InvalidContinuationTokenException', ], ], ], 'ListRepositoriesForApprovalRuleTemplate' => [ 'name' => 'ListRepositoriesForApprovalRuleTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListRepositoriesForApprovalRuleTemplateInput', ], 'output' => [ 'shape' => 'ListRepositoriesForApprovalRuleTemplateOutput', ], 'errors' => [ [ 'shape' => 'ApprovalRuleTemplateNameRequiredException', ], [ 'shape' => 'InvalidApprovalRuleTemplateNameException', ], [ 'shape' => 'ApprovalRuleTemplateDoesNotExistException', ], [ 'shape' => 'InvalidMaxResultsException', ], [ 'shape' => 'InvalidContinuationTokenException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsForResourceInput', ], 'output' => [ 'shape' => 'ListTagsForResourceOutput', ], 'errors' => [ [ 'shape' => 'RepositoryDoesNotExistException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'ResourceArnRequiredException', ], [ 'shape' => 'InvalidResourceArnException', ], ], ], 'MergeBranchesByFastForward' => [ 'name' => 'MergeBranchesByFastForward', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'MergeBranchesByFastForwardInput', ], 'output' => [ 'shape' => 'MergeBranchesByFastForwardOutput', ], 'errors' => [ [ 'shape' => 'RepositoryNameRequiredException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'RepositoryDoesNotExistException', ], [ 'shape' => 'TipsDivergenceExceededException', ], [ 'shape' => 'CommitRequiredException', ], [ 'shape' => 'InvalidCommitException', ], [ 'shape' => 'CommitDoesNotExistException', ], [ 'shape' => 'InvalidTargetBranchException', ], [ 'shape' => 'InvalidBranchNameException', ], [ 'shape' => 'BranchNameRequiredException', ], [ 'shape' => 'BranchNameIsTagNameException', ], [ 'shape' => 'BranchDoesNotExistException', ], [ 'shape' => 'ManualMergeRequiredException', ], [ 'shape' => 'ConcurrentReferenceUpdateException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'MergeBranchesBySquash' => [ 'name' => 'MergeBranchesBySquash', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'MergeBranchesBySquashInput', ], 'output' => [ 'shape' => 'MergeBranchesBySquashOutput', ], 'errors' => [ [ 'shape' => 'RepositoryNameRequiredException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'RepositoryDoesNotExistException', ], [ 'shape' => 'TipsDivergenceExceededException', ], [ 'shape' => 'CommitRequiredException', ], [ 'shape' => 'InvalidCommitException', ], [ 'shape' => 'CommitDoesNotExistException', ], [ 'shape' => 'InvalidTargetBranchException', ], [ 'shape' => 'InvalidBranchNameException', ], [ 'shape' => 'BranchNameRequiredException', ], [ 'shape' => 'BranchNameIsTagNameException', ], [ 'shape' => 'BranchDoesNotExistException', ], [ 'shape' => 'ManualMergeRequiredException', ], [ 'shape' => 'InvalidConflictDetailLevelException', ], [ 'shape' => 'InvalidConflictResolutionStrategyException', ], [ 'shape' => 'InvalidConflictResolutionException', ], [ 'shape' => 'MaximumConflictResolutionEntriesExceededException', ], [ 'shape' => 'MultipleConflictResolutionEntriesException', ], [ 'shape' => 'ReplacementTypeRequiredException', ], [ 'shape' => 'InvalidReplacementTypeException', ], [ 'shape' => 'ReplacementContentRequiredException', ], [ 'shape' => 'InvalidReplacementContentException', ], [ 'shape' => 'PathRequiredException', ], [ 'shape' => 'InvalidPathException', ], [ 'shape' => 'FileContentSizeLimitExceededException', ], [ 'shape' => 'FolderContentSizeLimitExceededException', ], [ 'shape' => 'MaximumFileContentToLoadExceededException', ], [ 'shape' => 'MaximumItemsToCompareExceededException', ], [ 'shape' => 'FileModeRequiredException', ], [ 'shape' => 'InvalidFileModeException', ], [ 'shape' => 'NameLengthExceededException', ], [ 'shape' => 'InvalidEmailException', ], [ 'shape' => 'CommitMessageLengthExceededException', ], [ 'shape' => 'ConcurrentReferenceUpdateException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'MergeBranchesByThreeWay' => [ 'name' => 'MergeBranchesByThreeWay', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'MergeBranchesByThreeWayInput', ], 'output' => [ 'shape' => 'MergeBranchesByThreeWayOutput', ], 'errors' => [ [ 'shape' => 'RepositoryNameRequiredException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'RepositoryDoesNotExistException', ], [ 'shape' => 'TipsDivergenceExceededException', ], [ 'shape' => 'CommitRequiredException', ], [ 'shape' => 'InvalidCommitException', ], [ 'shape' => 'CommitDoesNotExistException', ], [ 'shape' => 'InvalidTargetBranchException', ], [ 'shape' => 'InvalidBranchNameException', ], [ 'shape' => 'BranchNameRequiredException', ], [ 'shape' => 'BranchNameIsTagNameException', ], [ 'shape' => 'BranchDoesNotExistException', ], [ 'shape' => 'ManualMergeRequiredException', ], [ 'shape' => 'ConcurrentReferenceUpdateException', ], [ 'shape' => 'InvalidConflictDetailLevelException', ], [ 'shape' => 'InvalidConflictResolutionStrategyException', ], [ 'shape' => 'InvalidConflictResolutionException', ], [ 'shape' => 'MaximumConflictResolutionEntriesExceededException', ], [ 'shape' => 'MultipleConflictResolutionEntriesException', ], [ 'shape' => 'ReplacementTypeRequiredException', ], [ 'shape' => 'InvalidReplacementTypeException', ], [ 'shape' => 'ReplacementContentRequiredException', ], [ 'shape' => 'InvalidReplacementContentException', ], [ 'shape' => 'PathRequiredException', ], [ 'shape' => 'InvalidPathException', ], [ 'shape' => 'FileContentSizeLimitExceededException', ], [ 'shape' => 'FolderContentSizeLimitExceededException', ], [ 'shape' => 'MaximumFileContentToLoadExceededException', ], [ 'shape' => 'MaximumItemsToCompareExceededException', ], [ 'shape' => 'FileModeRequiredException', ], [ 'shape' => 'InvalidFileModeException', ], [ 'shape' => 'NameLengthExceededException', ], [ 'shape' => 'InvalidEmailException', ], [ 'shape' => 'CommitMessageLengthExceededException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'MergePullRequestByFastForward' => [ 'name' => 'MergePullRequestByFastForward', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'MergePullRequestByFastForwardInput', ], 'output' => [ 'shape' => 'MergePullRequestByFastForwardOutput', ], 'errors' => [ [ 'shape' => 'ManualMergeRequiredException', ], [ 'shape' => 'PullRequestAlreadyClosedException', ], [ 'shape' => 'PullRequestDoesNotExistException', ], [ 'shape' => 'InvalidPullRequestIdException', ], [ 'shape' => 'PullRequestIdRequiredException', ], [ 'shape' => 'TipOfSourceReferenceIsDifferentException', ], [ 'shape' => 'ReferenceDoesNotExistException', ], [ 'shape' => 'InvalidCommitIdException', ], [ 'shape' => 'RepositoryNotAssociatedWithPullRequestException', ], [ 'shape' => 'RepositoryNameRequiredException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'RepositoryDoesNotExistException', ], [ 'shape' => 'ConcurrentReferenceUpdateException', ], [ 'shape' => 'PullRequestApprovalRulesNotSatisfiedException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'MergePullRequestBySquash' => [ 'name' => 'MergePullRequestBySquash', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'MergePullRequestBySquashInput', ], 'output' => [ 'shape' => 'MergePullRequestBySquashOutput', ], 'errors' => [ [ 'shape' => 'PullRequestAlreadyClosedException', ], [ 'shape' => 'PullRequestDoesNotExistException', ], [ 'shape' => 'PullRequestIdRequiredException', ], [ 'shape' => 'InvalidPullRequestIdException', ], [ 'shape' => 'InvalidCommitIdException', ], [ 'shape' => 'ManualMergeRequiredException', ], [ 'shape' => 'TipOfSourceReferenceIsDifferentException', ], [ 'shape' => 'TipsDivergenceExceededException', ], [ 'shape' => 'NameLengthExceededException', ], [ 'shape' => 'InvalidEmailException', ], [ 'shape' => 'CommitMessageLengthExceededException', ], [ 'shape' => 'InvalidConflictDetailLevelException', ], [ 'shape' => 'InvalidConflictResolutionStrategyException', ], [ 'shape' => 'InvalidConflictResolutionException', ], [ 'shape' => 'ReplacementTypeRequiredException', ], [ 'shape' => 'InvalidReplacementTypeException', ], [ 'shape' => 'MultipleConflictResolutionEntriesException', ], [ 'shape' => 'ReplacementContentRequiredException', ], [ 'shape' => 'MaximumConflictResolutionEntriesExceededException', ], [ 'shape' => 'ConcurrentReferenceUpdateException', ], [ 'shape' => 'PathRequiredException', ], [ 'shape' => 'InvalidPathException', ], [ 'shape' => 'InvalidFileModeException', ], [ 'shape' => 'InvalidReplacementContentException', ], [ 'shape' => 'FileContentSizeLimitExceededException', ], [ 'shape' => 'FolderContentSizeLimitExceededException', ], [ 'shape' => 'MaximumFileContentToLoadExceededException', ], [ 'shape' => 'MaximumItemsToCompareExceededException', ], [ 'shape' => 'RepositoryNameRequiredException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'RepositoryDoesNotExistException', ], [ 'shape' => 'RepositoryNotAssociatedWithPullRequestException', ], [ 'shape' => 'PullRequestApprovalRulesNotSatisfiedException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'MergePullRequestByThreeWay' => [ 'name' => 'MergePullRequestByThreeWay', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'MergePullRequestByThreeWayInput', ], 'output' => [ 'shape' => 'MergePullRequestByThreeWayOutput', ], 'errors' => [ [ 'shape' => 'PullRequestAlreadyClosedException', ], [ 'shape' => 'PullRequestDoesNotExistException', ], [ 'shape' => 'PullRequestIdRequiredException', ], [ 'shape' => 'InvalidPullRequestIdException', ], [ 'shape' => 'InvalidCommitIdException', ], [ 'shape' => 'ManualMergeRequiredException', ], [ 'shape' => 'TipOfSourceReferenceIsDifferentException', ], [ 'shape' => 'TipsDivergenceExceededException', ], [ 'shape' => 'NameLengthExceededException', ], [ 'shape' => 'InvalidEmailException', ], [ 'shape' => 'CommitMessageLengthExceededException', ], [ 'shape' => 'InvalidConflictDetailLevelException', ], [ 'shape' => 'InvalidConflictResolutionStrategyException', ], [ 'shape' => 'InvalidConflictResolutionException', ], [ 'shape' => 'ReplacementTypeRequiredException', ], [ 'shape' => 'InvalidReplacementTypeException', ], [ 'shape' => 'MultipleConflictResolutionEntriesException', ], [ 'shape' => 'ReplacementContentRequiredException', ], [ 'shape' => 'MaximumConflictResolutionEntriesExceededException', ], [ 'shape' => 'PathRequiredException', ], [ 'shape' => 'InvalidPathException', ], [ 'shape' => 'InvalidFileModeException', ], [ 'shape' => 'InvalidReplacementContentException', ], [ 'shape' => 'FileContentSizeLimitExceededException', ], [ 'shape' => 'FolderContentSizeLimitExceededException', ], [ 'shape' => 'MaximumFileContentToLoadExceededException', ], [ 'shape' => 'MaximumItemsToCompareExceededException', ], [ 'shape' => 'RepositoryNameRequiredException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'RepositoryDoesNotExistException', ], [ 'shape' => 'RepositoryNotAssociatedWithPullRequestException', ], [ 'shape' => 'ConcurrentReferenceUpdateException', ], [ 'shape' => 'PullRequestApprovalRulesNotSatisfiedException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'OverridePullRequestApprovalRules' => [ 'name' => 'OverridePullRequestApprovalRules', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'OverridePullRequestApprovalRulesInput', ], 'errors' => [ [ 'shape' => 'PullRequestDoesNotExistException', ], [ 'shape' => 'InvalidPullRequestIdException', ], [ 'shape' => 'PullRequestIdRequiredException', ], [ 'shape' => 'InvalidRevisionIdException', ], [ 'shape' => 'RevisionIdRequiredException', ], [ 'shape' => 'InvalidOverrideStatusException', ], [ 'shape' => 'OverrideStatusRequiredException', ], [ 'shape' => 'OverrideAlreadySetException', ], [ 'shape' => 'RevisionNotCurrentException', ], [ 'shape' => 'PullRequestAlreadyClosedException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'PostCommentForComparedCommit' => [ 'name' => 'PostCommentForComparedCommit', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PostCommentForComparedCommitInput', ], 'output' => [ 'shape' => 'PostCommentForComparedCommitOutput', ], 'errors' => [ [ 'shape' => 'RepositoryNameRequiredException', ], [ 'shape' => 'RepositoryDoesNotExistException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'ClientRequestTokenRequiredException', ], [ 'shape' => 'InvalidClientRequestTokenException', ], [ 'shape' => 'IdempotencyParameterMismatchException', ], [ 'shape' => 'CommentContentRequiredException', ], [ 'shape' => 'CommentContentSizeLimitExceededException', ], [ 'shape' => 'InvalidFileLocationException', ], [ 'shape' => 'InvalidRelativeFileVersionEnumException', ], [ 'shape' => 'PathRequiredException', ], [ 'shape' => 'InvalidFilePositionException', ], [ 'shape' => 'CommitIdRequiredException', ], [ 'shape' => 'InvalidCommitIdException', ], [ 'shape' => 'BeforeCommitIdAndAfterCommitIdAreSameException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], [ 'shape' => 'CommitDoesNotExistException', ], [ 'shape' => 'InvalidPathException', ], [ 'shape' => 'PathDoesNotExistException', ], [ 'shape' => 'PathRequiredException', ], ], 'idempotent' => true, ], 'PostCommentForPullRequest' => [ 'name' => 'PostCommentForPullRequest', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PostCommentForPullRequestInput', ], 'output' => [ 'shape' => 'PostCommentForPullRequestOutput', ], 'errors' => [ [ 'shape' => 'PullRequestDoesNotExistException', ], [ 'shape' => 'InvalidPullRequestIdException', ], [ 'shape' => 'PullRequestIdRequiredException', ], [ 'shape' => 'RepositoryNotAssociatedWithPullRequestException', ], [ 'shape' => 'RepositoryNameRequiredException', ], [ 'shape' => 'RepositoryDoesNotExistException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'ClientRequestTokenRequiredException', ], [ 'shape' => 'InvalidClientRequestTokenException', ], [ 'shape' => 'IdempotencyParameterMismatchException', ], [ 'shape' => 'CommentContentRequiredException', ], [ 'shape' => 'CommentContentSizeLimitExceededException', ], [ 'shape' => 'InvalidFileLocationException', ], [ 'shape' => 'InvalidRelativeFileVersionEnumException', ], [ 'shape' => 'PathRequiredException', ], [ 'shape' => 'InvalidFilePositionException', ], [ 'shape' => 'CommitIdRequiredException', ], [ 'shape' => 'InvalidCommitIdException', ], [ 'shape' => 'BeforeCommitIdAndAfterCommitIdAreSameException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], [ 'shape' => 'CommitDoesNotExistException', ], [ 'shape' => 'InvalidPathException', ], [ 'shape' => 'PathDoesNotExistException', ], [ 'shape' => 'PathRequiredException', ], ], 'idempotent' => true, ], 'PostCommentReply' => [ 'name' => 'PostCommentReply', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PostCommentReplyInput', ], 'output' => [ 'shape' => 'PostCommentReplyOutput', ], 'errors' => [ [ 'shape' => 'ClientRequestTokenRequiredException', ], [ 'shape' => 'InvalidClientRequestTokenException', ], [ 'shape' => 'IdempotencyParameterMismatchException', ], [ 'shape' => 'CommentContentRequiredException', ], [ 'shape' => 'CommentContentSizeLimitExceededException', ], [ 'shape' => 'CommentDoesNotExistException', ], [ 'shape' => 'CommentIdRequiredException', ], [ 'shape' => 'InvalidCommentIdException', ], ], 'idempotent' => true, ], 'PutCommentReaction' => [ 'name' => 'PutCommentReaction', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutCommentReactionInput', ], 'errors' => [ [ 'shape' => 'CommentDoesNotExistException', ], [ 'shape' => 'CommentIdRequiredException', ], [ 'shape' => 'InvalidCommentIdException', ], [ 'shape' => 'InvalidReactionValueException', ], [ 'shape' => 'ReactionValueRequiredException', ], [ 'shape' => 'ReactionLimitExceededException', ], [ 'shape' => 'CommentDeletedException', ], ], ], 'PutFile' => [ 'name' => 'PutFile', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutFileInput', ], 'output' => [ 'shape' => 'PutFileOutput', ], 'errors' => [ [ 'shape' => 'RepositoryNameRequiredException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'RepositoryDoesNotExistException', ], [ 'shape' => 'ParentCommitIdRequiredException', ], [ 'shape' => 'InvalidParentCommitIdException', ], [ 'shape' => 'ParentCommitDoesNotExistException', ], [ 'shape' => 'ParentCommitIdOutdatedException', ], [ 'shape' => 'FileContentRequiredException', ], [ 'shape' => 'FileContentSizeLimitExceededException', ], [ 'shape' => 'FolderContentSizeLimitExceededException', ], [ 'shape' => 'PathRequiredException', ], [ 'shape' => 'InvalidPathException', ], [ 'shape' => 'BranchNameRequiredException', ], [ 'shape' => 'InvalidBranchNameException', ], [ 'shape' => 'BranchDoesNotExistException', ], [ 'shape' => 'BranchNameIsTagNameException', ], [ 'shape' => 'InvalidFileModeException', ], [ 'shape' => 'NameLengthExceededException', ], [ 'shape' => 'InvalidEmailException', ], [ 'shape' => 'CommitMessageLengthExceededException', ], [ 'shape' => 'InvalidDeletionParameterException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], [ 'shape' => 'SameFileContentException', ], [ 'shape' => 'FileNameConflictsWithDirectoryNameException', ], [ 'shape' => 'DirectoryNameConflictsWithFileNameException', ], [ 'shape' => 'FilePathConflictsWithSubmodulePathException', ], ], ], 'PutRepositoryTriggers' => [ 'name' => 'PutRepositoryTriggers', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutRepositoryTriggersInput', ], 'output' => [ 'shape' => 'PutRepositoryTriggersOutput', ], 'errors' => [ [ 'shape' => 'RepositoryDoesNotExistException', ], [ 'shape' => 'RepositoryNameRequiredException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'RepositoryTriggersListRequiredException', ], [ 'shape' => 'MaximumRepositoryTriggersExceededException', ], [ 'shape' => 'InvalidRepositoryTriggerNameException', ], [ 'shape' => 'InvalidRepositoryTriggerDestinationArnException', ], [ 'shape' => 'InvalidRepositoryTriggerRegionException', ], [ 'shape' => 'InvalidRepositoryTriggerCustomDataException', ], [ 'shape' => 'MaximumBranchesExceededException', ], [ 'shape' => 'InvalidRepositoryTriggerBranchNameException', ], [ 'shape' => 'InvalidRepositoryTriggerEventsException', ], [ 'shape' => 'RepositoryTriggerNameRequiredException', ], [ 'shape' => 'RepositoryTriggerDestinationArnRequiredException', ], [ 'shape' => 'RepositoryTriggerBranchNameListRequiredException', ], [ 'shape' => 'RepositoryTriggerEventsListRequiredException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TagResourceInput', ], 'errors' => [ [ 'shape' => 'RepositoryDoesNotExistException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'ResourceArnRequiredException', ], [ 'shape' => 'InvalidResourceArnException', ], [ 'shape' => 'TagsMapRequiredException', ], [ 'shape' => 'InvalidTagsMapException', ], [ 'shape' => 'TooManyTagsException', ], [ 'shape' => 'InvalidSystemTagUsageException', ], [ 'shape' => 'TagPolicyException', ], ], ], 'TestRepositoryTriggers' => [ 'name' => 'TestRepositoryTriggers', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TestRepositoryTriggersInput', ], 'output' => [ 'shape' => 'TestRepositoryTriggersOutput', ], 'errors' => [ [ 'shape' => 'RepositoryDoesNotExistException', ], [ 'shape' => 'RepositoryNameRequiredException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'RepositoryTriggersListRequiredException', ], [ 'shape' => 'MaximumRepositoryTriggersExceededException', ], [ 'shape' => 'InvalidRepositoryTriggerNameException', ], [ 'shape' => 'InvalidRepositoryTriggerDestinationArnException', ], [ 'shape' => 'InvalidRepositoryTriggerRegionException', ], [ 'shape' => 'InvalidRepositoryTriggerCustomDataException', ], [ 'shape' => 'MaximumBranchesExceededException', ], [ 'shape' => 'InvalidRepositoryTriggerBranchNameException', ], [ 'shape' => 'InvalidRepositoryTriggerEventsException', ], [ 'shape' => 'RepositoryTriggerNameRequiredException', ], [ 'shape' => 'RepositoryTriggerDestinationArnRequiredException', ], [ 'shape' => 'RepositoryTriggerBranchNameListRequiredException', ], [ 'shape' => 'RepositoryTriggerEventsListRequiredException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UntagResourceInput', ], 'errors' => [ [ 'shape' => 'RepositoryDoesNotExistException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'ResourceArnRequiredException', ], [ 'shape' => 'InvalidResourceArnException', ], [ 'shape' => 'TagKeysListRequiredException', ], [ 'shape' => 'InvalidTagKeysListException', ], [ 'shape' => 'TooManyTagsException', ], [ 'shape' => 'InvalidSystemTagUsageException', ], [ 'shape' => 'TagPolicyException', ], ], ], 'UpdateApprovalRuleTemplateContent' => [ 'name' => 'UpdateApprovalRuleTemplateContent', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateApprovalRuleTemplateContentInput', ], 'output' => [ 'shape' => 'UpdateApprovalRuleTemplateContentOutput', ], 'errors' => [ [ 'shape' => 'InvalidApprovalRuleTemplateNameException', ], [ 'shape' => 'ApprovalRuleTemplateNameRequiredException', ], [ 'shape' => 'ApprovalRuleTemplateDoesNotExistException', ], [ 'shape' => 'InvalidApprovalRuleTemplateContentException', ], [ 'shape' => 'InvalidRuleContentSha256Exception', ], [ 'shape' => 'ApprovalRuleTemplateContentRequiredException', ], ], ], 'UpdateApprovalRuleTemplateDescription' => [ 'name' => 'UpdateApprovalRuleTemplateDescription', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateApprovalRuleTemplateDescriptionInput', ], 'output' => [ 'shape' => 'UpdateApprovalRuleTemplateDescriptionOutput', ], 'errors' => [ [ 'shape' => 'InvalidApprovalRuleTemplateNameException', ], [ 'shape' => 'ApprovalRuleTemplateNameRequiredException', ], [ 'shape' => 'ApprovalRuleTemplateDoesNotExistException', ], [ 'shape' => 'InvalidApprovalRuleTemplateDescriptionException', ], ], ], 'UpdateApprovalRuleTemplateName' => [ 'name' => 'UpdateApprovalRuleTemplateName', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateApprovalRuleTemplateNameInput', ], 'output' => [ 'shape' => 'UpdateApprovalRuleTemplateNameOutput', ], 'errors' => [ [ 'shape' => 'InvalidApprovalRuleTemplateNameException', ], [ 'shape' => 'ApprovalRuleTemplateNameRequiredException', ], [ 'shape' => 'ApprovalRuleTemplateDoesNotExistException', ], [ 'shape' => 'ApprovalRuleTemplateNameAlreadyExistsException', ], ], ], 'UpdateComment' => [ 'name' => 'UpdateComment', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateCommentInput', ], 'output' => [ 'shape' => 'UpdateCommentOutput', ], 'errors' => [ [ 'shape' => 'CommentContentRequiredException', ], [ 'shape' => 'CommentContentSizeLimitExceededException', ], [ 'shape' => 'CommentDoesNotExistException', ], [ 'shape' => 'CommentIdRequiredException', ], [ 'shape' => 'InvalidCommentIdException', ], [ 'shape' => 'CommentNotCreatedByCallerException', ], [ 'shape' => 'CommentDeletedException', ], ], ], 'UpdateDefaultBranch' => [ 'name' => 'UpdateDefaultBranch', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateDefaultBranchInput', ], 'errors' => [ [ 'shape' => 'RepositoryNameRequiredException', ], [ 'shape' => 'RepositoryDoesNotExistException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'BranchNameRequiredException', ], [ 'shape' => 'InvalidBranchNameException', ], [ 'shape' => 'BranchDoesNotExistException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'UpdatePullRequestApprovalRuleContent' => [ 'name' => 'UpdatePullRequestApprovalRuleContent', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdatePullRequestApprovalRuleContentInput', ], 'output' => [ 'shape' => 'UpdatePullRequestApprovalRuleContentOutput', ], 'errors' => [ [ 'shape' => 'PullRequestDoesNotExistException', ], [ 'shape' => 'InvalidPullRequestIdException', ], [ 'shape' => 'PullRequestIdRequiredException', ], [ 'shape' => 'PullRequestAlreadyClosedException', ], [ 'shape' => 'ApprovalRuleNameRequiredException', ], [ 'shape' => 'InvalidApprovalRuleNameException', ], [ 'shape' => 'ApprovalRuleDoesNotExistException', ], [ 'shape' => 'InvalidRuleContentSha256Exception', ], [ 'shape' => 'ApprovalRuleContentRequiredException', ], [ 'shape' => 'InvalidApprovalRuleContentException', ], [ 'shape' => 'CannotModifyApprovalRuleFromTemplateException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'UpdatePullRequestApprovalState' => [ 'name' => 'UpdatePullRequestApprovalState', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdatePullRequestApprovalStateInput', ], 'errors' => [ [ 'shape' => 'PullRequestDoesNotExistException', ], [ 'shape' => 'InvalidPullRequestIdException', ], [ 'shape' => 'PullRequestIdRequiredException', ], [ 'shape' => 'InvalidRevisionIdException', ], [ 'shape' => 'RevisionIdRequiredException', ], [ 'shape' => 'InvalidApprovalStateException', ], [ 'shape' => 'ApprovalStateRequiredException', ], [ 'shape' => 'PullRequestCannotBeApprovedByAuthorException', ], [ 'shape' => 'RevisionNotCurrentException', ], [ 'shape' => 'PullRequestAlreadyClosedException', ], [ 'shape' => 'MaximumNumberOfApprovalsExceededException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'UpdatePullRequestDescription' => [ 'name' => 'UpdatePullRequestDescription', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdatePullRequestDescriptionInput', ], 'output' => [ 'shape' => 'UpdatePullRequestDescriptionOutput', ], 'errors' => [ [ 'shape' => 'PullRequestDoesNotExistException', ], [ 'shape' => 'InvalidPullRequestIdException', ], [ 'shape' => 'PullRequestIdRequiredException', ], [ 'shape' => 'InvalidDescriptionException', ], [ 'shape' => 'PullRequestAlreadyClosedException', ], ], ], 'UpdatePullRequestStatus' => [ 'name' => 'UpdatePullRequestStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdatePullRequestStatusInput', ], 'output' => [ 'shape' => 'UpdatePullRequestStatusOutput', ], 'errors' => [ [ 'shape' => 'PullRequestDoesNotExistException', ], [ 'shape' => 'InvalidPullRequestIdException', ], [ 'shape' => 'PullRequestIdRequiredException', ], [ 'shape' => 'InvalidPullRequestStatusUpdateException', ], [ 'shape' => 'InvalidPullRequestStatusException', ], [ 'shape' => 'PullRequestStatusRequiredException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'UpdatePullRequestTitle' => [ 'name' => 'UpdatePullRequestTitle', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdatePullRequestTitleInput', ], 'output' => [ 'shape' => 'UpdatePullRequestTitleOutput', ], 'errors' => [ [ 'shape' => 'PullRequestDoesNotExistException', ], [ 'shape' => 'InvalidPullRequestIdException', ], [ 'shape' => 'PullRequestIdRequiredException', ], [ 'shape' => 'TitleRequiredException', ], [ 'shape' => 'InvalidTitleException', ], [ 'shape' => 'PullRequestAlreadyClosedException', ], ], ], 'UpdateRepositoryDescription' => [ 'name' => 'UpdateRepositoryDescription', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateRepositoryDescriptionInput', ], 'errors' => [ [ 'shape' => 'RepositoryNameRequiredException', ], [ 'shape' => 'RepositoryDoesNotExistException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'InvalidRepositoryDescriptionException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'UpdateRepositoryEncryptionKey' => [ 'name' => 'UpdateRepositoryEncryptionKey', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateRepositoryEncryptionKeyInput', ], 'output' => [ 'shape' => 'UpdateRepositoryEncryptionKeyOutput', ], 'errors' => [ [ 'shape' => 'RepositoryNameRequiredException', ], [ 'shape' => 'RepositoryDoesNotExistException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'EncryptionKeyRequiredException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyInvalidIdException', ], [ 'shape' => 'EncryptionKeyInvalidUsageException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'UpdateRepositoryName' => [ 'name' => 'UpdateRepositoryName', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateRepositoryNameInput', ], 'errors' => [ [ 'shape' => 'RepositoryDoesNotExistException', ], [ 'shape' => 'RepositoryNameExistsException', ], [ 'shape' => 'RepositoryNameRequiredException', ], [ 'shape' => 'InvalidRepositoryNameException', ], ], ], ], 'shapes' => [ 'AccountId' => [ 'type' => 'string', ], 'ActorDoesNotExistException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'AdditionalData' => [ 'type' => 'string', ], 'Approval' => [ 'type' => 'structure', 'members' => [ 'userArn' => [ 'shape' => 'Arn', ], 'approvalState' => [ 'shape' => 'ApprovalState', ], ], ], 'ApprovalList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Approval', ], ], 'ApprovalRule' => [ 'type' => 'structure', 'members' => [ 'approvalRuleId' => [ 'shape' => 'ApprovalRuleId', ], 'approvalRuleName' => [ 'shape' => 'ApprovalRuleName', ], 'approvalRuleContent' => [ 'shape' => 'ApprovalRuleContent', ], 'ruleContentSha256' => [ 'shape' => 'RuleContentSha256', ], 'lastModifiedDate' => [ 'shape' => 'LastModifiedDate', ], 'creationDate' => [ 'shape' => 'CreationDate', ], 'lastModifiedUser' => [ 'shape' => 'Arn', ], 'originApprovalRuleTemplate' => [ 'shape' => 'OriginApprovalRuleTemplate', ], ], ], 'ApprovalRuleContent' => [ 'type' => 'string', 'max' => 3000, 'min' => 1, ], 'ApprovalRuleContentRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ApprovalRuleDoesNotExistException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ApprovalRuleEventMetadata' => [ 'type' => 'structure', 'members' => [ 'approvalRuleName' => [ 'shape' => 'ApprovalRuleName', ], 'approvalRuleId' => [ 'shape' => 'ApprovalRuleId', ], 'approvalRuleContent' => [ 'shape' => 'ApprovalRuleContent', ], ], ], 'ApprovalRuleId' => [ 'type' => 'string', ], 'ApprovalRuleName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'ApprovalRuleNameAlreadyExistsException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ApprovalRuleNameRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ApprovalRuleOverriddenEventMetadata' => [ 'type' => 'structure', 'members' => [ 'revisionId' => [ 'shape' => 'RevisionId', ], 'overrideStatus' => [ 'shape' => 'OverrideStatus', ], ], ], 'ApprovalRuleTemplate' => [ 'type' => 'structure', 'members' => [ 'approvalRuleTemplateId' => [ 'shape' => 'ApprovalRuleTemplateId', ], 'approvalRuleTemplateName' => [ 'shape' => 'ApprovalRuleTemplateName', ], 'approvalRuleTemplateDescription' => [ 'shape' => 'ApprovalRuleTemplateDescription', ], 'approvalRuleTemplateContent' => [ 'shape' => 'ApprovalRuleTemplateContent', ], 'ruleContentSha256' => [ 'shape' => 'RuleContentSha256', ], 'lastModifiedDate' => [ 'shape' => 'LastModifiedDate', ], 'creationDate' => [ 'shape' => 'CreationDate', ], 'lastModifiedUser' => [ 'shape' => 'Arn', ], ], ], 'ApprovalRuleTemplateContent' => [ 'type' => 'string', 'max' => 3000, 'min' => 1, ], 'ApprovalRuleTemplateContentRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ApprovalRuleTemplateDescription' => [ 'type' => 'string', 'max' => 1000, 'min' => 0, ], 'ApprovalRuleTemplateDoesNotExistException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ApprovalRuleTemplateId' => [ 'type' => 'string', ], 'ApprovalRuleTemplateInUseException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ApprovalRuleTemplateName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'ApprovalRuleTemplateNameAlreadyExistsException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ApprovalRuleTemplateNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ApprovalRuleTemplateName', ], ], 'ApprovalRuleTemplateNameRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ApprovalRulesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ApprovalRule', ], ], 'ApprovalRulesNotSatisfiedList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ApprovalRuleName', ], ], 'ApprovalRulesSatisfiedList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ApprovalRuleName', ], ], 'ApprovalState' => [ 'type' => 'string', 'enum' => [ 'APPROVE', 'REVOKE', ], ], 'ApprovalStateChangedEventMetadata' => [ 'type' => 'structure', 'members' => [ 'revisionId' => [ 'shape' => 'RevisionId', ], 'approvalStatus' => [ 'shape' => 'ApprovalState', ], ], ], 'ApprovalStateRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'Approved' => [ 'type' => 'boolean', ], 'Arn' => [ 'type' => 'string', ], 'AssociateApprovalRuleTemplateWithRepositoryInput' => [ 'type' => 'structure', 'required' => [ 'approvalRuleTemplateName', 'repositoryName', ], 'members' => [ 'approvalRuleTemplateName' => [ 'shape' => 'ApprovalRuleTemplateName', ], 'repositoryName' => [ 'shape' => 'RepositoryName', ], ], ], 'AuthorDoesNotExistException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'BatchAssociateApprovalRuleTemplateWithRepositoriesError' => [ 'type' => 'structure', 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'errorCode' => [ 'shape' => 'ErrorCode', ], 'errorMessage' => [ 'shape' => 'ErrorMessage', ], ], ], 'BatchAssociateApprovalRuleTemplateWithRepositoriesErrorsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchAssociateApprovalRuleTemplateWithRepositoriesError', ], ], 'BatchAssociateApprovalRuleTemplateWithRepositoriesInput' => [ 'type' => 'structure', 'required' => [ 'approvalRuleTemplateName', 'repositoryNames', ], 'members' => [ 'approvalRuleTemplateName' => [ 'shape' => 'ApprovalRuleTemplateName', ], 'repositoryNames' => [ 'shape' => 'RepositoryNameList', ], ], ], 'BatchAssociateApprovalRuleTemplateWithRepositoriesOutput' => [ 'type' => 'structure', 'required' => [ 'associatedRepositoryNames', 'errors', ], 'members' => [ 'associatedRepositoryNames' => [ 'shape' => 'RepositoryNameList', ], 'errors' => [ 'shape' => 'BatchAssociateApprovalRuleTemplateWithRepositoriesErrorsList', ], ], ], 'BatchDescribeMergeConflictsError' => [ 'type' => 'structure', 'required' => [ 'filePath', 'exceptionName', 'message', ], 'members' => [ 'filePath' => [ 'shape' => 'Path', ], 'exceptionName' => [ 'shape' => 'ExceptionName', ], 'message' => [ 'shape' => 'Message', ], ], ], 'BatchDescribeMergeConflictsErrors' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchDescribeMergeConflictsError', ], ], 'BatchDescribeMergeConflictsInput' => [ 'type' => 'structure', 'required' => [ 'repositoryName', 'destinationCommitSpecifier', 'sourceCommitSpecifier', 'mergeOption', ], 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'destinationCommitSpecifier' => [ 'shape' => 'CommitName', ], 'sourceCommitSpecifier' => [ 'shape' => 'CommitName', ], 'mergeOption' => [ 'shape' => 'MergeOptionTypeEnum', ], 'maxMergeHunks' => [ 'shape' => 'MaxResults', ], 'maxConflictFiles' => [ 'shape' => 'MaxResults', ], 'filePaths' => [ 'shape' => 'FilePaths', ], 'conflictDetailLevel' => [ 'shape' => 'ConflictDetailLevelTypeEnum', ], 'conflictResolutionStrategy' => [ 'shape' => 'ConflictResolutionStrategyTypeEnum', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'BatchDescribeMergeConflictsOutput' => [ 'type' => 'structure', 'required' => [ 'conflicts', 'destinationCommitId', 'sourceCommitId', ], 'members' => [ 'conflicts' => [ 'shape' => 'Conflicts', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'errors' => [ 'shape' => 'BatchDescribeMergeConflictsErrors', ], 'destinationCommitId' => [ 'shape' => 'ObjectId', ], 'sourceCommitId' => [ 'shape' => 'ObjectId', ], 'baseCommitId' => [ 'shape' => 'ObjectId', ], ], ], 'BatchDisassociateApprovalRuleTemplateFromRepositoriesError' => [ 'type' => 'structure', 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'errorCode' => [ 'shape' => 'ErrorCode', ], 'errorMessage' => [ 'shape' => 'ErrorMessage', ], ], ], 'BatchDisassociateApprovalRuleTemplateFromRepositoriesErrorsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchDisassociateApprovalRuleTemplateFromRepositoriesError', ], ], 'BatchDisassociateApprovalRuleTemplateFromRepositoriesInput' => [ 'type' => 'structure', 'required' => [ 'approvalRuleTemplateName', 'repositoryNames', ], 'members' => [ 'approvalRuleTemplateName' => [ 'shape' => 'ApprovalRuleTemplateName', ], 'repositoryNames' => [ 'shape' => 'RepositoryNameList', ], ], ], 'BatchDisassociateApprovalRuleTemplateFromRepositoriesOutput' => [ 'type' => 'structure', 'required' => [ 'disassociatedRepositoryNames', 'errors', ], 'members' => [ 'disassociatedRepositoryNames' => [ 'shape' => 'RepositoryNameList', ], 'errors' => [ 'shape' => 'BatchDisassociateApprovalRuleTemplateFromRepositoriesErrorsList', ], ], ], 'BatchGetCommitsError' => [ 'type' => 'structure', 'members' => [ 'commitId' => [ 'shape' => 'ObjectId', ], 'errorCode' => [ 'shape' => 'ErrorCode', ], 'errorMessage' => [ 'shape' => 'ErrorMessage', ], ], ], 'BatchGetCommitsErrorsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchGetCommitsError', ], ], 'BatchGetCommitsInput' => [ 'type' => 'structure', 'required' => [ 'commitIds', 'repositoryName', ], 'members' => [ 'commitIds' => [ 'shape' => 'CommitIdsInputList', ], 'repositoryName' => [ 'shape' => 'RepositoryName', ], ], ], 'BatchGetCommitsOutput' => [ 'type' => 'structure', 'members' => [ 'commits' => [ 'shape' => 'CommitObjectsList', ], 'errors' => [ 'shape' => 'BatchGetCommitsErrorsList', ], ], ], 'BatchGetRepositoriesError' => [ 'type' => 'structure', 'members' => [ 'repositoryId' => [ 'shape' => 'RepositoryId', ], 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'errorCode' => [ 'shape' => 'BatchGetRepositoriesErrorCodeEnum', ], 'errorMessage' => [ 'shape' => 'ErrorMessage', ], ], ], 'BatchGetRepositoriesErrorCodeEnum' => [ 'type' => 'string', 'enum' => [ 'EncryptionIntegrityChecksFailedException', 'EncryptionKeyAccessDeniedException', 'EncryptionKeyDisabledException', 'EncryptionKeyNotFoundException', 'EncryptionKeyUnavailableException', 'RepositoryDoesNotExistException', ], ], 'BatchGetRepositoriesErrorsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchGetRepositoriesError', ], ], 'BatchGetRepositoriesInput' => [ 'type' => 'structure', 'required' => [ 'repositoryNames', ], 'members' => [ 'repositoryNames' => [ 'shape' => 'RepositoryNameList', ], ], ], 'BatchGetRepositoriesOutput' => [ 'type' => 'structure', 'members' => [ 'repositories' => [ 'shape' => 'RepositoryMetadataList', ], 'repositoriesNotFound' => [ 'shape' => 'RepositoryNotFoundList', ], 'errors' => [ 'shape' => 'BatchGetRepositoriesErrorsList', ], ], ], 'BeforeCommitIdAndAfterCommitIdAreSameException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'BlobIdDoesNotExistException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'BlobIdRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'BlobMetadata' => [ 'type' => 'structure', 'members' => [ 'blobId' => [ 'shape' => 'ObjectId', ], 'path' => [ 'shape' => 'Path', ], 'mode' => [ 'shape' => 'Mode', ], ], ], 'BranchDoesNotExistException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'BranchInfo' => [ 'type' => 'structure', 'members' => [ 'branchName' => [ 'shape' => 'BranchName', ], 'commitId' => [ 'shape' => 'CommitId', ], ], ], 'BranchName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'BranchNameExistsException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'BranchNameIsTagNameException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'BranchNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BranchName', ], ], 'BranchNameRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'CallerReactions' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReactionValue', ], ], 'CannotDeleteApprovalRuleFromTemplateException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'CannotModifyApprovalRuleFromTemplateException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'CapitalBoolean' => [ 'type' => 'boolean', ], 'ChangeTypeEnum' => [ 'type' => 'string', 'enum' => [ 'A', 'M', 'D', ], ], 'ClientRequestToken' => [ 'type' => 'string', ], 'ClientRequestTokenRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'CloneUrlHttp' => [ 'type' => 'string', ], 'CloneUrlSsh' => [ 'type' => 'string', ], 'Comment' => [ 'type' => 'structure', 'members' => [ 'commentId' => [ 'shape' => 'CommentId', ], 'content' => [ 'shape' => 'Content', ], 'inReplyTo' => [ 'shape' => 'CommentId', ], 'creationDate' => [ 'shape' => 'CreationDate', ], 'lastModifiedDate' => [ 'shape' => 'LastModifiedDate', ], 'authorArn' => [ 'shape' => 'Arn', ], 'deleted' => [ 'shape' => 'IsCommentDeleted', ], 'clientRequestToken' => [ 'shape' => 'ClientRequestToken', ], 'callerReactions' => [ 'shape' => 'CallerReactions', ], 'reactionCounts' => [ 'shape' => 'ReactionCountsMap', ], ], ], 'CommentContentRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'CommentContentSizeLimitExceededException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'CommentDeletedException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'CommentDoesNotExistException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'CommentId' => [ 'type' => 'string', ], 'CommentIdRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'CommentNotCreatedByCallerException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'Comments' => [ 'type' => 'list', 'member' => [ 'shape' => 'Comment', ], ], 'CommentsForComparedCommit' => [ 'type' => 'structure', 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'beforeCommitId' => [ 'shape' => 'CommitId', ], 'afterCommitId' => [ 'shape' => 'CommitId', ], 'beforeBlobId' => [ 'shape' => 'ObjectId', ], 'afterBlobId' => [ 'shape' => 'ObjectId', ], 'location' => [ 'shape' => 'Location', ], 'comments' => [ 'shape' => 'Comments', ], ], ], 'CommentsForComparedCommitData' => [ 'type' => 'list', 'member' => [ 'shape' => 'CommentsForComparedCommit', ], ], 'CommentsForPullRequest' => [ 'type' => 'structure', 'members' => [ 'pullRequestId' => [ 'shape' => 'PullRequestId', ], 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'beforeCommitId' => [ 'shape' => 'CommitId', ], 'afterCommitId' => [ 'shape' => 'CommitId', ], 'beforeBlobId' => [ 'shape' => 'ObjectId', ], 'afterBlobId' => [ 'shape' => 'ObjectId', ], 'location' => [ 'shape' => 'Location', ], 'comments' => [ 'shape' => 'Comments', ], ], ], 'CommentsForPullRequestData' => [ 'type' => 'list', 'member' => [ 'shape' => 'CommentsForPullRequest', ], ], 'Commit' => [ 'type' => 'structure', 'members' => [ 'commitId' => [ 'shape' => 'ObjectId', ], 'treeId' => [ 'shape' => 'ObjectId', ], 'parents' => [ 'shape' => 'ParentList', ], 'message' => [ 'shape' => 'Message', ], 'author' => [ 'shape' => 'UserInfo', ], 'committer' => [ 'shape' => 'UserInfo', ], 'additionalData' => [ 'shape' => 'AdditionalData', ], ], ], 'CommitDoesNotExistException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'CommitId' => [ 'type' => 'string', ], 'CommitIdDoesNotExistException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'CommitIdRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'CommitIdsInputList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ObjectId', ], ], 'CommitIdsLimitExceededException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'CommitIdsListRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'CommitMessageLengthExceededException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'CommitName' => [ 'type' => 'string', ], 'CommitObjectsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Commit', ], ], 'CommitRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ConcurrentReferenceUpdateException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'Conflict' => [ 'type' => 'structure', 'members' => [ 'conflictMetadata' => [ 'shape' => 'ConflictMetadata', ], 'mergeHunks' => [ 'shape' => 'MergeHunks', ], ], ], 'ConflictDetailLevelTypeEnum' => [ 'type' => 'string', 'enum' => [ 'FILE_LEVEL', 'LINE_LEVEL', ], ], 'ConflictMetadata' => [ 'type' => 'structure', 'members' => [ 'filePath' => [ 'shape' => 'Path', ], 'fileSizes' => [ 'shape' => 'FileSizes', ], 'fileModes' => [ 'shape' => 'FileModes', ], 'objectTypes' => [ 'shape' => 'ObjectTypes', ], 'numberOfConflicts' => [ 'shape' => 'NumberOfConflicts', ], 'isBinaryFile' => [ 'shape' => 'IsBinaryFile', ], 'contentConflict' => [ 'shape' => 'IsContentConflict', ], 'fileModeConflict' => [ 'shape' => 'IsFileModeConflict', ], 'objectTypeConflict' => [ 'shape' => 'IsObjectTypeConflict', ], 'mergeOperations' => [ 'shape' => 'MergeOperations', ], ], ], 'ConflictMetadataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConflictMetadata', ], ], 'ConflictResolution' => [ 'type' => 'structure', 'members' => [ 'replaceContents' => [ 'shape' => 'ReplaceContentEntries', ], 'deleteFiles' => [ 'shape' => 'DeleteFileEntries', ], 'setFileModes' => [ 'shape' => 'SetFileModeEntries', ], ], ], 'ConflictResolutionStrategyTypeEnum' => [ 'type' => 'string', 'enum' => [ 'NONE', 'ACCEPT_SOURCE', 'ACCEPT_DESTINATION', 'AUTOMERGE', ], ], 'Conflicts' => [ 'type' => 'list', 'member' => [ 'shape' => 'Conflict', ], ], 'Content' => [ 'type' => 'string', ], 'Count' => [ 'type' => 'integer', ], 'CreateApprovalRuleTemplateInput' => [ 'type' => 'structure', 'required' => [ 'approvalRuleTemplateName', 'approvalRuleTemplateContent', ], 'members' => [ 'approvalRuleTemplateName' => [ 'shape' => 'ApprovalRuleTemplateName', ], 'approvalRuleTemplateContent' => [ 'shape' => 'ApprovalRuleTemplateContent', ], 'approvalRuleTemplateDescription' => [ 'shape' => 'ApprovalRuleTemplateDescription', ], ], ], 'CreateApprovalRuleTemplateOutput' => [ 'type' => 'structure', 'required' => [ 'approvalRuleTemplate', ], 'members' => [ 'approvalRuleTemplate' => [ 'shape' => 'ApprovalRuleTemplate', ], ], ], 'CreateBranchInput' => [ 'type' => 'structure', 'required' => [ 'repositoryName', 'branchName', 'commitId', ], 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'branchName' => [ 'shape' => 'BranchName', ], 'commitId' => [ 'shape' => 'CommitId', ], ], ], 'CreateCommitInput' => [ 'type' => 'structure', 'required' => [ 'repositoryName', 'branchName', ], 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'branchName' => [ 'shape' => 'BranchName', ], 'parentCommitId' => [ 'shape' => 'CommitId', ], 'authorName' => [ 'shape' => 'Name', ], 'email' => [ 'shape' => 'Email', ], 'commitMessage' => [ 'shape' => 'Message', ], 'keepEmptyFolders' => [ 'shape' => 'KeepEmptyFolders', ], 'putFiles' => [ 'shape' => 'PutFileEntries', ], 'deleteFiles' => [ 'shape' => 'DeleteFileEntries', ], 'setFileModes' => [ 'shape' => 'SetFileModeEntries', ], ], ], 'CreateCommitOutput' => [ 'type' => 'structure', 'members' => [ 'commitId' => [ 'shape' => 'ObjectId', ], 'treeId' => [ 'shape' => 'ObjectId', ], 'filesAdded' => [ 'shape' => 'FilesMetadata', ], 'filesUpdated' => [ 'shape' => 'FilesMetadata', ], 'filesDeleted' => [ 'shape' => 'FilesMetadata', ], ], ], 'CreatePullRequestApprovalRuleInput' => [ 'type' => 'structure', 'required' => [ 'pullRequestId', 'approvalRuleName', 'approvalRuleContent', ], 'members' => [ 'pullRequestId' => [ 'shape' => 'PullRequestId', ], 'approvalRuleName' => [ 'shape' => 'ApprovalRuleName', ], 'approvalRuleContent' => [ 'shape' => 'ApprovalRuleContent', ], ], ], 'CreatePullRequestApprovalRuleOutput' => [ 'type' => 'structure', 'required' => [ 'approvalRule', ], 'members' => [ 'approvalRule' => [ 'shape' => 'ApprovalRule', ], ], ], 'CreatePullRequestInput' => [ 'type' => 'structure', 'required' => [ 'title', 'targets', ], 'members' => [ 'title' => [ 'shape' => 'Title', ], 'description' => [ 'shape' => 'Description', ], 'targets' => [ 'shape' => 'TargetList', ], 'clientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], ], ], 'CreatePullRequestOutput' => [ 'type' => 'structure', 'required' => [ 'pullRequest', ], 'members' => [ 'pullRequest' => [ 'shape' => 'PullRequest', ], ], ], 'CreateRepositoryInput' => [ 'type' => 'structure', 'required' => [ 'repositoryName', ], 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'repositoryDescription' => [ 'shape' => 'RepositoryDescription', ], 'tags' => [ 'shape' => 'TagsMap', ], 'kmsKeyId' => [ 'shape' => 'KmsKeyId', ], ], ], 'CreateRepositoryOutput' => [ 'type' => 'structure', 'members' => [ 'repositoryMetadata' => [ 'shape' => 'RepositoryMetadata', ], ], ], 'CreateUnreferencedMergeCommitInput' => [ 'type' => 'structure', 'required' => [ 'repositoryName', 'sourceCommitSpecifier', 'destinationCommitSpecifier', 'mergeOption', ], 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'sourceCommitSpecifier' => [ 'shape' => 'CommitName', ], 'destinationCommitSpecifier' => [ 'shape' => 'CommitName', ], 'mergeOption' => [ 'shape' => 'MergeOptionTypeEnum', ], 'conflictDetailLevel' => [ 'shape' => 'ConflictDetailLevelTypeEnum', ], 'conflictResolutionStrategy' => [ 'shape' => 'ConflictResolutionStrategyTypeEnum', ], 'authorName' => [ 'shape' => 'Name', ], 'email' => [ 'shape' => 'Email', ], 'commitMessage' => [ 'shape' => 'Message', ], 'keepEmptyFolders' => [ 'shape' => 'KeepEmptyFolders', ], 'conflictResolution' => [ 'shape' => 'ConflictResolution', ], ], ], 'CreateUnreferencedMergeCommitOutput' => [ 'type' => 'structure', 'members' => [ 'commitId' => [ 'shape' => 'ObjectId', ], 'treeId' => [ 'shape' => 'ObjectId', ], ], ], 'CreationDate' => [ 'type' => 'timestamp', ], 'Date' => [ 'type' => 'string', ], 'DefaultBranchCannotBeDeletedException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'DeleteApprovalRuleTemplateInput' => [ 'type' => 'structure', 'required' => [ 'approvalRuleTemplateName', ], 'members' => [ 'approvalRuleTemplateName' => [ 'shape' => 'ApprovalRuleTemplateName', ], ], ], 'DeleteApprovalRuleTemplateOutput' => [ 'type' => 'structure', 'required' => [ 'approvalRuleTemplateId', ], 'members' => [ 'approvalRuleTemplateId' => [ 'shape' => 'ApprovalRuleTemplateId', ], ], ], 'DeleteBranchInput' => [ 'type' => 'structure', 'required' => [ 'repositoryName', 'branchName', ], 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'branchName' => [ 'shape' => 'BranchName', ], ], ], 'DeleteBranchOutput' => [ 'type' => 'structure', 'members' => [ 'deletedBranch' => [ 'shape' => 'BranchInfo', ], ], ], 'DeleteCommentContentInput' => [ 'type' => 'structure', 'required' => [ 'commentId', ], 'members' => [ 'commentId' => [ 'shape' => 'CommentId', ], ], ], 'DeleteCommentContentOutput' => [ 'type' => 'structure', 'members' => [ 'comment' => [ 'shape' => 'Comment', ], ], ], 'DeleteFileEntries' => [ 'type' => 'list', 'member' => [ 'shape' => 'DeleteFileEntry', ], ], 'DeleteFileEntry' => [ 'type' => 'structure', 'required' => [ 'filePath', ], 'members' => [ 'filePath' => [ 'shape' => 'Path', ], ], ], 'DeleteFileInput' => [ 'type' => 'structure', 'required' => [ 'repositoryName', 'branchName', 'filePath', 'parentCommitId', ], 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'branchName' => [ 'shape' => 'BranchName', ], 'filePath' => [ 'shape' => 'Path', ], 'parentCommitId' => [ 'shape' => 'CommitId', ], 'keepEmptyFolders' => [ 'shape' => 'KeepEmptyFolders', ], 'commitMessage' => [ 'shape' => 'Message', ], 'name' => [ 'shape' => 'Name', ], 'email' => [ 'shape' => 'Email', ], ], ], 'DeleteFileOutput' => [ 'type' => 'structure', 'required' => [ 'commitId', 'blobId', 'treeId', 'filePath', ], 'members' => [ 'commitId' => [ 'shape' => 'ObjectId', ], 'blobId' => [ 'shape' => 'ObjectId', ], 'treeId' => [ 'shape' => 'ObjectId', ], 'filePath' => [ 'shape' => 'Path', ], ], ], 'DeletePullRequestApprovalRuleInput' => [ 'type' => 'structure', 'required' => [ 'pullRequestId', 'approvalRuleName', ], 'members' => [ 'pullRequestId' => [ 'shape' => 'PullRequestId', ], 'approvalRuleName' => [ 'shape' => 'ApprovalRuleName', ], ], ], 'DeletePullRequestApprovalRuleOutput' => [ 'type' => 'structure', 'required' => [ 'approvalRuleId', ], 'members' => [ 'approvalRuleId' => [ 'shape' => 'ApprovalRuleId', ], ], ], 'DeleteRepositoryInput' => [ 'type' => 'structure', 'required' => [ 'repositoryName', ], 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], ], ], 'DeleteRepositoryOutput' => [ 'type' => 'structure', 'members' => [ 'repositoryId' => [ 'shape' => 'RepositoryId', ], ], ], 'DescribeMergeConflictsInput' => [ 'type' => 'structure', 'required' => [ 'repositoryName', 'destinationCommitSpecifier', 'sourceCommitSpecifier', 'mergeOption', 'filePath', ], 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'destinationCommitSpecifier' => [ 'shape' => 'CommitName', ], 'sourceCommitSpecifier' => [ 'shape' => 'CommitName', ], 'mergeOption' => [ 'shape' => 'MergeOptionTypeEnum', ], 'maxMergeHunks' => [ 'shape' => 'MaxResults', ], 'filePath' => [ 'shape' => 'Path', ], 'conflictDetailLevel' => [ 'shape' => 'ConflictDetailLevelTypeEnum', ], 'conflictResolutionStrategy' => [ 'shape' => 'ConflictResolutionStrategyTypeEnum', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeMergeConflictsOutput' => [ 'type' => 'structure', 'required' => [ 'conflictMetadata', 'mergeHunks', 'destinationCommitId', 'sourceCommitId', ], 'members' => [ 'conflictMetadata' => [ 'shape' => 'ConflictMetadata', ], 'mergeHunks' => [ 'shape' => 'MergeHunks', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'destinationCommitId' => [ 'shape' => 'ObjectId', ], 'sourceCommitId' => [ 'shape' => 'ObjectId', ], 'baseCommitId' => [ 'shape' => 'ObjectId', ], ], ], 'DescribePullRequestEventsInput' => [ 'type' => 'structure', 'required' => [ 'pullRequestId', ], 'members' => [ 'pullRequestId' => [ 'shape' => 'PullRequestId', ], 'pullRequestEventType' => [ 'shape' => 'PullRequestEventType', ], 'actorArn' => [ 'shape' => 'Arn', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'DescribePullRequestEventsOutput' => [ 'type' => 'structure', 'required' => [ 'pullRequestEvents', ], 'members' => [ 'pullRequestEvents' => [ 'shape' => 'PullRequestEventList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'Description' => [ 'type' => 'string', 'max' => 10240, ], 'Difference' => [ 'type' => 'structure', 'members' => [ 'beforeBlob' => [ 'shape' => 'BlobMetadata', ], 'afterBlob' => [ 'shape' => 'BlobMetadata', ], 'changeType' => [ 'shape' => 'ChangeTypeEnum', ], ], ], 'DifferenceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Difference', ], ], 'DirectoryNameConflictsWithFileNameException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'DisassociateApprovalRuleTemplateFromRepositoryInput' => [ 'type' => 'structure', 'required' => [ 'approvalRuleTemplateName', 'repositoryName', ], 'members' => [ 'approvalRuleTemplateName' => [ 'shape' => 'ApprovalRuleTemplateName', ], 'repositoryName' => [ 'shape' => 'RepositoryName', ], ], ], 'Email' => [ 'type' => 'string', ], 'EncryptionIntegrityChecksFailedException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, 'fault' => true, ], 'EncryptionKeyAccessDeniedException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'EncryptionKeyDisabledException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'EncryptionKeyInvalidIdException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'EncryptionKeyInvalidUsageException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'EncryptionKeyNotFoundException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'EncryptionKeyRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'EncryptionKeyUnavailableException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ErrorCode' => [ 'type' => 'string', ], 'ErrorMessage' => [ 'type' => 'string', ], 'EvaluatePullRequestApprovalRulesInput' => [ 'type' => 'structure', 'required' => [ 'pullRequestId', 'revisionId', ], 'members' => [ 'pullRequestId' => [ 'shape' => 'PullRequestId', ], 'revisionId' => [ 'shape' => 'RevisionId', ], ], ], 'EvaluatePullRequestApprovalRulesOutput' => [ 'type' => 'structure', 'required' => [ 'evaluation', ], 'members' => [ 'evaluation' => [ 'shape' => 'Evaluation', ], ], ], 'Evaluation' => [ 'type' => 'structure', 'members' => [ 'approved' => [ 'shape' => 'Approved', ], 'overridden' => [ 'shape' => 'Overridden', ], 'approvalRulesSatisfied' => [ 'shape' => 'ApprovalRulesSatisfiedList', ], 'approvalRulesNotSatisfied' => [ 'shape' => 'ApprovalRulesNotSatisfiedList', ], ], ], 'EventDate' => [ 'type' => 'timestamp', ], 'ExceptionName' => [ 'type' => 'string', ], 'File' => [ 'type' => 'structure', 'members' => [ 'blobId' => [ 'shape' => 'ObjectId', ], 'absolutePath' => [ 'shape' => 'Path', ], 'relativePath' => [ 'shape' => 'Path', ], 'fileMode' => [ 'shape' => 'FileModeTypeEnum', ], ], ], 'FileContent' => [ 'type' => 'blob', 'max' => 6291456, ], 'FileContentAndSourceFileSpecifiedException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'FileContentRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'FileContentSizeLimitExceededException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'FileDoesNotExistException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'FileEntryRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'FileList' => [ 'type' => 'list', 'member' => [ 'shape' => 'File', ], ], 'FileMetadata' => [ 'type' => 'structure', 'members' => [ 'absolutePath' => [ 'shape' => 'Path', ], 'blobId' => [ 'shape' => 'ObjectId', ], 'fileMode' => [ 'shape' => 'FileModeTypeEnum', ], ], ], 'FileModeRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'FileModeTypeEnum' => [ 'type' => 'string', 'enum' => [ 'EXECUTABLE', 'NORMAL', 'SYMLINK', ], ], 'FileModes' => [ 'type' => 'structure', 'members' => [ 'source' => [ 'shape' => 'FileModeTypeEnum', ], 'destination' => [ 'shape' => 'FileModeTypeEnum', ], 'base' => [ 'shape' => 'FileModeTypeEnum', ], ], ], 'FileNameConflictsWithDirectoryNameException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'FilePathConflictsWithSubmodulePathException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'FilePaths' => [ 'type' => 'list', 'member' => [ 'shape' => 'Path', ], ], 'FileSize' => [ 'type' => 'long', ], 'FileSizes' => [ 'type' => 'structure', 'members' => [ 'source' => [ 'shape' => 'FileSize', ], 'destination' => [ 'shape' => 'FileSize', ], 'base' => [ 'shape' => 'FileSize', ], ], ], 'FileTooLargeException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'FileVersion' => [ 'type' => 'structure', 'members' => [ 'commit' => [ 'shape' => 'Commit', ], 'blobId' => [ 'shape' => 'ObjectId', ], 'path' => [ 'shape' => 'Path', ], 'revisionChildren' => [ 'shape' => 'RevisionChildren', ], ], ], 'FilesMetadata' => [ 'type' => 'list', 'member' => [ 'shape' => 'FileMetadata', ], ], 'Folder' => [ 'type' => 'structure', 'members' => [ 'treeId' => [ 'shape' => 'ObjectId', ], 'absolutePath' => [ 'shape' => 'Path', ], 'relativePath' => [ 'shape' => 'Path', ], ], ], 'FolderContentSizeLimitExceededException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'FolderDoesNotExistException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'FolderList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Folder', ], ], 'GetApprovalRuleTemplateInput' => [ 'type' => 'structure', 'required' => [ 'approvalRuleTemplateName', ], 'members' => [ 'approvalRuleTemplateName' => [ 'shape' => 'ApprovalRuleTemplateName', ], ], ], 'GetApprovalRuleTemplateOutput' => [ 'type' => 'structure', 'required' => [ 'approvalRuleTemplate', ], 'members' => [ 'approvalRuleTemplate' => [ 'shape' => 'ApprovalRuleTemplate', ], ], ], 'GetBlobInput' => [ 'type' => 'structure', 'required' => [ 'repositoryName', 'blobId', ], 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'blobId' => [ 'shape' => 'ObjectId', ], ], ], 'GetBlobOutput' => [ 'type' => 'structure', 'required' => [ 'content', ], 'members' => [ 'content' => [ 'shape' => 'blob', ], ], ], 'GetBranchInput' => [ 'type' => 'structure', 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'branchName' => [ 'shape' => 'BranchName', ], ], ], 'GetBranchOutput' => [ 'type' => 'structure', 'members' => [ 'branch' => [ 'shape' => 'BranchInfo', ], ], ], 'GetCommentInput' => [ 'type' => 'structure', 'required' => [ 'commentId', ], 'members' => [ 'commentId' => [ 'shape' => 'CommentId', ], ], ], 'GetCommentOutput' => [ 'type' => 'structure', 'members' => [ 'comment' => [ 'shape' => 'Comment', ], ], ], 'GetCommentReactionsInput' => [ 'type' => 'structure', 'required' => [ 'commentId', ], 'members' => [ 'commentId' => [ 'shape' => 'CommentId', ], 'reactionUserArn' => [ 'shape' => 'Arn', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'GetCommentReactionsOutput' => [ 'type' => 'structure', 'required' => [ 'reactionsForComment', ], 'members' => [ 'reactionsForComment' => [ 'shape' => 'ReactionsForCommentList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetCommentsForComparedCommitInput' => [ 'type' => 'structure', 'required' => [ 'repositoryName', 'afterCommitId', ], 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'beforeCommitId' => [ 'shape' => 'CommitId', ], 'afterCommitId' => [ 'shape' => 'CommitId', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'GetCommentsForComparedCommitOutput' => [ 'type' => 'structure', 'members' => [ 'commentsForComparedCommitData' => [ 'shape' => 'CommentsForComparedCommitData', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetCommentsForPullRequestInput' => [ 'type' => 'structure', 'required' => [ 'pullRequestId', ], 'members' => [ 'pullRequestId' => [ 'shape' => 'PullRequestId', ], 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'beforeCommitId' => [ 'shape' => 'CommitId', ], 'afterCommitId' => [ 'shape' => 'CommitId', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'GetCommentsForPullRequestOutput' => [ 'type' => 'structure', 'members' => [ 'commentsForPullRequestData' => [ 'shape' => 'CommentsForPullRequestData', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetCommitInput' => [ 'type' => 'structure', 'required' => [ 'repositoryName', 'commitId', ], 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'commitId' => [ 'shape' => 'ObjectId', ], ], ], 'GetCommitOutput' => [ 'type' => 'structure', 'required' => [ 'commit', ], 'members' => [ 'commit' => [ 'shape' => 'Commit', ], ], ], 'GetDifferencesInput' => [ 'type' => 'structure', 'required' => [ 'repositoryName', 'afterCommitSpecifier', ], 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'beforeCommitSpecifier' => [ 'shape' => 'CommitName', ], 'afterCommitSpecifier' => [ 'shape' => 'CommitName', ], 'beforePath' => [ 'shape' => 'Path', ], 'afterPath' => [ 'shape' => 'Path', ], 'MaxResults' => [ 'shape' => 'Limit', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetDifferencesOutput' => [ 'type' => 'structure', 'members' => [ 'differences' => [ 'shape' => 'DifferenceList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetFileInput' => [ 'type' => 'structure', 'required' => [ 'repositoryName', 'filePath', ], 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'commitSpecifier' => [ 'shape' => 'CommitName', ], 'filePath' => [ 'shape' => 'Path', ], ], ], 'GetFileOutput' => [ 'type' => 'structure', 'required' => [ 'commitId', 'blobId', 'filePath', 'fileMode', 'fileSize', 'fileContent', ], 'members' => [ 'commitId' => [ 'shape' => 'ObjectId', ], 'blobId' => [ 'shape' => 'ObjectId', ], 'filePath' => [ 'shape' => 'Path', ], 'fileMode' => [ 'shape' => 'FileModeTypeEnum', ], 'fileSize' => [ 'shape' => 'ObjectSize', ], 'fileContent' => [ 'shape' => 'FileContent', ], ], ], 'GetFolderInput' => [ 'type' => 'structure', 'required' => [ 'repositoryName', 'folderPath', ], 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'commitSpecifier' => [ 'shape' => 'CommitName', ], 'folderPath' => [ 'shape' => 'Path', ], ], ], 'GetFolderOutput' => [ 'type' => 'structure', 'required' => [ 'commitId', 'folderPath', ], 'members' => [ 'commitId' => [ 'shape' => 'ObjectId', ], 'folderPath' => [ 'shape' => 'Path', ], 'treeId' => [ 'shape' => 'ObjectId', ], 'subFolders' => [ 'shape' => 'FolderList', ], 'files' => [ 'shape' => 'FileList', ], 'symbolicLinks' => [ 'shape' => 'SymbolicLinkList', ], 'subModules' => [ 'shape' => 'SubModuleList', ], ], ], 'GetMergeCommitInput' => [ 'type' => 'structure', 'required' => [ 'repositoryName', 'sourceCommitSpecifier', 'destinationCommitSpecifier', ], 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'sourceCommitSpecifier' => [ 'shape' => 'CommitName', ], 'destinationCommitSpecifier' => [ 'shape' => 'CommitName', ], 'conflictDetailLevel' => [ 'shape' => 'ConflictDetailLevelTypeEnum', ], 'conflictResolutionStrategy' => [ 'shape' => 'ConflictResolutionStrategyTypeEnum', ], ], ], 'GetMergeCommitOutput' => [ 'type' => 'structure', 'members' => [ 'sourceCommitId' => [ 'shape' => 'ObjectId', ], 'destinationCommitId' => [ 'shape' => 'ObjectId', ], 'baseCommitId' => [ 'shape' => 'ObjectId', ], 'mergedCommitId' => [ 'shape' => 'ObjectId', ], ], ], 'GetMergeConflictsInput' => [ 'type' => 'structure', 'required' => [ 'repositoryName', 'destinationCommitSpecifier', 'sourceCommitSpecifier', 'mergeOption', ], 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'destinationCommitSpecifier' => [ 'shape' => 'CommitName', ], 'sourceCommitSpecifier' => [ 'shape' => 'CommitName', ], 'mergeOption' => [ 'shape' => 'MergeOptionTypeEnum', ], 'conflictDetailLevel' => [ 'shape' => 'ConflictDetailLevelTypeEnum', ], 'maxConflictFiles' => [ 'shape' => 'MaxResults', ], 'conflictResolutionStrategy' => [ 'shape' => 'ConflictResolutionStrategyTypeEnum', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetMergeConflictsOutput' => [ 'type' => 'structure', 'required' => [ 'mergeable', 'destinationCommitId', 'sourceCommitId', 'conflictMetadataList', ], 'members' => [ 'mergeable' => [ 'shape' => 'IsMergeable', ], 'destinationCommitId' => [ 'shape' => 'ObjectId', ], 'sourceCommitId' => [ 'shape' => 'ObjectId', ], 'baseCommitId' => [ 'shape' => 'ObjectId', ], 'conflictMetadataList' => [ 'shape' => 'ConflictMetadataList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetMergeOptionsInput' => [ 'type' => 'structure', 'required' => [ 'repositoryName', 'sourceCommitSpecifier', 'destinationCommitSpecifier', ], 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'sourceCommitSpecifier' => [ 'shape' => 'CommitName', ], 'destinationCommitSpecifier' => [ 'shape' => 'CommitName', ], 'conflictDetailLevel' => [ 'shape' => 'ConflictDetailLevelTypeEnum', ], 'conflictResolutionStrategy' => [ 'shape' => 'ConflictResolutionStrategyTypeEnum', ], ], ], 'GetMergeOptionsOutput' => [ 'type' => 'structure', 'required' => [ 'mergeOptions', 'sourceCommitId', 'destinationCommitId', 'baseCommitId', ], 'members' => [ 'mergeOptions' => [ 'shape' => 'MergeOptions', ], 'sourceCommitId' => [ 'shape' => 'ObjectId', ], 'destinationCommitId' => [ 'shape' => 'ObjectId', ], 'baseCommitId' => [ 'shape' => 'ObjectId', ], ], ], 'GetPullRequestApprovalStatesInput' => [ 'type' => 'structure', 'required' => [ 'pullRequestId', 'revisionId', ], 'members' => [ 'pullRequestId' => [ 'shape' => 'PullRequestId', ], 'revisionId' => [ 'shape' => 'RevisionId', ], ], ], 'GetPullRequestApprovalStatesOutput' => [ 'type' => 'structure', 'members' => [ 'approvals' => [ 'shape' => 'ApprovalList', ], ], ], 'GetPullRequestInput' => [ 'type' => 'structure', 'required' => [ 'pullRequestId', ], 'members' => [ 'pullRequestId' => [ 'shape' => 'PullRequestId', ], ], ], 'GetPullRequestOutput' => [ 'type' => 'structure', 'required' => [ 'pullRequest', ], 'members' => [ 'pullRequest' => [ 'shape' => 'PullRequest', ], ], ], 'GetPullRequestOverrideStateInput' => [ 'type' => 'structure', 'required' => [ 'pullRequestId', 'revisionId', ], 'members' => [ 'pullRequestId' => [ 'shape' => 'PullRequestId', ], 'revisionId' => [ 'shape' => 'RevisionId', ], ], ], 'GetPullRequestOverrideStateOutput' => [ 'type' => 'structure', 'members' => [ 'overridden' => [ 'shape' => 'Overridden', ], 'overrider' => [ 'shape' => 'Arn', ], ], ], 'GetRepositoryInput' => [ 'type' => 'structure', 'required' => [ 'repositoryName', ], 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], ], ], 'GetRepositoryOutput' => [ 'type' => 'structure', 'members' => [ 'repositoryMetadata' => [ 'shape' => 'RepositoryMetadata', ], ], ], 'GetRepositoryTriggersInput' => [ 'type' => 'structure', 'required' => [ 'repositoryName', ], 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], ], ], 'GetRepositoryTriggersOutput' => [ 'type' => 'structure', 'members' => [ 'configurationId' => [ 'shape' => 'RepositoryTriggersConfigurationId', ], 'triggers' => [ 'shape' => 'RepositoryTriggersList', ], ], ], 'HunkContent' => [ 'type' => 'string', ], 'IdempotencyParameterMismatchException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidActorArnException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidApprovalRuleContentException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidApprovalRuleNameException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidApprovalRuleTemplateContentException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidApprovalRuleTemplateDescriptionException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidApprovalRuleTemplateNameException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidApprovalStateException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidAuthorArnException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidBlobIdException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidBranchNameException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidClientRequestTokenException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidCommentIdException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidCommitException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidCommitIdException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidConflictDetailLevelException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidConflictResolutionException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidConflictResolutionStrategyException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidContinuationTokenException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidDeletionParameterException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidDescriptionException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidDestinationCommitSpecifierException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidEmailException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidFileLocationException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidFileModeException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidFilePositionException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidMaxConflictFilesException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidMaxMergeHunksException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidMaxResultsException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidMergeOptionException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidOrderException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidOverrideStatusException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidParentCommitIdException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidPathException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidPullRequestEventTypeException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidPullRequestIdException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidPullRequestStatusException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidPullRequestStatusUpdateException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidReactionUserArnException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidReactionValueException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidReferenceNameException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidRelativeFileVersionEnumException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidReplacementContentException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidReplacementTypeException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidRepositoryDescriptionException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidRepositoryNameException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidRepositoryTriggerBranchNameException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidRepositoryTriggerCustomDataException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidRepositoryTriggerDestinationArnException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidRepositoryTriggerEventsException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidRepositoryTriggerNameException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidRepositoryTriggerRegionException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidResourceArnException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidRevisionIdException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidRuleContentSha256Exception' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidSortByException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidSourceCommitSpecifierException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidSystemTagUsageException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidTagKeysListException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidTagsMapException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidTargetBranchException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidTargetException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidTargetsException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidTitleException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'IsBinaryFile' => [ 'type' => 'structure', 'members' => [ 'source' => [ 'shape' => 'CapitalBoolean', ], 'destination' => [ 'shape' => 'CapitalBoolean', ], 'base' => [ 'shape' => 'CapitalBoolean', ], ], ], 'IsCommentDeleted' => [ 'type' => 'boolean', ], 'IsContentConflict' => [ 'type' => 'boolean', ], 'IsFileModeConflict' => [ 'type' => 'boolean', ], 'IsHunkConflict' => [ 'type' => 'boolean', ], 'IsMergeable' => [ 'type' => 'boolean', ], 'IsMerged' => [ 'type' => 'boolean', ], 'IsMove' => [ 'type' => 'boolean', ], 'IsObjectTypeConflict' => [ 'type' => 'boolean', ], 'KeepEmptyFolders' => [ 'type' => 'boolean', ], 'KmsKeyId' => [ 'type' => 'string', 'pattern' => '^[a-zA-Z0-9:/_-]+$', ], 'LastModifiedDate' => [ 'type' => 'timestamp', ], 'Limit' => [ 'type' => 'integer', 'box' => true, ], 'LineNumber' => [ 'type' => 'integer', ], 'ListApprovalRuleTemplatesInput' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListApprovalRuleTemplatesOutput' => [ 'type' => 'structure', 'members' => [ 'approvalRuleTemplateNames' => [ 'shape' => 'ApprovalRuleTemplateNameList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAssociatedApprovalRuleTemplatesForRepositoryInput' => [ 'type' => 'structure', 'required' => [ 'repositoryName', ], 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListAssociatedApprovalRuleTemplatesForRepositoryOutput' => [ 'type' => 'structure', 'members' => [ 'approvalRuleTemplateNames' => [ 'shape' => 'ApprovalRuleTemplateNameList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListBranchesInput' => [ 'type' => 'structure', 'required' => [ 'repositoryName', ], 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListBranchesOutput' => [ 'type' => 'structure', 'members' => [ 'branches' => [ 'shape' => 'BranchNameList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListFileCommitHistoryRequest' => [ 'type' => 'structure', 'required' => [ 'repositoryName', 'filePath', ], 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'commitSpecifier' => [ 'shape' => 'CommitName', ], 'filePath' => [ 'shape' => 'Path', ], 'maxResults' => [ 'shape' => 'Limit', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListFileCommitHistoryResponse' => [ 'type' => 'structure', 'required' => [ 'revisionDag', ], 'members' => [ 'revisionDag' => [ 'shape' => 'RevisionDag', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListPullRequestsInput' => [ 'type' => 'structure', 'required' => [ 'repositoryName', ], 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'authorArn' => [ 'shape' => 'Arn', ], 'pullRequestStatus' => [ 'shape' => 'PullRequestStatusEnum', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListPullRequestsOutput' => [ 'type' => 'structure', 'required' => [ 'pullRequestIds', ], 'members' => [ 'pullRequestIds' => [ 'shape' => 'PullRequestIdList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListRepositoriesForApprovalRuleTemplateInput' => [ 'type' => 'structure', 'required' => [ 'approvalRuleTemplateName', ], 'members' => [ 'approvalRuleTemplateName' => [ 'shape' => 'ApprovalRuleTemplateName', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListRepositoriesForApprovalRuleTemplateOutput' => [ 'type' => 'structure', 'members' => [ 'repositoryNames' => [ 'shape' => 'RepositoryNameList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListRepositoriesInput' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'sortBy' => [ 'shape' => 'SortByEnum', ], 'order' => [ 'shape' => 'OrderEnum', ], ], ], 'ListRepositoriesOutput' => [ 'type' => 'structure', 'members' => [ 'repositories' => [ 'shape' => 'RepositoryNameIdPairList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagsForResourceInput' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ResourceArn', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagsForResourceOutput' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'TagsMap', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'Location' => [ 'type' => 'structure', 'members' => [ 'filePath' => [ 'shape' => 'Path', ], 'filePosition' => [ 'shape' => 'Position', ], 'relativeFileVersion' => [ 'shape' => 'RelativeFileVersionEnum', ], ], ], 'ManualMergeRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'MaxResults' => [ 'type' => 'integer', ], 'MaximumBranchesExceededException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'MaximumConflictResolutionEntriesExceededException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'MaximumFileContentToLoadExceededException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'MaximumFileEntriesExceededException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'MaximumItemsToCompareExceededException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'MaximumNumberOfApprovalsExceededException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'MaximumOpenPullRequestsExceededException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'MaximumRepositoryNamesExceededException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'MaximumRepositoryTriggersExceededException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'MaximumRuleTemplatesAssociatedWithRepositoryException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'MergeBranchesByFastForwardInput' => [ 'type' => 'structure', 'required' => [ 'repositoryName', 'sourceCommitSpecifier', 'destinationCommitSpecifier', ], 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'sourceCommitSpecifier' => [ 'shape' => 'CommitName', ], 'destinationCommitSpecifier' => [ 'shape' => 'CommitName', ], 'targetBranch' => [ 'shape' => 'BranchName', ], ], ], 'MergeBranchesByFastForwardOutput' => [ 'type' => 'structure', 'members' => [ 'commitId' => [ 'shape' => 'ObjectId', ], 'treeId' => [ 'shape' => 'ObjectId', ], ], ], 'MergeBranchesBySquashInput' => [ 'type' => 'structure', 'required' => [ 'repositoryName', 'sourceCommitSpecifier', 'destinationCommitSpecifier', ], 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'sourceCommitSpecifier' => [ 'shape' => 'CommitName', ], 'destinationCommitSpecifier' => [ 'shape' => 'CommitName', ], 'targetBranch' => [ 'shape' => 'BranchName', ], 'conflictDetailLevel' => [ 'shape' => 'ConflictDetailLevelTypeEnum', ], 'conflictResolutionStrategy' => [ 'shape' => 'ConflictResolutionStrategyTypeEnum', ], 'authorName' => [ 'shape' => 'Name', ], 'email' => [ 'shape' => 'Email', ], 'commitMessage' => [ 'shape' => 'Message', ], 'keepEmptyFolders' => [ 'shape' => 'KeepEmptyFolders', ], 'conflictResolution' => [ 'shape' => 'ConflictResolution', ], ], ], 'MergeBranchesBySquashOutput' => [ 'type' => 'structure', 'members' => [ 'commitId' => [ 'shape' => 'ObjectId', ], 'treeId' => [ 'shape' => 'ObjectId', ], ], ], 'MergeBranchesByThreeWayInput' => [ 'type' => 'structure', 'required' => [ 'repositoryName', 'sourceCommitSpecifier', 'destinationCommitSpecifier', ], 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'sourceCommitSpecifier' => [ 'shape' => 'CommitName', ], 'destinationCommitSpecifier' => [ 'shape' => 'CommitName', ], 'targetBranch' => [ 'shape' => 'BranchName', ], 'conflictDetailLevel' => [ 'shape' => 'ConflictDetailLevelTypeEnum', ], 'conflictResolutionStrategy' => [ 'shape' => 'ConflictResolutionStrategyTypeEnum', ], 'authorName' => [ 'shape' => 'Name', ], 'email' => [ 'shape' => 'Email', ], 'commitMessage' => [ 'shape' => 'Message', ], 'keepEmptyFolders' => [ 'shape' => 'KeepEmptyFolders', ], 'conflictResolution' => [ 'shape' => 'ConflictResolution', ], ], ], 'MergeBranchesByThreeWayOutput' => [ 'type' => 'structure', 'members' => [ 'commitId' => [ 'shape' => 'ObjectId', ], 'treeId' => [ 'shape' => 'ObjectId', ], ], ], 'MergeHunk' => [ 'type' => 'structure', 'members' => [ 'isConflict' => [ 'shape' => 'IsHunkConflict', ], 'source' => [ 'shape' => 'MergeHunkDetail', ], 'destination' => [ 'shape' => 'MergeHunkDetail', ], 'base' => [ 'shape' => 'MergeHunkDetail', ], ], ], 'MergeHunkDetail' => [ 'type' => 'structure', 'members' => [ 'startLine' => [ 'shape' => 'LineNumber', ], 'endLine' => [ 'shape' => 'LineNumber', ], 'hunkContent' => [ 'shape' => 'HunkContent', ], ], ], 'MergeHunks' => [ 'type' => 'list', 'member' => [ 'shape' => 'MergeHunk', ], ], 'MergeMetadata' => [ 'type' => 'structure', 'members' => [ 'isMerged' => [ 'shape' => 'IsMerged', ], 'mergedBy' => [ 'shape' => 'Arn', ], 'mergeCommitId' => [ 'shape' => 'CommitId', ], 'mergeOption' => [ 'shape' => 'MergeOptionTypeEnum', ], ], ], 'MergeOperations' => [ 'type' => 'structure', 'members' => [ 'source' => [ 'shape' => 'ChangeTypeEnum', ], 'destination' => [ 'shape' => 'ChangeTypeEnum', ], ], ], 'MergeOptionRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'MergeOptionTypeEnum' => [ 'type' => 'string', 'enum' => [ 'FAST_FORWARD_MERGE', 'SQUASH_MERGE', 'THREE_WAY_MERGE', ], ], 'MergeOptions' => [ 'type' => 'list', 'member' => [ 'shape' => 'MergeOptionTypeEnum', ], ], 'MergePullRequestByFastForwardInput' => [ 'type' => 'structure', 'required' => [ 'pullRequestId', 'repositoryName', ], 'members' => [ 'pullRequestId' => [ 'shape' => 'PullRequestId', ], 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'sourceCommitId' => [ 'shape' => 'ObjectId', ], ], ], 'MergePullRequestByFastForwardOutput' => [ 'type' => 'structure', 'members' => [ 'pullRequest' => [ 'shape' => 'PullRequest', ], ], ], 'MergePullRequestBySquashInput' => [ 'type' => 'structure', 'required' => [ 'pullRequestId', 'repositoryName', ], 'members' => [ 'pullRequestId' => [ 'shape' => 'PullRequestId', ], 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'sourceCommitId' => [ 'shape' => 'ObjectId', ], 'conflictDetailLevel' => [ 'shape' => 'ConflictDetailLevelTypeEnum', ], 'conflictResolutionStrategy' => [ 'shape' => 'ConflictResolutionStrategyTypeEnum', ], 'commitMessage' => [ 'shape' => 'Message', ], 'authorName' => [ 'shape' => 'Name', ], 'email' => [ 'shape' => 'Email', ], 'keepEmptyFolders' => [ 'shape' => 'KeepEmptyFolders', ], 'conflictResolution' => [ 'shape' => 'ConflictResolution', ], ], ], 'MergePullRequestBySquashOutput' => [ 'type' => 'structure', 'members' => [ 'pullRequest' => [ 'shape' => 'PullRequest', ], ], ], 'MergePullRequestByThreeWayInput' => [ 'type' => 'structure', 'required' => [ 'pullRequestId', 'repositoryName', ], 'members' => [ 'pullRequestId' => [ 'shape' => 'PullRequestId', ], 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'sourceCommitId' => [ 'shape' => 'ObjectId', ], 'conflictDetailLevel' => [ 'shape' => 'ConflictDetailLevelTypeEnum', ], 'conflictResolutionStrategy' => [ 'shape' => 'ConflictResolutionStrategyTypeEnum', ], 'commitMessage' => [ 'shape' => 'Message', ], 'authorName' => [ 'shape' => 'Name', ], 'email' => [ 'shape' => 'Email', ], 'keepEmptyFolders' => [ 'shape' => 'KeepEmptyFolders', ], 'conflictResolution' => [ 'shape' => 'ConflictResolution', ], ], ], 'MergePullRequestByThreeWayOutput' => [ 'type' => 'structure', 'members' => [ 'pullRequest' => [ 'shape' => 'PullRequest', ], ], ], 'Message' => [ 'type' => 'string', ], 'Mode' => [ 'type' => 'string', ], 'MultipleConflictResolutionEntriesException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'MultipleRepositoriesInPullRequestException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'Name' => [ 'type' => 'string', ], 'NameLengthExceededException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'NextToken' => [ 'type' => 'string', ], 'NoChangeException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'NumberOfConflicts' => [ 'type' => 'integer', ], 'NumberOfRuleTemplatesExceededException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'NumberOfRulesExceededException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ObjectId' => [ 'type' => 'string', ], 'ObjectSize' => [ 'type' => 'long', ], 'ObjectTypeEnum' => [ 'type' => 'string', 'enum' => [ 'FILE', 'DIRECTORY', 'GIT_LINK', 'SYMBOLIC_LINK', ], ], 'ObjectTypes' => [ 'type' => 'structure', 'members' => [ 'source' => [ 'shape' => 'ObjectTypeEnum', ], 'destination' => [ 'shape' => 'ObjectTypeEnum', ], 'base' => [ 'shape' => 'ObjectTypeEnum', ], ], ], 'OperationNotAllowedException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'OrderEnum' => [ 'type' => 'string', 'enum' => [ 'ascending', 'descending', ], ], 'OriginApprovalRuleTemplate' => [ 'type' => 'structure', 'members' => [ 'approvalRuleTemplateId' => [ 'shape' => 'ApprovalRuleTemplateId', ], 'approvalRuleTemplateName' => [ 'shape' => 'ApprovalRuleTemplateName', ], ], ], 'Overridden' => [ 'type' => 'boolean', ], 'OverrideAlreadySetException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'OverridePullRequestApprovalRulesInput' => [ 'type' => 'structure', 'required' => [ 'pullRequestId', 'revisionId', 'overrideStatus', ], 'members' => [ 'pullRequestId' => [ 'shape' => 'PullRequestId', ], 'revisionId' => [ 'shape' => 'RevisionId', ], 'overrideStatus' => [ 'shape' => 'OverrideStatus', ], ], ], 'OverrideStatus' => [ 'type' => 'string', 'enum' => [ 'OVERRIDE', 'REVOKE', ], ], 'OverrideStatusRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ParentCommitDoesNotExistException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ParentCommitIdOutdatedException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ParentCommitIdRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ParentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ObjectId', ], ], 'Path' => [ 'type' => 'string', ], 'PathDoesNotExistException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'PathRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'Position' => [ 'type' => 'long', ], 'PostCommentForComparedCommitInput' => [ 'type' => 'structure', 'required' => [ 'repositoryName', 'afterCommitId', 'content', ], 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'beforeCommitId' => [ 'shape' => 'CommitId', ], 'afterCommitId' => [ 'shape' => 'CommitId', ], 'location' => [ 'shape' => 'Location', ], 'content' => [ 'shape' => 'Content', ], 'clientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], ], ], 'PostCommentForComparedCommitOutput' => [ 'type' => 'structure', 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'beforeCommitId' => [ 'shape' => 'CommitId', ], 'afterCommitId' => [ 'shape' => 'CommitId', ], 'beforeBlobId' => [ 'shape' => 'ObjectId', ], 'afterBlobId' => [ 'shape' => 'ObjectId', ], 'location' => [ 'shape' => 'Location', ], 'comment' => [ 'shape' => 'Comment', ], ], ], 'PostCommentForPullRequestInput' => [ 'type' => 'structure', 'required' => [ 'pullRequestId', 'repositoryName', 'beforeCommitId', 'afterCommitId', 'content', ], 'members' => [ 'pullRequestId' => [ 'shape' => 'PullRequestId', ], 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'beforeCommitId' => [ 'shape' => 'CommitId', ], 'afterCommitId' => [ 'shape' => 'CommitId', ], 'location' => [ 'shape' => 'Location', ], 'content' => [ 'shape' => 'Content', ], 'clientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], ], ], 'PostCommentForPullRequestOutput' => [ 'type' => 'structure', 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'pullRequestId' => [ 'shape' => 'PullRequestId', ], 'beforeCommitId' => [ 'shape' => 'CommitId', ], 'afterCommitId' => [ 'shape' => 'CommitId', ], 'beforeBlobId' => [ 'shape' => 'ObjectId', ], 'afterBlobId' => [ 'shape' => 'ObjectId', ], 'location' => [ 'shape' => 'Location', ], 'comment' => [ 'shape' => 'Comment', ], ], ], 'PostCommentReplyInput' => [ 'type' => 'structure', 'required' => [ 'inReplyTo', 'content', ], 'members' => [ 'inReplyTo' => [ 'shape' => 'CommentId', ], 'clientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'content' => [ 'shape' => 'Content', ], ], ], 'PostCommentReplyOutput' => [ 'type' => 'structure', 'members' => [ 'comment' => [ 'shape' => 'Comment', ], ], ], 'PullRequest' => [ 'type' => 'structure', 'members' => [ 'pullRequestId' => [ 'shape' => 'PullRequestId', ], 'title' => [ 'shape' => 'Title', ], 'description' => [ 'shape' => 'Description', ], 'lastActivityDate' => [ 'shape' => 'LastModifiedDate', ], 'creationDate' => [ 'shape' => 'CreationDate', ], 'pullRequestStatus' => [ 'shape' => 'PullRequestStatusEnum', ], 'authorArn' => [ 'shape' => 'Arn', ], 'pullRequestTargets' => [ 'shape' => 'PullRequestTargetList', ], 'clientRequestToken' => [ 'shape' => 'ClientRequestToken', ], 'revisionId' => [ 'shape' => 'RevisionId', ], 'approvalRules' => [ 'shape' => 'ApprovalRulesList', ], ], ], 'PullRequestAlreadyClosedException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'PullRequestApprovalRulesNotSatisfiedException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'PullRequestCannotBeApprovedByAuthorException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'PullRequestCreatedEventMetadata' => [ 'type' => 'structure', 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'sourceCommitId' => [ 'shape' => 'CommitId', ], 'destinationCommitId' => [ 'shape' => 'CommitId', ], 'mergeBase' => [ 'shape' => 'CommitId', ], ], ], 'PullRequestDoesNotExistException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'PullRequestEvent' => [ 'type' => 'structure', 'members' => [ 'pullRequestId' => [ 'shape' => 'PullRequestId', ], 'eventDate' => [ 'shape' => 'EventDate', ], 'pullRequestEventType' => [ 'shape' => 'PullRequestEventType', ], 'actorArn' => [ 'shape' => 'Arn', ], 'pullRequestCreatedEventMetadata' => [ 'shape' => 'PullRequestCreatedEventMetadata', ], 'pullRequestStatusChangedEventMetadata' => [ 'shape' => 'PullRequestStatusChangedEventMetadata', ], 'pullRequestSourceReferenceUpdatedEventMetadata' => [ 'shape' => 'PullRequestSourceReferenceUpdatedEventMetadata', ], 'pullRequestMergedStateChangedEventMetadata' => [ 'shape' => 'PullRequestMergedStateChangedEventMetadata', ], 'approvalRuleEventMetadata' => [ 'shape' => 'ApprovalRuleEventMetadata', ], 'approvalStateChangedEventMetadata' => [ 'shape' => 'ApprovalStateChangedEventMetadata', ], 'approvalRuleOverriddenEventMetadata' => [ 'shape' => 'ApprovalRuleOverriddenEventMetadata', ], ], ], 'PullRequestEventList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PullRequestEvent', ], ], 'PullRequestEventType' => [ 'type' => 'string', 'enum' => [ 'PULL_REQUEST_CREATED', 'PULL_REQUEST_STATUS_CHANGED', 'PULL_REQUEST_SOURCE_REFERENCE_UPDATED', 'PULL_REQUEST_MERGE_STATE_CHANGED', 'PULL_REQUEST_APPROVAL_RULE_CREATED', 'PULL_REQUEST_APPROVAL_RULE_UPDATED', 'PULL_REQUEST_APPROVAL_RULE_DELETED', 'PULL_REQUEST_APPROVAL_RULE_OVERRIDDEN', 'PULL_REQUEST_APPROVAL_STATE_CHANGED', ], ], 'PullRequestId' => [ 'type' => 'string', ], 'PullRequestIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PullRequestId', ], ], 'PullRequestIdRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'PullRequestMergedStateChangedEventMetadata' => [ 'type' => 'structure', 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'destinationReference' => [ 'shape' => 'ReferenceName', ], 'mergeMetadata' => [ 'shape' => 'MergeMetadata', ], ], ], 'PullRequestSourceReferenceUpdatedEventMetadata' => [ 'type' => 'structure', 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'beforeCommitId' => [ 'shape' => 'CommitId', ], 'afterCommitId' => [ 'shape' => 'CommitId', ], 'mergeBase' => [ 'shape' => 'CommitId', ], ], ], 'PullRequestStatusChangedEventMetadata' => [ 'type' => 'structure', 'members' => [ 'pullRequestStatus' => [ 'shape' => 'PullRequestStatusEnum', ], ], ], 'PullRequestStatusEnum' => [ 'type' => 'string', 'enum' => [ 'OPEN', 'CLOSED', ], ], 'PullRequestStatusRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'PullRequestTarget' => [ 'type' => 'structure', 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'sourceReference' => [ 'shape' => 'ReferenceName', ], 'destinationReference' => [ 'shape' => 'ReferenceName', ], 'destinationCommit' => [ 'shape' => 'CommitId', ], 'sourceCommit' => [ 'shape' => 'CommitId', ], 'mergeBase' => [ 'shape' => 'CommitId', ], 'mergeMetadata' => [ 'shape' => 'MergeMetadata', ], ], ], 'PullRequestTargetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PullRequestTarget', ], ], 'PutCommentReactionInput' => [ 'type' => 'structure', 'required' => [ 'commentId', 'reactionValue', ], 'members' => [ 'commentId' => [ 'shape' => 'CommentId', ], 'reactionValue' => [ 'shape' => 'ReactionValue', ], ], ], 'PutFileEntries' => [ 'type' => 'list', 'member' => [ 'shape' => 'PutFileEntry', ], ], 'PutFileEntry' => [ 'type' => 'structure', 'required' => [ 'filePath', ], 'members' => [ 'filePath' => [ 'shape' => 'Path', ], 'fileMode' => [ 'shape' => 'FileModeTypeEnum', ], 'fileContent' => [ 'shape' => 'FileContent', ], 'sourceFile' => [ 'shape' => 'SourceFileSpecifier', ], ], ], 'PutFileEntryConflictException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'PutFileInput' => [ 'type' => 'structure', 'required' => [ 'repositoryName', 'branchName', 'fileContent', 'filePath', ], 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'branchName' => [ 'shape' => 'BranchName', ], 'fileContent' => [ 'shape' => 'FileContent', ], 'filePath' => [ 'shape' => 'Path', ], 'fileMode' => [ 'shape' => 'FileModeTypeEnum', ], 'parentCommitId' => [ 'shape' => 'CommitId', ], 'commitMessage' => [ 'shape' => 'Message', ], 'name' => [ 'shape' => 'Name', ], 'email' => [ 'shape' => 'Email', ], ], ], 'PutFileOutput' => [ 'type' => 'structure', 'required' => [ 'commitId', 'blobId', 'treeId', ], 'members' => [ 'commitId' => [ 'shape' => 'ObjectId', ], 'blobId' => [ 'shape' => 'ObjectId', ], 'treeId' => [ 'shape' => 'ObjectId', ], ], ], 'PutRepositoryTriggersInput' => [ 'type' => 'structure', 'required' => [ 'repositoryName', 'triggers', ], 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'triggers' => [ 'shape' => 'RepositoryTriggersList', ], ], ], 'PutRepositoryTriggersOutput' => [ 'type' => 'structure', 'members' => [ 'configurationId' => [ 'shape' => 'RepositoryTriggersConfigurationId', ], ], ], 'ReactionCountsMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ReactionValue', ], 'value' => [ 'shape' => 'Count', ], ], 'ReactionEmoji' => [ 'type' => 'string', ], 'ReactionForComment' => [ 'type' => 'structure', 'members' => [ 'reaction' => [ 'shape' => 'ReactionValueFormats', ], 'reactionUsers' => [ 'shape' => 'ReactionUsersList', ], 'reactionsFromDeletedUsersCount' => [ 'shape' => 'Count', ], ], ], 'ReactionLimitExceededException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ReactionShortCode' => [ 'type' => 'string', ], 'ReactionUnicode' => [ 'type' => 'string', ], 'ReactionUsersList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Arn', ], ], 'ReactionValue' => [ 'type' => 'string', ], 'ReactionValueFormats' => [ 'type' => 'structure', 'members' => [ 'emoji' => [ 'shape' => 'ReactionEmoji', ], 'shortCode' => [ 'shape' => 'ReactionShortCode', ], 'unicode' => [ 'shape' => 'ReactionUnicode', ], ], ], 'ReactionValueRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ReactionsForCommentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReactionForComment', ], ], 'ReferenceDoesNotExistException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ReferenceName' => [ 'type' => 'string', ], 'ReferenceNameRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ReferenceTypeNotSupportedException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'RelativeFileVersionEnum' => [ 'type' => 'string', 'enum' => [ 'BEFORE', 'AFTER', ], ], 'ReplaceContentEntries' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReplaceContentEntry', ], ], 'ReplaceContentEntry' => [ 'type' => 'structure', 'required' => [ 'filePath', 'replacementType', ], 'members' => [ 'filePath' => [ 'shape' => 'Path', ], 'replacementType' => [ 'shape' => 'ReplacementTypeEnum', ], 'content' => [ 'shape' => 'FileContent', ], 'fileMode' => [ 'shape' => 'FileModeTypeEnum', ], ], ], 'ReplacementContentRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ReplacementTypeEnum' => [ 'type' => 'string', 'enum' => [ 'KEEP_BASE', 'KEEP_SOURCE', 'KEEP_DESTINATION', 'USE_NEW_CONTENT', ], ], 'ReplacementTypeRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'RepositoryDescription' => [ 'type' => 'string', 'max' => 1000, ], 'RepositoryDoesNotExistException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'RepositoryId' => [ 'type' => 'string', ], 'RepositoryLimitExceededException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'RepositoryMetadata' => [ 'type' => 'structure', 'members' => [ 'accountId' => [ 'shape' => 'AccountId', ], 'repositoryId' => [ 'shape' => 'RepositoryId', ], 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'repositoryDescription' => [ 'shape' => 'RepositoryDescription', ], 'defaultBranch' => [ 'shape' => 'BranchName', ], 'lastModifiedDate' => [ 'shape' => 'LastModifiedDate', ], 'creationDate' => [ 'shape' => 'CreationDate', ], 'cloneUrlHttp' => [ 'shape' => 'CloneUrlHttp', ], 'cloneUrlSsh' => [ 'shape' => 'CloneUrlSsh', ], 'Arn' => [ 'shape' => 'Arn', ], 'kmsKeyId' => [ 'shape' => 'KmsKeyId', ], ], ], 'RepositoryMetadataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RepositoryMetadata', ], ], 'RepositoryName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[\\w\\.-]+', ], 'RepositoryNameExistsException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'RepositoryNameIdPair' => [ 'type' => 'structure', 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'repositoryId' => [ 'shape' => 'RepositoryId', ], ], ], 'RepositoryNameIdPairList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RepositoryNameIdPair', ], ], 'RepositoryNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RepositoryName', ], ], 'RepositoryNameRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'RepositoryNamesRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'RepositoryNotAssociatedWithPullRequestException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'RepositoryNotFoundList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RepositoryName', ], ], 'RepositoryTrigger' => [ 'type' => 'structure', 'required' => [ 'name', 'destinationArn', 'events', ], 'members' => [ 'name' => [ 'shape' => 'RepositoryTriggerName', ], 'destinationArn' => [ 'shape' => 'Arn', ], 'customData' => [ 'shape' => 'RepositoryTriggerCustomData', ], 'branches' => [ 'shape' => 'BranchNameList', ], 'events' => [ 'shape' => 'RepositoryTriggerEventList', ], ], ], 'RepositoryTriggerBranchNameListRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'RepositoryTriggerCustomData' => [ 'type' => 'string', ], 'RepositoryTriggerDestinationArnRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'RepositoryTriggerEventEnum' => [ 'type' => 'string', 'enum' => [ 'all', 'updateReference', 'createReference', 'deleteReference', ], ], 'RepositoryTriggerEventList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RepositoryTriggerEventEnum', ], ], 'RepositoryTriggerEventsListRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'RepositoryTriggerExecutionFailure' => [ 'type' => 'structure', 'members' => [ 'trigger' => [ 'shape' => 'RepositoryTriggerName', ], 'failureMessage' => [ 'shape' => 'RepositoryTriggerExecutionFailureMessage', ], ], ], 'RepositoryTriggerExecutionFailureList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RepositoryTriggerExecutionFailure', ], ], 'RepositoryTriggerExecutionFailureMessage' => [ 'type' => 'string', ], 'RepositoryTriggerName' => [ 'type' => 'string', ], 'RepositoryTriggerNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RepositoryTriggerName', ], ], 'RepositoryTriggerNameRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'RepositoryTriggersConfigurationId' => [ 'type' => 'string', ], 'RepositoryTriggersList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RepositoryTrigger', ], ], 'RepositoryTriggersListRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ResourceArn' => [ 'type' => 'string', ], 'ResourceArnRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'RestrictedSourceFileException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'RevisionChildren' => [ 'type' => 'list', 'member' => [ 'shape' => 'RevisionId', ], ], 'RevisionDag' => [ 'type' => 'list', 'member' => [ 'shape' => 'FileVersion', ], ], 'RevisionId' => [ 'type' => 'string', ], 'RevisionIdRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'RevisionNotCurrentException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'RuleContentSha256' => [ 'type' => 'string', ], 'SameFileContentException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'SamePathRequestException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'SetFileModeEntries' => [ 'type' => 'list', 'member' => [ 'shape' => 'SetFileModeEntry', ], ], 'SetFileModeEntry' => [ 'type' => 'structure', 'required' => [ 'filePath', 'fileMode', ], 'members' => [ 'filePath' => [ 'shape' => 'Path', ], 'fileMode' => [ 'shape' => 'FileModeTypeEnum', ], ], ], 'SortByEnum' => [ 'type' => 'string', 'enum' => [ 'repositoryName', 'lastModifiedDate', ], ], 'SourceAndDestinationAreSameException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'SourceFileOrContentRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'SourceFileSpecifier' => [ 'type' => 'structure', 'required' => [ 'filePath', ], 'members' => [ 'filePath' => [ 'shape' => 'Path', ], 'isMove' => [ 'shape' => 'IsMove', ], ], ], 'SubModule' => [ 'type' => 'structure', 'members' => [ 'commitId' => [ 'shape' => 'ObjectId', ], 'absolutePath' => [ 'shape' => 'Path', ], 'relativePath' => [ 'shape' => 'Path', ], ], ], 'SubModuleList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SubModule', ], ], 'SymbolicLink' => [ 'type' => 'structure', 'members' => [ 'blobId' => [ 'shape' => 'ObjectId', ], 'absolutePath' => [ 'shape' => 'Path', ], 'relativePath' => [ 'shape' => 'Path', ], 'fileMode' => [ 'shape' => 'FileModeTypeEnum', ], ], ], 'SymbolicLinkList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SymbolicLink', ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagKeysList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], ], 'TagKeysListRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'TagPolicyException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'TagResourceInput' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ResourceArn', ], 'tags' => [ 'shape' => 'TagsMap', ], ], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'TagsMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], ], 'TagsMapRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'Target' => [ 'type' => 'structure', 'required' => [ 'repositoryName', 'sourceReference', ], 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'sourceReference' => [ 'shape' => 'ReferenceName', ], 'destinationReference' => [ 'shape' => 'ReferenceName', ], ], ], 'TargetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Target', ], ], 'TargetRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'TargetsRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'TestRepositoryTriggersInput' => [ 'type' => 'structure', 'required' => [ 'repositoryName', 'triggers', ], 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'triggers' => [ 'shape' => 'RepositoryTriggersList', ], ], ], 'TestRepositoryTriggersOutput' => [ 'type' => 'structure', 'members' => [ 'successfulExecutions' => [ 'shape' => 'RepositoryTriggerNameList', ], 'failedExecutions' => [ 'shape' => 'RepositoryTriggerExecutionFailureList', ], ], ], 'TipOfSourceReferenceIsDifferentException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'TipsDivergenceExceededException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'Title' => [ 'type' => 'string', 'max' => 150, ], 'TitleRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'TooManyTagsException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'UntagResourceInput' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ResourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeysList', ], ], ], 'UpdateApprovalRuleTemplateContentInput' => [ 'type' => 'structure', 'required' => [ 'approvalRuleTemplateName', 'newRuleContent', ], 'members' => [ 'approvalRuleTemplateName' => [ 'shape' => 'ApprovalRuleTemplateName', ], 'newRuleContent' => [ 'shape' => 'ApprovalRuleTemplateContent', ], 'existingRuleContentSha256' => [ 'shape' => 'RuleContentSha256', ], ], ], 'UpdateApprovalRuleTemplateContentOutput' => [ 'type' => 'structure', 'required' => [ 'approvalRuleTemplate', ], 'members' => [ 'approvalRuleTemplate' => [ 'shape' => 'ApprovalRuleTemplate', ], ], ], 'UpdateApprovalRuleTemplateDescriptionInput' => [ 'type' => 'structure', 'required' => [ 'approvalRuleTemplateName', 'approvalRuleTemplateDescription', ], 'members' => [ 'approvalRuleTemplateName' => [ 'shape' => 'ApprovalRuleTemplateName', ], 'approvalRuleTemplateDescription' => [ 'shape' => 'ApprovalRuleTemplateDescription', ], ], ], 'UpdateApprovalRuleTemplateDescriptionOutput' => [ 'type' => 'structure', 'required' => [ 'approvalRuleTemplate', ], 'members' => [ 'approvalRuleTemplate' => [ 'shape' => 'ApprovalRuleTemplate', ], ], ], 'UpdateApprovalRuleTemplateNameInput' => [ 'type' => 'structure', 'required' => [ 'oldApprovalRuleTemplateName', 'newApprovalRuleTemplateName', ], 'members' => [ 'oldApprovalRuleTemplateName' => [ 'shape' => 'ApprovalRuleTemplateName', ], 'newApprovalRuleTemplateName' => [ 'shape' => 'ApprovalRuleTemplateName', ], ], ], 'UpdateApprovalRuleTemplateNameOutput' => [ 'type' => 'structure', 'required' => [ 'approvalRuleTemplate', ], 'members' => [ 'approvalRuleTemplate' => [ 'shape' => 'ApprovalRuleTemplate', ], ], ], 'UpdateCommentInput' => [ 'type' => 'structure', 'required' => [ 'commentId', 'content', ], 'members' => [ 'commentId' => [ 'shape' => 'CommentId', ], 'content' => [ 'shape' => 'Content', ], ], ], 'UpdateCommentOutput' => [ 'type' => 'structure', 'members' => [ 'comment' => [ 'shape' => 'Comment', ], ], ], 'UpdateDefaultBranchInput' => [ 'type' => 'structure', 'required' => [ 'repositoryName', 'defaultBranchName', ], 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'defaultBranchName' => [ 'shape' => 'BranchName', ], ], ], 'UpdatePullRequestApprovalRuleContentInput' => [ 'type' => 'structure', 'required' => [ 'pullRequestId', 'approvalRuleName', 'newRuleContent', ], 'members' => [ 'pullRequestId' => [ 'shape' => 'PullRequestId', ], 'approvalRuleName' => [ 'shape' => 'ApprovalRuleName', ], 'existingRuleContentSha256' => [ 'shape' => 'RuleContentSha256', ], 'newRuleContent' => [ 'shape' => 'ApprovalRuleContent', ], ], ], 'UpdatePullRequestApprovalRuleContentOutput' => [ 'type' => 'structure', 'required' => [ 'approvalRule', ], 'members' => [ 'approvalRule' => [ 'shape' => 'ApprovalRule', ], ], ], 'UpdatePullRequestApprovalStateInput' => [ 'type' => 'structure', 'required' => [ 'pullRequestId', 'revisionId', 'approvalState', ], 'members' => [ 'pullRequestId' => [ 'shape' => 'PullRequestId', ], 'revisionId' => [ 'shape' => 'RevisionId', ], 'approvalState' => [ 'shape' => 'ApprovalState', ], ], ], 'UpdatePullRequestDescriptionInput' => [ 'type' => 'structure', 'required' => [ 'pullRequestId', 'description', ], 'members' => [ 'pullRequestId' => [ 'shape' => 'PullRequestId', ], 'description' => [ 'shape' => 'Description', ], ], ], 'UpdatePullRequestDescriptionOutput' => [ 'type' => 'structure', 'required' => [ 'pullRequest', ], 'members' => [ 'pullRequest' => [ 'shape' => 'PullRequest', ], ], ], 'UpdatePullRequestStatusInput' => [ 'type' => 'structure', 'required' => [ 'pullRequestId', 'pullRequestStatus', ], 'members' => [ 'pullRequestId' => [ 'shape' => 'PullRequestId', ], 'pullRequestStatus' => [ 'shape' => 'PullRequestStatusEnum', ], ], ], 'UpdatePullRequestStatusOutput' => [ 'type' => 'structure', 'required' => [ 'pullRequest', ], 'members' => [ 'pullRequest' => [ 'shape' => 'PullRequest', ], ], ], 'UpdatePullRequestTitleInput' => [ 'type' => 'structure', 'required' => [ 'pullRequestId', 'title', ], 'members' => [ 'pullRequestId' => [ 'shape' => 'PullRequestId', ], 'title' => [ 'shape' => 'Title', ], ], ], 'UpdatePullRequestTitleOutput' => [ 'type' => 'structure', 'required' => [ 'pullRequest', ], 'members' => [ 'pullRequest' => [ 'shape' => 'PullRequest', ], ], ], 'UpdateRepositoryDescriptionInput' => [ 'type' => 'structure', 'required' => [ 'repositoryName', ], 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'repositoryDescription' => [ 'shape' => 'RepositoryDescription', ], ], ], 'UpdateRepositoryEncryptionKeyInput' => [ 'type' => 'structure', 'required' => [ 'repositoryName', 'kmsKeyId', ], 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'kmsKeyId' => [ 'shape' => 'KmsKeyId', ], ], ], 'UpdateRepositoryEncryptionKeyOutput' => [ 'type' => 'structure', 'members' => [ 'repositoryId' => [ 'shape' => 'RepositoryId', ], 'kmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'originalKmsKeyId' => [ 'shape' => 'KmsKeyId', ], ], ], 'UpdateRepositoryNameInput' => [ 'type' => 'structure', 'required' => [ 'oldName', 'newName', ], 'members' => [ 'oldName' => [ 'shape' => 'RepositoryName', ], 'newName' => [ 'shape' => 'RepositoryName', ], ], ], 'UserInfo' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'Name', ], 'email' => [ 'shape' => 'Email', ], 'date' => [ 'shape' => 'Date', ], ], ], 'blob' => [ 'type' => 'blob', ], ],];
