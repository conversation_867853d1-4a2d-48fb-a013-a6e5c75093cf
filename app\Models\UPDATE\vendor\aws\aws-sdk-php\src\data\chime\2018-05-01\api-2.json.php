<?php
// This file was auto-generated from sdk-root/src/data/chime/2018-05-01/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2018-05-01', 'endpointPrefix' => 'chime', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceFullName' => 'Amazon Chime', 'serviceId' => 'Chime', 'signatureVersion' => 'v4', 'uid' => 'chime-2018-05-01', 'auth' => [ 'aws.auth#sigv4', ], ], 'operations' => [ 'AssociatePhoneNumberWithUser' => [ 'name' => 'AssociatePhoneNumberWithUser', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts/{accountId}/users/{userId}?operation=associate-phone-number', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AssociatePhoneNumberWithUserRequest', ], 'output' => [ 'shape' => 'AssociatePhoneNumberWithUserResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'AssociateSigninDelegateGroupsWithAccount' => [ 'name' => 'AssociateSigninDelegateGroupsWithAccount', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts/{accountId}?operation=associate-signin-delegate-groups', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AssociateSigninDelegateGroupsWithAccountRequest', ], 'output' => [ 'shape' => 'AssociateSigninDelegateGroupsWithAccountResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'BatchCreateRoomMembership' => [ 'name' => 'BatchCreateRoomMembership', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts/{accountId}/rooms/{roomId}/memberships?operation=batch-create', 'responseCode' => 201, ], 'input' => [ 'shape' => 'BatchCreateRoomMembershipRequest', ], 'output' => [ 'shape' => 'BatchCreateRoomMembershipResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'BatchDeletePhoneNumber' => [ 'name' => 'BatchDeletePhoneNumber', 'http' => [ 'method' => 'POST', 'requestUri' => '/phone-numbers?operation=batch-delete', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchDeletePhoneNumberRequest', ], 'output' => [ 'shape' => 'BatchDeletePhoneNumberResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'BatchSuspendUser' => [ 'name' => 'BatchSuspendUser', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts/{accountId}/users?operation=suspend', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchSuspendUserRequest', ], 'output' => [ 'shape' => 'BatchSuspendUserResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'BatchUnsuspendUser' => [ 'name' => 'BatchUnsuspendUser', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts/{accountId}/users?operation=unsuspend', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchUnsuspendUserRequest', ], 'output' => [ 'shape' => 'BatchUnsuspendUserResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'BatchUpdatePhoneNumber' => [ 'name' => 'BatchUpdatePhoneNumber', 'http' => [ 'method' => 'POST', 'requestUri' => '/phone-numbers?operation=batch-update', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchUpdatePhoneNumberRequest', ], 'output' => [ 'shape' => 'BatchUpdatePhoneNumberResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'BatchUpdateUser' => [ 'name' => 'BatchUpdateUser', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts/{accountId}/users', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchUpdateUserRequest', ], 'output' => [ 'shape' => 'BatchUpdateUserResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'CreateAccount' => [ 'name' => 'CreateAccount', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateAccountRequest', ], 'output' => [ 'shape' => 'CreateAccountResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'CreateBot' => [ 'name' => 'CreateBot', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts/{accountId}/bots', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateBotRequest', ], 'output' => [ 'shape' => 'CreateBotResponse', ], 'errors' => [ [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ThrottledClientException', ], ], ], 'CreateMeetingDialOut' => [ 'name' => 'CreateMeetingDialOut', 'http' => [ 'method' => 'POST', 'requestUri' => '/meetings/{meetingId}/dial-outs', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateMeetingDialOutRequest', ], 'output' => [ 'shape' => 'CreateMeetingDialOutResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'CreatePhoneNumberOrder' => [ 'name' => 'CreatePhoneNumberOrder', 'http' => [ 'method' => 'POST', 'requestUri' => '/phone-number-orders', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreatePhoneNumberOrderRequest', ], 'output' => [ 'shape' => 'CreatePhoneNumberOrderResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'CreateRoom' => [ 'name' => 'CreateRoom', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts/{accountId}/rooms', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateRoomRequest', ], 'output' => [ 'shape' => 'CreateRoomResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'CreateRoomMembership' => [ 'name' => 'CreateRoomMembership', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts/{accountId}/rooms/{roomId}/memberships', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateRoomMembershipRequest', ], 'output' => [ 'shape' => 'CreateRoomMembershipResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'CreateUser' => [ 'name' => 'CreateUser', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts/{accountId}/users?operation=create', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateUserRequest', ], 'output' => [ 'shape' => 'CreateUserResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'DeleteAccount' => [ 'name' => 'DeleteAccount', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/accounts/{accountId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteAccountRequest', ], 'output' => [ 'shape' => 'DeleteAccountResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'UnprocessableEntityException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'DeleteEventsConfiguration' => [ 'name' => 'DeleteEventsConfiguration', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/accounts/{accountId}/bots/{botId}/events-configuration', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteEventsConfigurationRequest', ], 'errors' => [ [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ResourceLimitExceededException', ], ], ], 'DeletePhoneNumber' => [ 'name' => 'DeletePhoneNumber', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/phone-numbers/{phoneNumberId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeletePhoneNumberRequest', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'DeleteRoom' => [ 'name' => 'DeleteRoom', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/accounts/{accountId}/rooms/{roomId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteRoomRequest', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'DeleteRoomMembership' => [ 'name' => 'DeleteRoomMembership', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/accounts/{accountId}/rooms/{roomId}/memberships/{memberId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteRoomMembershipRequest', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'DisassociatePhoneNumberFromUser' => [ 'name' => 'DisassociatePhoneNumberFromUser', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts/{accountId}/users/{userId}?operation=disassociate-phone-number', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DisassociatePhoneNumberFromUserRequest', ], 'output' => [ 'shape' => 'DisassociatePhoneNumberFromUserResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'DisassociateSigninDelegateGroupsFromAccount' => [ 'name' => 'DisassociateSigninDelegateGroupsFromAccount', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts/{accountId}?operation=disassociate-signin-delegate-groups', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DisassociateSigninDelegateGroupsFromAccountRequest', ], 'output' => [ 'shape' => 'DisassociateSigninDelegateGroupsFromAccountResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'GetAccount' => [ 'name' => 'GetAccount', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{accountId}', ], 'input' => [ 'shape' => 'GetAccountRequest', ], 'output' => [ 'shape' => 'GetAccountResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'GetAccountSettings' => [ 'name' => 'GetAccountSettings', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{accountId}/settings', ], 'input' => [ 'shape' => 'GetAccountSettingsRequest', ], 'output' => [ 'shape' => 'GetAccountSettingsResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'GetBot' => [ 'name' => 'GetBot', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{accountId}/bots/{botId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetBotRequest', ], 'output' => [ 'shape' => 'GetBotResponse', ], 'errors' => [ [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], ], ], 'GetEventsConfiguration' => [ 'name' => 'GetEventsConfiguration', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{accountId}/bots/{botId}/events-configuration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetEventsConfigurationRequest', ], 'output' => [ 'shape' => 'GetEventsConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'NotFoundException', ], ], ], 'GetGlobalSettings' => [ 'name' => 'GetGlobalSettings', 'http' => [ 'method' => 'GET', 'requestUri' => '/settings', 'responseCode' => 200, ], 'output' => [ 'shape' => 'GetGlobalSettingsResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'GetPhoneNumber' => [ 'name' => 'GetPhoneNumber', 'http' => [ 'method' => 'GET', 'requestUri' => '/phone-numbers/{phoneNumberId}', ], 'input' => [ 'shape' => 'GetPhoneNumberRequest', ], 'output' => [ 'shape' => 'GetPhoneNumberResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'GetPhoneNumberOrder' => [ 'name' => 'GetPhoneNumberOrder', 'http' => [ 'method' => 'GET', 'requestUri' => '/phone-number-orders/{phoneNumberOrderId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetPhoneNumberOrderRequest', ], 'output' => [ 'shape' => 'GetPhoneNumberOrderResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'GetPhoneNumberSettings' => [ 'name' => 'GetPhoneNumberSettings', 'http' => [ 'method' => 'GET', 'requestUri' => '/settings/phone-number', 'responseCode' => 200, ], 'output' => [ 'shape' => 'GetPhoneNumberSettingsResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'GetRetentionSettings' => [ 'name' => 'GetRetentionSettings', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{accountId}/retention-settings', ], 'input' => [ 'shape' => 'GetRetentionSettingsRequest', ], 'output' => [ 'shape' => 'GetRetentionSettingsResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'GetRoom' => [ 'name' => 'GetRoom', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{accountId}/rooms/{roomId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetRoomRequest', ], 'output' => [ 'shape' => 'GetRoomResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'GetUser' => [ 'name' => 'GetUser', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{accountId}/users/{userId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetUserRequest', ], 'output' => [ 'shape' => 'GetUserResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'GetUserSettings' => [ 'name' => 'GetUserSettings', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{accountId}/users/{userId}/settings', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetUserSettingsRequest', ], 'output' => [ 'shape' => 'GetUserSettingsResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'InviteUsers' => [ 'name' => 'InviteUsers', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts/{accountId}/users?operation=add', 'responseCode' => 201, ], 'input' => [ 'shape' => 'InviteUsersRequest', ], 'output' => [ 'shape' => 'InviteUsersResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'ListAccounts' => [ 'name' => 'ListAccounts', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts', ], 'input' => [ 'shape' => 'ListAccountsRequest', ], 'output' => [ 'shape' => 'ListAccountsResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'ListBots' => [ 'name' => 'ListBots', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{accountId}/bots', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListBotsRequest', ], 'output' => [ 'shape' => 'ListBotsResponse', ], 'errors' => [ [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ThrottledClientException', ], ], ], 'ListPhoneNumberOrders' => [ 'name' => 'ListPhoneNumberOrders', 'http' => [ 'method' => 'GET', 'requestUri' => '/phone-number-orders', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListPhoneNumberOrdersRequest', ], 'output' => [ 'shape' => 'ListPhoneNumberOrdersResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'ListPhoneNumbers' => [ 'name' => 'ListPhoneNumbers', 'http' => [ 'method' => 'GET', 'requestUri' => '/phone-numbers', ], 'input' => [ 'shape' => 'ListPhoneNumbersRequest', ], 'output' => [ 'shape' => 'ListPhoneNumbersResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'ListRoomMemberships' => [ 'name' => 'ListRoomMemberships', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{accountId}/rooms/{roomId}/memberships', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListRoomMembershipsRequest', ], 'output' => [ 'shape' => 'ListRoomMembershipsResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'ListRooms' => [ 'name' => 'ListRooms', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{accountId}/rooms', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListRoomsRequest', ], 'output' => [ 'shape' => 'ListRoomsResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'ListSupportedPhoneNumberCountries' => [ 'name' => 'ListSupportedPhoneNumberCountries', 'http' => [ 'method' => 'GET', 'requestUri' => '/phone-number-countries', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListSupportedPhoneNumberCountriesRequest', ], 'output' => [ 'shape' => 'ListSupportedPhoneNumberCountriesResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'ListUsers' => [ 'name' => 'ListUsers', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{accountId}/users', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListUsersRequest', ], 'output' => [ 'shape' => 'ListUsersResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'LogoutUser' => [ 'name' => 'LogoutUser', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts/{accountId}/users/{userId}?operation=logout', 'responseCode' => 204, ], 'input' => [ 'shape' => 'LogoutUserRequest', ], 'output' => [ 'shape' => 'LogoutUserResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'PutEventsConfiguration' => [ 'name' => 'PutEventsConfiguration', 'http' => [ 'method' => 'PUT', 'requestUri' => '/accounts/{accountId}/bots/{botId}/events-configuration', 'responseCode' => 201, ], 'input' => [ 'shape' => 'PutEventsConfigurationRequest', ], 'output' => [ 'shape' => 'PutEventsConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'NotFoundException', ], ], ], 'PutRetentionSettings' => [ 'name' => 'PutRetentionSettings', 'http' => [ 'method' => 'PUT', 'requestUri' => '/accounts/{accountId}/retention-settings', 'responseCode' => 204, ], 'input' => [ 'shape' => 'PutRetentionSettingsRequest', ], 'output' => [ 'shape' => 'PutRetentionSettingsResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'RedactConversationMessage' => [ 'name' => 'RedactConversationMessage', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts/{accountId}/conversations/{conversationId}/messages/{messageId}?operation=redact', 'responseCode' => 200, ], 'input' => [ 'shape' => 'RedactConversationMessageRequest', ], 'output' => [ 'shape' => 'RedactConversationMessageResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'RedactRoomMessage' => [ 'name' => 'RedactRoomMessage', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts/{accountId}/rooms/{roomId}/messages/{messageId}?operation=redact', 'responseCode' => 200, ], 'input' => [ 'shape' => 'RedactRoomMessageRequest', ], 'output' => [ 'shape' => 'RedactRoomMessageResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'RegenerateSecurityToken' => [ 'name' => 'RegenerateSecurityToken', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts/{accountId}/bots/{botId}?operation=regenerate-security-token', 'responseCode' => 200, ], 'input' => [ 'shape' => 'RegenerateSecurityTokenRequest', ], 'output' => [ 'shape' => 'RegenerateSecurityTokenResponse', ], 'errors' => [ [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ThrottledClientException', ], ], ], 'ResetPersonalPIN' => [ 'name' => 'ResetPersonalPIN', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts/{accountId}/users/{userId}?operation=reset-personal-pin', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ResetPersonalPINRequest', ], 'output' => [ 'shape' => 'ResetPersonalPINResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'RestorePhoneNumber' => [ 'name' => 'RestorePhoneNumber', 'http' => [ 'method' => 'POST', 'requestUri' => '/phone-numbers/{phoneNumberId}?operation=restore', 'responseCode' => 200, ], 'input' => [ 'shape' => 'RestorePhoneNumberRequest', ], 'output' => [ 'shape' => 'RestorePhoneNumberResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'SearchAvailablePhoneNumbers' => [ 'name' => 'SearchAvailablePhoneNumbers', 'http' => [ 'method' => 'GET', 'requestUri' => '/search?type=phone-numbers', ], 'input' => [ 'shape' => 'SearchAvailablePhoneNumbersRequest', ], 'output' => [ 'shape' => 'SearchAvailablePhoneNumbersResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'UpdateAccount' => [ 'name' => 'UpdateAccount', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts/{accountId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateAccountRequest', ], 'output' => [ 'shape' => 'UpdateAccountResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'UpdateAccountSettings' => [ 'name' => 'UpdateAccountSettings', 'http' => [ 'method' => 'PUT', 'requestUri' => '/accounts/{accountId}/settings', 'responseCode' => 204, ], 'input' => [ 'shape' => 'UpdateAccountSettingsRequest', ], 'output' => [ 'shape' => 'UpdateAccountSettingsResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'UpdateBot' => [ 'name' => 'UpdateBot', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts/{accountId}/bots/{botId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateBotRequest', ], 'output' => [ 'shape' => 'UpdateBotResponse', ], 'errors' => [ [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ThrottledClientException', ], ], ], 'UpdateGlobalSettings' => [ 'name' => 'UpdateGlobalSettings', 'http' => [ 'method' => 'PUT', 'requestUri' => '/settings', 'responseCode' => 204, ], 'input' => [ 'shape' => 'UpdateGlobalSettingsRequest', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'UpdatePhoneNumber' => [ 'name' => 'UpdatePhoneNumber', 'http' => [ 'method' => 'POST', 'requestUri' => '/phone-numbers/{phoneNumberId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdatePhoneNumberRequest', ], 'output' => [ 'shape' => 'UpdatePhoneNumberResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'UpdatePhoneNumberSettings' => [ 'name' => 'UpdatePhoneNumberSettings', 'http' => [ 'method' => 'PUT', 'requestUri' => '/settings/phone-number', 'responseCode' => 204, ], 'input' => [ 'shape' => 'UpdatePhoneNumberSettingsRequest', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'UpdateRoom' => [ 'name' => 'UpdateRoom', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts/{accountId}/rooms/{roomId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateRoomRequest', ], 'output' => [ 'shape' => 'UpdateRoomResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'UpdateRoomMembership' => [ 'name' => 'UpdateRoomMembership', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts/{accountId}/rooms/{roomId}/memberships/{memberId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateRoomMembershipRequest', ], 'output' => [ 'shape' => 'UpdateRoomMembershipResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'UpdateUser' => [ 'name' => 'UpdateUser', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts/{accountId}/users/{userId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateUserRequest', ], 'output' => [ 'shape' => 'UpdateUserResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'UpdateUserSettings' => [ 'name' => 'UpdateUserSettings', 'http' => [ 'method' => 'PUT', 'requestUri' => '/accounts/{accountId}/users/{userId}/settings', 'responseCode' => 204, ], 'input' => [ 'shape' => 'UpdateUserSettingsRequest', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'ErrorCode', ], 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 403, ], 'exception' => true, ], 'Account' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'AccountId', 'Name', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'String', ], 'AccountId' => [ 'shape' => 'String', ], 'Name' => [ 'shape' => 'String', ], 'AccountType' => [ 'shape' => 'AccountType', ], 'CreatedTimestamp' => [ 'shape' => 'Iso8601Timestamp', ], 'DefaultLicense' => [ 'shape' => 'License', ], 'SupportedLicenses' => [ 'shape' => 'LicenseList', ], 'AccountStatus' => [ 'shape' => 'AccountStatus', ], 'SigninDelegateGroups' => [ 'shape' => 'SigninDelegateGroupList', ], ], ], 'AccountList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Account', ], ], 'AccountName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '.*\\S.*', ], 'AccountSettings' => [ 'type' => 'structure', 'members' => [ 'DisableRemoteControl' => [ 'shape' => 'Boolean', ], 'EnableDialOut' => [ 'shape' => 'Boolean', ], ], ], 'AccountStatus' => [ 'type' => 'string', 'enum' => [ 'Suspended', 'Active', ], ], 'AccountType' => [ 'type' => 'string', 'enum' => [ 'Team', 'EnterpriseDirectory', 'EnterpriseLWA', 'EnterpriseOIDC', ], ], 'AlexaForBusinessMetadata' => [ 'type' => 'structure', 'members' => [ 'IsAlexaForBusinessEnabled' => [ 'shape' => 'Boolean', ], 'AlexaForBusinessRoomArn' => [ 'shape' => 'SensitiveString', ], ], ], 'Alpha2CountryCode' => [ 'type' => 'string', 'pattern' => '[A-Z]{2}', ], 'AssociatePhoneNumberWithUserRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'UserId', 'E164PhoneNumber', ], 'members' => [ 'AccountId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'accountId', ], 'UserId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'userId', ], 'E164PhoneNumber' => [ 'shape' => 'E164PhoneNumber', ], ], ], 'AssociatePhoneNumberWithUserResponse' => [ 'type' => 'structure', 'members' => [], ], 'AssociateSigninDelegateGroupsWithAccountRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'SigninDelegateGroups', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], 'SigninDelegateGroups' => [ 'shape' => 'SigninDelegateGroupList', ], ], ], 'AssociateSigninDelegateGroupsWithAccountResponse' => [ 'type' => 'structure', 'members' => [], ], 'BadRequestException' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'ErrorCode', ], 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'BatchCreateRoomMembershipRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'RoomId', 'MembershipItemList', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], 'RoomId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'roomId', ], 'MembershipItemList' => [ 'shape' => 'MembershipItemList', ], ], ], 'BatchCreateRoomMembershipResponse' => [ 'type' => 'structure', 'members' => [ 'Errors' => [ 'shape' => 'MemberErrorList', ], ], ], 'BatchDeletePhoneNumberRequest' => [ 'type' => 'structure', 'required' => [ 'PhoneNumberIds', ], 'members' => [ 'PhoneNumberIds' => [ 'shape' => 'NonEmptyStringList', ], ], ], 'BatchDeletePhoneNumberResponse' => [ 'type' => 'structure', 'members' => [ 'PhoneNumberErrors' => [ 'shape' => 'PhoneNumberErrorList', ], ], ], 'BatchSuspendUserRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'UserIdList', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], 'UserIdList' => [ 'shape' => 'UserIdList', ], ], ], 'BatchSuspendUserResponse' => [ 'type' => 'structure', 'members' => [ 'UserErrors' => [ 'shape' => 'UserErrorList', ], ], ], 'BatchUnsuspendUserRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'UserIdList', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], 'UserIdList' => [ 'shape' => 'UserIdList', ], ], ], 'BatchUnsuspendUserResponse' => [ 'type' => 'structure', 'members' => [ 'UserErrors' => [ 'shape' => 'UserErrorList', ], ], ], 'BatchUpdatePhoneNumberRequest' => [ 'type' => 'structure', 'required' => [ 'UpdatePhoneNumberRequestItems', ], 'members' => [ 'UpdatePhoneNumberRequestItems' => [ 'shape' => 'UpdatePhoneNumberRequestItemList', ], ], ], 'BatchUpdatePhoneNumberResponse' => [ 'type' => 'structure', 'members' => [ 'PhoneNumberErrors' => [ 'shape' => 'PhoneNumberErrorList', ], ], ], 'BatchUpdateUserRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'UpdateUserRequestItems', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], 'UpdateUserRequestItems' => [ 'shape' => 'UpdateUserRequestItemList', ], ], ], 'BatchUpdateUserResponse' => [ 'type' => 'structure', 'members' => [ 'UserErrors' => [ 'shape' => 'UserErrorList', ], ], ], 'Boolean' => [ 'type' => 'boolean', ], 'Bot' => [ 'type' => 'structure', 'members' => [ 'BotId' => [ 'shape' => 'String', ], 'UserId' => [ 'shape' => 'String', ], 'DisplayName' => [ 'shape' => 'SensitiveString', ], 'BotType' => [ 'shape' => 'BotType', ], 'Disabled' => [ 'shape' => 'NullableBoolean', ], 'CreatedTimestamp' => [ 'shape' => 'Iso8601Timestamp', ], 'UpdatedTimestamp' => [ 'shape' => 'Iso8601Timestamp', ], 'BotEmail' => [ 'shape' => 'SensitiveString', ], 'SecurityToken' => [ 'shape' => 'SensitiveString', ], ], ], 'BotList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Bot', ], ], 'BotType' => [ 'type' => 'string', 'enum' => [ 'ChatBot', ], ], 'BusinessCallingSettings' => [ 'type' => 'structure', 'members' => [ 'CdrBucket' => [ 'shape' => 'String', 'box' => true, ], ], ], 'CallingName' => [ 'type' => 'string', 'pattern' => '^$|^[a-zA-Z0-9 ]{2,15}$', 'sensitive' => true, ], 'CallingNameStatus' => [ 'type' => 'string', 'enum' => [ 'Unassigned', 'UpdateInProgress', 'UpdateSucceeded', 'UpdateFailed', ], ], 'ClientRequestToken' => [ 'type' => 'string', 'max' => 64, 'min' => 2, 'pattern' => '[-_a-zA-Z0-9]*', 'sensitive' => true, ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'ErrorCode', ], 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'ConversationRetentionSettings' => [ 'type' => 'structure', 'members' => [ 'RetentionDays' => [ 'shape' => 'RetentionDays', ], ], ], 'CreateAccountRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'AccountName', ], ], ], 'CreateAccountResponse' => [ 'type' => 'structure', 'members' => [ 'Account' => [ 'shape' => 'Account', ], ], ], 'CreateBotRequest' => [ 'type' => 'structure', 'required' => [ 'DisplayName', 'AccountId', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], 'DisplayName' => [ 'shape' => 'SensitiveString', ], 'Domain' => [ 'shape' => 'NonEmptyString', ], ], ], 'CreateBotResponse' => [ 'type' => 'structure', 'members' => [ 'Bot' => [ 'shape' => 'Bot', ], ], ], 'CreateMeetingDialOutRequest' => [ 'type' => 'structure', 'required' => [ 'MeetingId', 'FromPhoneNumber', 'ToPhoneNumber', 'JoinToken', ], 'members' => [ 'MeetingId' => [ 'shape' => 'GuidString', 'location' => 'uri', 'locationName' => 'meetingId', ], 'FromPhoneNumber' => [ 'shape' => 'E164PhoneNumber', ], 'ToPhoneNumber' => [ 'shape' => 'E164PhoneNumber', ], 'JoinToken' => [ 'shape' => 'JoinTokenString', ], ], ], 'CreateMeetingDialOutResponse' => [ 'type' => 'structure', 'members' => [ 'TransactionId' => [ 'shape' => 'GuidString', ], ], ], 'CreatePhoneNumberOrderRequest' => [ 'type' => 'structure', 'required' => [ 'ProductType', 'E164PhoneNumbers', ], 'members' => [ 'ProductType' => [ 'shape' => 'PhoneNumberProductType', ], 'E164PhoneNumbers' => [ 'shape' => 'E164PhoneNumberList', ], ], ], 'CreatePhoneNumberOrderResponse' => [ 'type' => 'structure', 'members' => [ 'PhoneNumberOrder' => [ 'shape' => 'PhoneNumberOrder', ], ], ], 'CreateRoomMembershipRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'RoomId', 'MemberId', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], 'RoomId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'roomId', ], 'MemberId' => [ 'shape' => 'NonEmptyString', ], 'Role' => [ 'shape' => 'RoomMembershipRole', ], ], ], 'CreateRoomMembershipResponse' => [ 'type' => 'structure', 'members' => [ 'RoomMembership' => [ 'shape' => 'RoomMembership', ], ], ], 'CreateRoomRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'Name', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], 'Name' => [ 'shape' => 'SensitiveString', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], ], ], 'CreateRoomResponse' => [ 'type' => 'structure', 'members' => [ 'Room' => [ 'shape' => 'Room', ], ], ], 'CreateUserRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], 'Username' => [ 'shape' => 'String', ], 'Email' => [ 'shape' => 'EmailAddress', ], 'UserType' => [ 'shape' => 'UserType', ], ], ], 'CreateUserResponse' => [ 'type' => 'structure', 'members' => [ 'User' => [ 'shape' => 'User', ], ], ], 'DeleteAccountRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], ], ], 'DeleteAccountResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteEventsConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'BotId', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], 'BotId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'botId', ], ], ], 'DeletePhoneNumberRequest' => [ 'type' => 'structure', 'required' => [ 'PhoneNumberId', ], 'members' => [ 'PhoneNumberId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'phoneNumberId', ], ], ], 'DeleteRoomMembershipRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'RoomId', 'MemberId', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], 'RoomId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'roomId', ], 'MemberId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'memberId', ], ], ], 'DeleteRoomRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'RoomId', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], 'RoomId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'roomId', ], ], ], 'DisassociatePhoneNumberFromUserRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'UserId', ], 'members' => [ 'AccountId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'accountId', ], 'UserId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'userId', ], ], ], 'DisassociatePhoneNumberFromUserResponse' => [ 'type' => 'structure', 'members' => [], ], 'DisassociateSigninDelegateGroupsFromAccountRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'GroupNames', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], 'GroupNames' => [ 'shape' => 'NonEmptyStringList', ], ], ], 'DisassociateSigninDelegateGroupsFromAccountResponse' => [ 'type' => 'structure', 'members' => [], ], 'E164PhoneNumber' => [ 'type' => 'string', 'pattern' => '^\\+?[1-9]\\d{1,14}$', 'sensitive' => true, ], 'E164PhoneNumberList' => [ 'type' => 'list', 'member' => [ 'shape' => 'E164PhoneNumber', ], ], 'EmailAddress' => [ 'type' => 'string', 'pattern' => '.+@.+\\..+', 'sensitive' => true, ], 'EmailStatus' => [ 'type' => 'string', 'enum' => [ 'NotSent', 'Sent', 'Failed', ], ], 'ErrorCode' => [ 'type' => 'string', 'enum' => [ 'BadRequest', 'Conflict', 'Forbidden', 'NotFound', 'PreconditionFailed', 'ResourceLimitExceeded', 'ServiceFailure', 'AccessDenied', 'ServiceUnavailable', 'Throttled', 'Throttling', 'Unauthorized', 'Unprocessable', 'VoiceConnectorGroupAssociationsExist', 'PhoneNumberAssociationsExist', ], ], 'EventsConfiguration' => [ 'type' => 'structure', 'members' => [ 'BotId' => [ 'shape' => 'String', ], 'OutboundEventsHTTPSEndpoint' => [ 'shape' => 'SensitiveString', ], 'LambdaFunctionArn' => [ 'shape' => 'SensitiveString', ], ], ], 'ForbiddenException' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'ErrorCode', ], 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 403, ], 'exception' => true, ], 'GetAccountRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], ], ], 'GetAccountResponse' => [ 'type' => 'structure', 'members' => [ 'Account' => [ 'shape' => 'Account', ], ], ], 'GetAccountSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], ], ], 'GetAccountSettingsResponse' => [ 'type' => 'structure', 'members' => [ 'AccountSettings' => [ 'shape' => 'AccountSettings', ], ], ], 'GetBotRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'BotId', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], 'BotId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'botId', ], ], ], 'GetBotResponse' => [ 'type' => 'structure', 'members' => [ 'Bot' => [ 'shape' => 'Bot', ], ], ], 'GetEventsConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'BotId', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], 'BotId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'botId', ], ], ], 'GetEventsConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'EventsConfiguration' => [ 'shape' => 'EventsConfiguration', ], ], ], 'GetGlobalSettingsResponse' => [ 'type' => 'structure', 'members' => [ 'BusinessCalling' => [ 'shape' => 'BusinessCallingSettings', ], 'VoiceConnector' => [ 'shape' => 'VoiceConnectorSettings', ], ], ], 'GetPhoneNumberOrderRequest' => [ 'type' => 'structure', 'required' => [ 'PhoneNumberOrderId', ], 'members' => [ 'PhoneNumberOrderId' => [ 'shape' => 'GuidString', 'location' => 'uri', 'locationName' => 'phoneNumberOrderId', ], ], ], 'GetPhoneNumberOrderResponse' => [ 'type' => 'structure', 'members' => [ 'PhoneNumberOrder' => [ 'shape' => 'PhoneNumberOrder', ], ], ], 'GetPhoneNumberRequest' => [ 'type' => 'structure', 'required' => [ 'PhoneNumberId', ], 'members' => [ 'PhoneNumberId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'phoneNumberId', ], ], ], 'GetPhoneNumberResponse' => [ 'type' => 'structure', 'members' => [ 'PhoneNumber' => [ 'shape' => 'PhoneNumber', ], ], ], 'GetPhoneNumberSettingsResponse' => [ 'type' => 'structure', 'members' => [ 'CallingName' => [ 'shape' => 'CallingName', ], 'CallingNameUpdatedTimestamp' => [ 'shape' => 'Iso8601Timestamp', ], ], ], 'GetRetentionSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], ], ], 'GetRetentionSettingsResponse' => [ 'type' => 'structure', 'members' => [ 'RetentionSettings' => [ 'shape' => 'RetentionSettings', ], 'InitiateDeletionTimestamp' => [ 'shape' => 'Iso8601Timestamp', ], ], ], 'GetRoomRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'RoomId', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], 'RoomId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'roomId', ], ], ], 'GetRoomResponse' => [ 'type' => 'structure', 'members' => [ 'Room' => [ 'shape' => 'Room', ], ], ], 'GetUserRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'UserId', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], 'UserId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'userId', ], ], ], 'GetUserResponse' => [ 'type' => 'structure', 'members' => [ 'User' => [ 'shape' => 'User', ], ], ], 'GetUserSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'UserId', ], 'members' => [ 'AccountId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'accountId', ], 'UserId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'userId', ], ], ], 'GetUserSettingsResponse' => [ 'type' => 'structure', 'members' => [ 'UserSettings' => [ 'shape' => 'UserSettings', ], ], ], 'GuidString' => [ 'type' => 'string', 'pattern' => '[a-fA-F0-9]{8}(?:-[a-fA-F0-9]{4}){3}-[a-fA-F0-9]{12}', ], 'Invite' => [ 'type' => 'structure', 'members' => [ 'InviteId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'InviteStatus', ], 'EmailAddress' => [ 'shape' => 'EmailAddress', ], 'EmailStatus' => [ 'shape' => 'EmailStatus', ], ], ], 'InviteList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Invite', ], ], 'InviteStatus' => [ 'type' => 'string', 'enum' => [ 'Pending', 'Accepted', 'Failed', ], ], 'InviteUsersRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'UserEmailList', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], 'UserEmailList' => [ 'shape' => 'UserEmailList', ], 'UserType' => [ 'shape' => 'UserType', ], ], ], 'InviteUsersResponse' => [ 'type' => 'structure', 'members' => [ 'Invites' => [ 'shape' => 'InviteList', ], ], ], 'Iso8601Timestamp' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'JoinTokenString' => [ 'type' => 'string', 'max' => 2048, 'min' => 2, 'pattern' => '^[a-zA-Z0-9+/]+$', 'sensitive' => true, ], 'License' => [ 'type' => 'string', 'enum' => [ 'Basic', 'Plus', 'Pro', 'ProTrial', ], ], 'LicenseList' => [ 'type' => 'list', 'member' => [ 'shape' => 'License', ], ], 'ListAccountsRequest' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'AccountName', 'location' => 'querystring', 'locationName' => 'name', ], 'UserEmail' => [ 'shape' => 'EmailAddress', 'location' => 'querystring', 'locationName' => 'user-email', ], 'NextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'next-token', ], 'MaxResults' => [ 'shape' => 'ProfileServiceMaxResults', 'location' => 'querystring', 'locationName' => 'max-results', ], ], ], 'ListAccountsResponse' => [ 'type' => 'structure', 'members' => [ 'Accounts' => [ 'shape' => 'AccountList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListBotsRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], 'MaxResults' => [ 'shape' => 'ResultMax', 'location' => 'querystring', 'locationName' => 'max-results', ], 'NextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'next-token', ], ], ], 'ListBotsResponse' => [ 'type' => 'structure', 'members' => [ 'Bots' => [ 'shape' => 'BotList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListPhoneNumberOrdersRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'next-token', ], 'MaxResults' => [ 'shape' => 'ResultMax', 'location' => 'querystring', 'locationName' => 'max-results', ], ], ], 'ListPhoneNumberOrdersResponse' => [ 'type' => 'structure', 'members' => [ 'PhoneNumberOrders' => [ 'shape' => 'PhoneNumberOrderList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListPhoneNumbersRequest' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'PhoneNumberStatus', 'location' => 'querystring', 'locationName' => 'status', ], 'ProductType' => [ 'shape' => 'PhoneNumberProductType', 'location' => 'querystring', 'locationName' => 'product-type', ], 'FilterName' => [ 'shape' => 'PhoneNumberAssociationName', 'location' => 'querystring', 'locationName' => 'filter-name', ], 'FilterValue' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'filter-value', ], 'MaxResults' => [ 'shape' => 'ResultMax', 'location' => 'querystring', 'locationName' => 'max-results', ], 'NextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'next-token', ], ], ], 'ListPhoneNumbersResponse' => [ 'type' => 'structure', 'members' => [ 'PhoneNumbers' => [ 'shape' => 'PhoneNumberList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListRoomMembershipsRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'RoomId', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], 'RoomId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'roomId', ], 'MaxResults' => [ 'shape' => 'ResultMax', 'location' => 'querystring', 'locationName' => 'max-results', ], 'NextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'next-token', ], ], ], 'ListRoomMembershipsResponse' => [ 'type' => 'structure', 'members' => [ 'RoomMemberships' => [ 'shape' => 'RoomMembershipList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListRoomsRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], 'MemberId' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'member-id', ], 'MaxResults' => [ 'shape' => 'ResultMax', 'location' => 'querystring', 'locationName' => 'max-results', ], 'NextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'next-token', ], ], ], 'ListRoomsResponse' => [ 'type' => 'structure', 'members' => [ 'Rooms' => [ 'shape' => 'RoomList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListSupportedPhoneNumberCountriesRequest' => [ 'type' => 'structure', 'required' => [ 'ProductType', ], 'members' => [ 'ProductType' => [ 'shape' => 'PhoneNumberProductType', 'location' => 'querystring', 'locationName' => 'product-type', ], ], ], 'ListSupportedPhoneNumberCountriesResponse' => [ 'type' => 'structure', 'members' => [ 'PhoneNumberCountries' => [ 'shape' => 'PhoneNumberCountriesList', ], ], ], 'ListUsersRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], 'UserEmail' => [ 'shape' => 'EmailAddress', 'location' => 'querystring', 'locationName' => 'user-email', ], 'UserType' => [ 'shape' => 'UserType', 'location' => 'querystring', 'locationName' => 'user-type', ], 'MaxResults' => [ 'shape' => 'ProfileServiceMaxResults', 'location' => 'querystring', 'locationName' => 'max-results', ], 'NextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'next-token', ], ], ], 'ListUsersResponse' => [ 'type' => 'structure', 'members' => [ 'Users' => [ 'shape' => 'UserList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'LogoutUserRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'UserId', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], 'UserId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'userId', ], ], ], 'LogoutUserResponse' => [ 'type' => 'structure', 'members' => [], ], 'Member' => [ 'type' => 'structure', 'members' => [ 'MemberId' => [ 'shape' => 'NonEmptyString', ], 'MemberType' => [ 'shape' => 'MemberType', ], 'Email' => [ 'shape' => 'SensitiveString', ], 'FullName' => [ 'shape' => 'SensitiveString', ], 'AccountId' => [ 'shape' => 'NonEmptyString', ], ], ], 'MemberError' => [ 'type' => 'structure', 'members' => [ 'MemberId' => [ 'shape' => 'NonEmptyString', ], 'ErrorCode' => [ 'shape' => 'ErrorCode', ], 'ErrorMessage' => [ 'shape' => 'String', ], ], ], 'MemberErrorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MemberError', ], ], 'MemberType' => [ 'type' => 'string', 'enum' => [ 'User', 'Bot', 'Webhook', ], ], 'MembershipItem' => [ 'type' => 'structure', 'members' => [ 'MemberId' => [ 'shape' => 'NonEmptyString', ], 'Role' => [ 'shape' => 'RoomMembershipRole', ], ], ], 'MembershipItemList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MembershipItem', ], 'max' => 50, ], 'NonEmptyString' => [ 'type' => 'string', 'pattern' => '.*\\S.*', ], 'NonEmptyStringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], 'min' => 1, ], 'NotFoundException' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'ErrorCode', ], 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'NullableBoolean' => [ 'type' => 'boolean', ], 'OrderedPhoneNumber' => [ 'type' => 'structure', 'members' => [ 'E164PhoneNumber' => [ 'shape' => 'E164PhoneNumber', ], 'Status' => [ 'shape' => 'OrderedPhoneNumberStatus', ], ], ], 'OrderedPhoneNumberList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OrderedPhoneNumber', ], ], 'OrderedPhoneNumberStatus' => [ 'type' => 'string', 'enum' => [ 'Processing', 'Acquired', 'Failed', ], ], 'PhoneNumber' => [ 'type' => 'structure', 'members' => [ 'PhoneNumberId' => [ 'shape' => 'String', ], 'E164PhoneNumber' => [ 'shape' => 'E164PhoneNumber', ], 'Country' => [ 'shape' => 'Alpha2CountryCode', ], 'Type' => [ 'shape' => 'PhoneNumberType', ], 'ProductType' => [ 'shape' => 'PhoneNumberProductType', ], 'Status' => [ 'shape' => 'PhoneNumberStatus', ], 'Capabilities' => [ 'shape' => 'PhoneNumberCapabilities', ], 'Associations' => [ 'shape' => 'PhoneNumberAssociationList', ], 'CallingName' => [ 'shape' => 'CallingName', ], 'CallingNameStatus' => [ 'shape' => 'CallingNameStatus', ], 'CreatedTimestamp' => [ 'shape' => 'Iso8601Timestamp', ], 'UpdatedTimestamp' => [ 'shape' => 'Iso8601Timestamp', ], 'DeletionTimestamp' => [ 'shape' => 'Iso8601Timestamp', ], ], ], 'PhoneNumberAssociation' => [ 'type' => 'structure', 'members' => [ 'Value' => [ 'shape' => 'String', ], 'Name' => [ 'shape' => 'PhoneNumberAssociationName', ], 'AssociatedTimestamp' => [ 'shape' => 'Iso8601Timestamp', ], ], ], 'PhoneNumberAssociationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PhoneNumberAssociation', ], ], 'PhoneNumberAssociationName' => [ 'type' => 'string', 'enum' => [ 'AccountId', 'UserId', 'VoiceConnectorId', 'VoiceConnectorGroupId', 'SipRuleId', ], ], 'PhoneNumberCapabilities' => [ 'type' => 'structure', 'members' => [ 'InboundCall' => [ 'shape' => 'NullableBoolean', ], 'OutboundCall' => [ 'shape' => 'NullableBoolean', ], 'InboundSMS' => [ 'shape' => 'NullableBoolean', ], 'OutboundSMS' => [ 'shape' => 'NullableBoolean', ], 'InboundMMS' => [ 'shape' => 'NullableBoolean', ], 'OutboundMMS' => [ 'shape' => 'NullableBoolean', ], ], ], 'PhoneNumberCountriesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PhoneNumberCountry', ], ], 'PhoneNumberCountry' => [ 'type' => 'structure', 'members' => [ 'CountryCode' => [ 'shape' => 'Alpha2CountryCode', ], 'SupportedPhoneNumberTypes' => [ 'shape' => 'PhoneNumberTypeList', ], ], ], 'PhoneNumberError' => [ 'type' => 'structure', 'members' => [ 'PhoneNumberId' => [ 'shape' => 'NonEmptyString', ], 'ErrorCode' => [ 'shape' => 'ErrorCode', ], 'ErrorMessage' => [ 'shape' => 'String', ], ], ], 'PhoneNumberErrorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PhoneNumberError', ], ], 'PhoneNumberList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PhoneNumber', ], ], 'PhoneNumberMaxResults' => [ 'type' => 'integer', 'max' => 500, 'min' => 1, ], 'PhoneNumberOrder' => [ 'type' => 'structure', 'members' => [ 'PhoneNumberOrderId' => [ 'shape' => 'GuidString', ], 'ProductType' => [ 'shape' => 'PhoneNumberProductType', ], 'Status' => [ 'shape' => 'PhoneNumberOrderStatus', ], 'OrderedPhoneNumbers' => [ 'shape' => 'OrderedPhoneNumberList', ], 'CreatedTimestamp' => [ 'shape' => 'Iso8601Timestamp', ], 'UpdatedTimestamp' => [ 'shape' => 'Iso8601Timestamp', ], ], ], 'PhoneNumberOrderList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PhoneNumberOrder', ], ], 'PhoneNumberOrderStatus' => [ 'type' => 'string', 'enum' => [ 'Processing', 'Successful', 'Failed', 'Partial', ], ], 'PhoneNumberProductType' => [ 'type' => 'string', 'enum' => [ 'BusinessCalling', 'VoiceConnector', 'SipMediaApplicationDialIn', ], ], 'PhoneNumberStatus' => [ 'type' => 'string', 'enum' => [ 'AcquireInProgress', 'AcquireFailed', 'Unassigned', 'Assigned', 'ReleaseInProgress', 'DeleteInProgress', 'ReleaseFailed', 'DeleteFailed', ], ], 'PhoneNumberType' => [ 'type' => 'string', 'enum' => [ 'Local', 'TollFree', ], ], 'PhoneNumberTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PhoneNumberType', ], ], 'ProfileServiceMaxResults' => [ 'type' => 'integer', 'max' => 200, 'min' => 1, ], 'PutEventsConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'BotId', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], 'BotId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'botId', ], 'OutboundEventsHTTPSEndpoint' => [ 'shape' => 'SensitiveString', ], 'LambdaFunctionArn' => [ 'shape' => 'SensitiveString', ], ], ], 'PutEventsConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'EventsConfiguration' => [ 'shape' => 'EventsConfiguration', ], ], ], 'PutRetentionSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'RetentionSettings', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], 'RetentionSettings' => [ 'shape' => 'RetentionSettings', ], ], ], 'PutRetentionSettingsResponse' => [ 'type' => 'structure', 'members' => [ 'RetentionSettings' => [ 'shape' => 'RetentionSettings', ], 'InitiateDeletionTimestamp' => [ 'shape' => 'Iso8601Timestamp', ], ], ], 'RedactConversationMessageRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'ConversationId', 'MessageId', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], 'ConversationId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'conversationId', ], 'MessageId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'messageId', ], ], ], 'RedactConversationMessageResponse' => [ 'type' => 'structure', 'members' => [], ], 'RedactRoomMessageRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'RoomId', 'MessageId', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], 'RoomId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'roomId', ], 'MessageId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'messageId', ], ], ], 'RedactRoomMessageResponse' => [ 'type' => 'structure', 'members' => [], ], 'RegenerateSecurityTokenRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'BotId', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], 'BotId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'botId', ], ], ], 'RegenerateSecurityTokenResponse' => [ 'type' => 'structure', 'members' => [ 'Bot' => [ 'shape' => 'Bot', ], ], ], 'RegistrationStatus' => [ 'type' => 'string', 'enum' => [ 'Unregistered', 'Registered', 'Suspended', ], ], 'ResetPersonalPINRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'UserId', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], 'UserId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'userId', ], ], ], 'ResetPersonalPINResponse' => [ 'type' => 'structure', 'members' => [ 'User' => [ 'shape' => 'User', ], ], ], 'ResourceLimitExceededException' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'ErrorCode', ], 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'RestorePhoneNumberRequest' => [ 'type' => 'structure', 'required' => [ 'PhoneNumberId', ], 'members' => [ 'PhoneNumberId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'phoneNumberId', ], ], ], 'RestorePhoneNumberResponse' => [ 'type' => 'structure', 'members' => [ 'PhoneNumber' => [ 'shape' => 'PhoneNumber', ], ], ], 'ResultMax' => [ 'type' => 'integer', 'max' => 99, 'min' => 1, ], 'RetentionDays' => [ 'type' => 'integer', 'max' => 5475, 'min' => 1, ], 'RetentionSettings' => [ 'type' => 'structure', 'members' => [ 'RoomRetentionSettings' => [ 'shape' => 'RoomRetentionSettings', ], 'ConversationRetentionSettings' => [ 'shape' => 'ConversationRetentionSettings', ], ], ], 'Room' => [ 'type' => 'structure', 'members' => [ 'RoomId' => [ 'shape' => 'NonEmptyString', ], 'Name' => [ 'shape' => 'SensitiveString', ], 'AccountId' => [ 'shape' => 'NonEmptyString', ], 'CreatedBy' => [ 'shape' => 'NonEmptyString', ], 'CreatedTimestamp' => [ 'shape' => 'Iso8601Timestamp', ], 'UpdatedTimestamp' => [ 'shape' => 'Iso8601Timestamp', ], ], ], 'RoomList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Room', ], ], 'RoomMembership' => [ 'type' => 'structure', 'members' => [ 'RoomId' => [ 'shape' => 'NonEmptyString', ], 'Member' => [ 'shape' => 'Member', ], 'Role' => [ 'shape' => 'RoomMembershipRole', ], 'InvitedBy' => [ 'shape' => 'NonEmptyString', ], 'UpdatedTimestamp' => [ 'shape' => 'Iso8601Timestamp', ], ], ], 'RoomMembershipList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RoomMembership', ], ], 'RoomMembershipRole' => [ 'type' => 'string', 'enum' => [ 'Administrator', 'Member', ], ], 'RoomRetentionSettings' => [ 'type' => 'structure', 'members' => [ 'RetentionDays' => [ 'shape' => 'RetentionDays', ], ], ], 'SearchAvailablePhoneNumbersRequest' => [ 'type' => 'structure', 'members' => [ 'AreaCode' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'area-code', ], 'City' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'city', ], 'Country' => [ 'shape' => 'Alpha2CountryCode', 'location' => 'querystring', 'locationName' => 'country', ], 'State' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'state', ], 'TollFreePrefix' => [ 'shape' => 'TollFreePrefix', 'location' => 'querystring', 'locationName' => 'toll-free-prefix', ], 'PhoneNumberType' => [ 'shape' => 'PhoneNumberType', 'location' => 'querystring', 'locationName' => 'phone-number-type', ], 'MaxResults' => [ 'shape' => 'PhoneNumberMaxResults', 'location' => 'querystring', 'locationName' => 'max-results', ], 'NextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'next-token', ], ], ], 'SearchAvailablePhoneNumbersResponse' => [ 'type' => 'structure', 'members' => [ 'E164PhoneNumbers' => [ 'shape' => 'E164PhoneNumberList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'SensitiveString' => [ 'type' => 'string', 'sensitive' => true, ], 'ServiceFailureException' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'ErrorCode', ], 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'ServiceUnavailableException' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'ErrorCode', ], 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, 'fault' => true, ], 'SigninDelegateGroup' => [ 'type' => 'structure', 'members' => [ 'GroupName' => [ 'shape' => 'NonEmptyString', ], ], ], 'SigninDelegateGroupList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SigninDelegateGroup', ], ], 'String' => [ 'type' => 'string', ], 'TelephonySettings' => [ 'type' => 'structure', 'required' => [ 'InboundCalling', 'OutboundCalling', 'SMS', ], 'members' => [ 'InboundCalling' => [ 'shape' => 'Boolean', ], 'OutboundCalling' => [ 'shape' => 'Boolean', ], 'SMS' => [ 'shape' => 'Boolean', ], ], ], 'ThrottledClientException' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'ErrorCode', ], 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], 'TollFreePrefix' => [ 'type' => 'string', 'max' => 3, 'min' => 3, 'pattern' => '^8(00|33|44|55|66|77|88)$', ], 'UnauthorizedClientException' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'ErrorCode', ], 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 401, ], 'exception' => true, ], 'UnprocessableEntityException' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'ErrorCode', ], 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 422, ], 'exception' => true, ], 'UpdateAccountRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], 'Name' => [ 'shape' => 'AccountName', ], 'DefaultLicense' => [ 'shape' => 'License', ], ], ], 'UpdateAccountResponse' => [ 'type' => 'structure', 'members' => [ 'Account' => [ 'shape' => 'Account', ], ], ], 'UpdateAccountSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'AccountSettings', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], 'AccountSettings' => [ 'shape' => 'AccountSettings', ], ], ], 'UpdateAccountSettingsResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateBotRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'BotId', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], 'BotId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'botId', ], 'Disabled' => [ 'shape' => 'NullableBoolean', ], ], ], 'UpdateBotResponse' => [ 'type' => 'structure', 'members' => [ 'Bot' => [ 'shape' => 'Bot', ], ], ], 'UpdateGlobalSettingsRequest' => [ 'type' => 'structure', 'members' => [ 'BusinessCalling' => [ 'shape' => 'BusinessCallingSettings', ], 'VoiceConnector' => [ 'shape' => 'VoiceConnectorSettings', ], ], ], 'UpdatePhoneNumberRequest' => [ 'type' => 'structure', 'required' => [ 'PhoneNumberId', ], 'members' => [ 'PhoneNumberId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'phoneNumberId', ], 'ProductType' => [ 'shape' => 'PhoneNumberProductType', ], 'CallingName' => [ 'shape' => 'CallingName', ], ], ], 'UpdatePhoneNumberRequestItem' => [ 'type' => 'structure', 'required' => [ 'PhoneNumberId', ], 'members' => [ 'PhoneNumberId' => [ 'shape' => 'NonEmptyString', ], 'ProductType' => [ 'shape' => 'PhoneNumberProductType', ], 'CallingName' => [ 'shape' => 'CallingName', ], ], ], 'UpdatePhoneNumberRequestItemList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UpdatePhoneNumberRequestItem', ], ], 'UpdatePhoneNumberResponse' => [ 'type' => 'structure', 'members' => [ 'PhoneNumber' => [ 'shape' => 'PhoneNumber', ], ], ], 'UpdatePhoneNumberSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'CallingName', ], 'members' => [ 'CallingName' => [ 'shape' => 'CallingName', ], ], ], 'UpdateRoomMembershipRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'RoomId', 'MemberId', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], 'RoomId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'roomId', ], 'MemberId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'memberId', ], 'Role' => [ 'shape' => 'RoomMembershipRole', ], ], ], 'UpdateRoomMembershipResponse' => [ 'type' => 'structure', 'members' => [ 'RoomMembership' => [ 'shape' => 'RoomMembership', ], ], ], 'UpdateRoomRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'RoomId', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], 'RoomId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'roomId', ], 'Name' => [ 'shape' => 'SensitiveString', ], ], ], 'UpdateRoomResponse' => [ 'type' => 'structure', 'members' => [ 'Room' => [ 'shape' => 'Room', ], ], ], 'UpdateUserRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'UserId', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], 'UserId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'userId', ], 'LicenseType' => [ 'shape' => 'License', ], 'UserType' => [ 'shape' => 'UserType', ], 'AlexaForBusinessMetadata' => [ 'shape' => 'AlexaForBusinessMetadata', ], ], ], 'UpdateUserRequestItem' => [ 'type' => 'structure', 'required' => [ 'UserId', ], 'members' => [ 'UserId' => [ 'shape' => 'NonEmptyString', ], 'LicenseType' => [ 'shape' => 'License', ], 'UserType' => [ 'shape' => 'UserType', ], 'AlexaForBusinessMetadata' => [ 'shape' => 'AlexaForBusinessMetadata', ], ], ], 'UpdateUserRequestItemList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UpdateUserRequestItem', ], 'max' => 20, ], 'UpdateUserResponse' => [ 'type' => 'structure', 'members' => [ 'User' => [ 'shape' => 'User', ], ], ], 'UpdateUserSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'UserId', 'UserSettings', ], 'members' => [ 'AccountId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'accountId', ], 'UserId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'userId', ], 'UserSettings' => [ 'shape' => 'UserSettings', ], ], ], 'User' => [ 'type' => 'structure', 'required' => [ 'UserId', ], 'members' => [ 'UserId' => [ 'shape' => 'String', ], 'AccountId' => [ 'shape' => 'String', ], 'PrimaryEmail' => [ 'shape' => 'EmailAddress', ], 'PrimaryProvisionedNumber' => [ 'shape' => 'SensitiveString', ], 'DisplayName' => [ 'shape' => 'SensitiveString', ], 'LicenseType' => [ 'shape' => 'License', ], 'UserType' => [ 'shape' => 'UserType', ], 'UserRegistrationStatus' => [ 'shape' => 'RegistrationStatus', ], 'UserInvitationStatus' => [ 'shape' => 'InviteStatus', ], 'RegisteredOn' => [ 'shape' => 'Iso8601Timestamp', ], 'InvitedOn' => [ 'shape' => 'Iso8601Timestamp', ], 'AlexaForBusinessMetadata' => [ 'shape' => 'AlexaForBusinessMetadata', ], 'PersonalPIN' => [ 'shape' => 'String', ], ], ], 'UserEmailList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EmailAddress', ], 'max' => 50, ], 'UserError' => [ 'type' => 'structure', 'members' => [ 'UserId' => [ 'shape' => 'NonEmptyString', ], 'ErrorCode' => [ 'shape' => 'ErrorCode', ], 'ErrorMessage' => [ 'shape' => 'String', ], ], ], 'UserErrorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UserError', ], ], 'UserIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], 'max' => 50, ], 'UserList' => [ 'type' => 'list', 'member' => [ 'shape' => 'User', ], ], 'UserSettings' => [ 'type' => 'structure', 'required' => [ 'Telephony', ], 'members' => [ 'Telephony' => [ 'shape' => 'TelephonySettings', ], ], ], 'UserType' => [ 'type' => 'string', 'enum' => [ 'PrivateUser', 'SharedDevice', ], ], 'VoiceConnectorSettings' => [ 'type' => 'structure', 'members' => [ 'CdrBucket' => [ 'shape' => 'String', 'box' => true, ], ], ], ], 'deprecated' => true, 'deprecatedMessage' => 'This namespace has been deprecated',];
