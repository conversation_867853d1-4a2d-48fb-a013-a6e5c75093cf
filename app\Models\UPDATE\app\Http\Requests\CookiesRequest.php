<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CookiesRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize() : bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules() : array
    {
        return [
            'cookies_details_page_id' => 'required|numeric',
            'cookies_summary'         => 'required|string',
        ];
    }
}
