"use strict";(self.webpackChunk=self.webpackChunk||[]).push([[112],{112:(t,e,a)=>{a.r(e),a.d(e,{default:()=>f});var n=a(9726),o={class:"mb-10 sm:mb-20"},i={class:"container"},r={class:"flex items-center justify-between gap-5 mb-6 max-md:mb-8"},s={class:"flex flex-wrap items-end gap-3 max-md:flex-col max-md:items-start max-md:gap-1.5"},l={class:"text-3xl font-bold capitalize max-sm:text-lg"},c={class:"text-xl font-medium capitalize max-sm:text-sm"},u={class:"w-full max-md:p-0"},p={class:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 mb-12"};var d=a(7468),g=a(3537),m=a(1811);const h={name:"ProductSectionProductComponent",components:{PaginationComponent:a(6079).A,LoadingComponent:m.A,LoadingContentComponent:g.A,ProductListComponent:d.A},data:function(){return{loading:{isActive:!1},loadingContent:{isActive:!1}}},computed:{productSection:function(){return this.$store.getters["frontendProductSection/show"]},pagination:function(){return this.$store.getters["frontendProductSection/productPagination"]},products:function(){return this.$store.getters["frontendProductSection/products"]}},mounted:function(){var t=this;void 0!==this.$route.params.slug&&(this.loading.isActive=!0,this.$store.dispatch("frontendProductSection/show",this.$route.params.slug).then(function(e){t.loading.isActive=!0,t.$store.dispatch("frontendProductSection/products",{slug:t.$route.params.slug,per_page:32}).then(function(e){t.loading.isActive=!1}).catch(function(e){t.loading.isActive=!1})}).catch(function(e){t.loading.isActive=!1}))},methods:{sectionProducts:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;void 0!==this.$route.params.slug&&(this.loadingContent.isActive=!0,this.$store.dispatch("frontendProductSection/products",{slug:this.$route.params.slug,per_page:32,page:e}).then(function(e){t.loadingContent.isActive=!1}).catch(function(e){t.loadingContent.isActive=!1}))}}};const f=(0,a(6262).A)(h,[["render",function(t,e,a,d,g,m){var h=(0,n.resolveComponent)("LoadingComponent"),f=(0,n.resolveComponent)("LoadingContentComponent"),P=(0,n.resolveComponent)("ProductListComponent"),v=(0,n.resolveComponent)("PaginationComponent");return(0,n.openBlock)(),(0,n.createElementBlock)(n.Fragment,null,[(0,n.createVNode)(h,{props:g.loading},null,8,["props"]),(0,n.createElementVNode)("section",o,[(0,n.createElementVNode)("div",i,[(0,n.createElementVNode)("div",r,[(0,n.createElementVNode)("div",s,[(0,n.createElementVNode)("h3",l,(0,n.toDisplayString)(m.productSection.name),1),(0,n.createElementVNode)("span",c," ("+(0,n.toDisplayString)(m.products.length)+" "+(0,n.toDisplayString)(m.products.length>1?t.$t("label.products_found"):t.$t("label.product_found"))+") ",1)])]),(0,n.createElementVNode)("div",u,[(0,n.createElementVNode)("div",p,[(0,n.createVNode)(f,{props:g.loadingContent},null,8,["props"]),m.products.length>0?((0,n.openBlock)(),(0,n.createBlock)(P,{key:0,products:m.products},null,8,["products"])):(0,n.createCommentVNode)("",!0)]),(0,n.createVNode)(v,{onPaginationChangePage:m.sectionProducts,data:m.pagination,limit:1,"keep-length":!1},null,8,["onPaginationChangePage","data"])])])])],64)}]])},3537:(t,e,a)=>{a.d(e,{A:()=>r});var n=a(9726);var o=a(4693);const i={name:"LoadingContentComponent",components:{VueElementLoading:a.n(o)()},props:["props"],data:function(){return{isActive:!1}}};const r=(0,a(6262).A)(i,[["render",function(t,e,a,o,i,r){var s=(0,n.resolveComponent)("VueElementLoading");return(0,n.openBlock)(),(0,n.createBlock)(s,{spinner:"bar-fade-scale",color:"#FD8B0E",active:a.props.isActive,"is-full-screen":!1},null,8,["active"])}]])},6079:(t,e,a)=>{a.d(e,{A:()=>l});var n=a(9726),o=["disabled"],i=["aria-current"],r=["disabled"];const s={name:"PaginationComponent",inheritAttrs:!1,emits:["pagination-change-page"],components:{RenderLessPagination:{emits:["pagination-change-page"],props:{data:{type:Object,default:()=>{}},limit:{type:Number,default:0},keepLength:{type:Boolean,default:!1}},computed:{isApiResource(){return!!this.data.meta},currentPage(){return this.isApiResource?this.data.meta.current_page:this.data.current_page??null},firstPageUrl(){return this.data.first_page_url??this.data.meta?.first_page_url??this.data.links?.first??null},from(){return this.isApiResource?this.data.meta.from:this.data.from??null},lastPage(){return this.isApiResource?this.data.meta.last_page:this.data.last_page??null},lastPageUrl(){return this.data.last_page_url??this.data.meta?.last_page_url??this.data.links?.last??null},nextPageUrl(){return this.data.next_page_url??this.data.meta?.next_page_url??this.data.links?.next??null},perPage(){return this.isApiResource?this.data.meta.per_page:this.data.per_page??null},prevPageUrl(){return this.data.prev_page_url??this.data.meta?.prev_page_url??this.data.links?.prev??null},to(){return this.isApiResource?this.data.meta.to:this.data.to??null},total(){return this.isApiResource?this.data.meta.total:this.data.total??null},pageRange(){if(-1===this.limit)return 0;if(0===this.limit)return this.lastPage;for(var t,e=this.currentPage,a=this.keepLength,n=this.lastPage,o=this.limit,i=e-o,r=e+o,s=2*(o+2),l=2*(o+2)-1,c=[],u=[],p=1;p<=n;p++)(1===p||p===n||p>=i&&p<=r||a&&p<s&&e<s-2||a&&p>n-l&&e>n-l+2)&&c.push(p);return c.forEach(function(e){t&&(e-t===2?u.push(t+1):e-t!==1&&u.push("...")),u.push(e),t=e}),u}},methods:{previousPage(){this.selectPage(this.currentPage-1)},nextPage(){this.selectPage(this.currentPage+1)},selectPage(t){"..."!==t&&t!==this.currentPage&&this.$emit("pagination-change-page",t)}},render(){return this.$slots.default({data:this.data,limit:this.limit,computed:{isApiResource:this.isApiResource,currentPage:this.currentPage,firstPageUrl:this.firstPageUrl,from:this.from,lastPage:this.lastPage,lastPageUrl:this.lastPageUrl,nextPageUrl:this.nextPageUrl,perPage:this.perPage,prevPageUrl:this.prevPageUrl,to:this.to,total:this.total,pageRange:this.pageRange},prevButtonEvents:{click:t=>{t.preventDefault(),this.previousPage()}},nextButtonEvents:{click:t=>{t.preventDefault(),this.nextPage()}},pageButtonEvents:t=>({click:e=>{e.preventDefault(),this.selectPage(t)}})})}}},props:{data:{type:Object,default:function(){}},limit:{type:Number,default:0},keepLength:{type:Boolean,default:!1}},data:function(){return{activeClass:["bg-primary","text-white"]}},methods:{onPaginationChangePage:function(t){this.$emit("pagination-change-page",t)}}};const l=(0,a(6262).A)(s,[["render",function(t,e,a,s,l,c){var u=(0,n.resolveComponent)("RenderLessPagination");return(0,n.openBlock)(),(0,n.createBlock)(u,{data:a.data,limit:a.limit,"keep-length":a.keepLength,onPaginationChangePage:c.onPaginationChangePage},{default:(0,n.withCtx)(function(e){return[e.computed.total>e.computed.perPage?((0,n.openBlock)(),(0,n.createElementBlock)("nav",(0,n.mergeProps)({key:0},t.$attrs,{"aria-label":"Pagination",class:"flex items-center justify-center gap-4 mobile:mb-12"}),[(0,n.createElementVNode)("button",(0,n.mergeProps)({disabled:!e.computed.prevPageUrl,class:e.computed.prevPageUrl?"hover:text-white hover:bg-primary":""},(0,n.toHandlers)(e.prevButtonEvents,!0),{class:"h-10 leading-10 px-4 rounded-full font-medium capitalize bg-gray-100 transition-all duration-500"}),[(0,n.renderSlot)(t.$slots,"prev-nav",{},function(){return[(0,n.createTextVNode)((0,n.toDisplayString)(t.$t("label.previous")),1)]})],16,o),((0,n.openBlock)(!0),(0,n.createElementBlock)(n.Fragment,null,(0,n.renderList)(e.computed.pageRange,function(t,a){return(0,n.openBlock)(),(0,n.createElementBlock)("button",(0,n.mergeProps)({class:e.computed.currentPage===t?"bg-primary text-white":"","aria-current":e.computed.currentPage?"page":null,key:a},(0,n.toHandlers)(e.pageButtonEvents(t),!0),{class:"w-10 h-10 leading-10 rounded-full font-medium capitalize text-center transition-all duration-500 hover:text-white hover:bg-primary hidden sm:block bg-gray-100"}),(0,n.toDisplayString)(t),17,i)}),128)),(0,n.createElementVNode)("button",(0,n.mergeProps)({disabled:!e.computed.nextPageUrl,class:e.computed.nextPageUrl?"hover:text-white hover:bg-primary":""},(0,n.toHandlers)(e.nextButtonEvents,!0),{class:"h-10 leading-10 px-4 rounded-full font-medium capitalize bg-gray-100 transition-all duration-500"}),[(0,n.renderSlot)(t.$slots,"next-nav",{},function(){return[(0,n.createTextVNode)((0,n.toDisplayString)(t.$t("label.next")),1)]})],16,r)],16)):(0,n.createCommentVNode)("",!0)]}),_:3},8,["data","limit","keep-length","onPaginationChangePage"])}]])}}]);