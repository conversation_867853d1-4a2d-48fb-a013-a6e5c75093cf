"use strict";(self.webpackChunk=self.webpackChunk||[]).push([[1801],{1801:(e,t,o)=>{o.r(t),o.d(t,{default:()=>S});var n=o(9726),a={class:"capitalize text-2xl font-bold mb-7 text-primary"},l={class:"p-6 mb-6 rounded-2xl shadow-card bg-white"},r={class:"text-xl font-bold capitalize mb-5"},i={class:"row"},c={class:"col-12 md:col-6"},s={for:"name",class:"field-title required"},d={key:0,class:"db-field-alert"},m={class:"col-12 md:col-6"},p={for:"email",class:"field-title required"},u={key:0,class:"db-field-alert"},f={class:"col-12 md:col-6"},h={for:"phone",class:"field-title required"},g={class:"w-fit flex-shrink-0 dropdown-group"},y={type:"button",class:"flex items-center gap-1 dropdown-btn"},v={class:"whitespace-nowrap flex-shrink-0 text-xs"},b={class:"p-1.5 w-24 rounded-lg shadow-xl absolute top-8 -left-4 z-10 border border-gray-200 bg-white scale-y-0 origin-top dropdown-list !h-52 !overflow-x-hidden !overflow-y-auto thin-scrolling"},N=["onClick"],V={class:"whitespace-nowrap text-xs"},E={key:0,class:"db-field-alert"},C={class:"col-12 md:col-6"},x={for:"image",class:"field-title"},w={key:0,class:"db-field-alert"},k={type:"submit",class:"px-6 py-3 capitalize rounded-full whitespace-nowrap text-center font-semibold text-white bg-primary"};var _=o(9856),A=o(8655);const D={name:"AccountInfoComponent",components:{LoadingComponent:o(1811).A},data:function(){return{loading:{isActive:!1},form:{name:"",email:"",phone:"",country_code:""},flag:"",image:"",errors:{}}},mounted:function(){var e=this;try{this.loading.isActive=!0;var t=this.$store.getters.authInfo;this.$store.dispatch("frontendCountryCode/lists"),this.form={name:t.name,email:t.email,phone:t.phone,country_code:t.country_code},null!==t.country_code&&this.$store.dispatch("frontendCountryCode/callingCode",t.country_code).then(function(t){e.flag=t.data.data.flag_emoji,e.loading.isActive=!1}).catch(function(t){e.loading.isActive=!1}),this.$store.dispatch("frontendSetting/lists").then(function(o){e.$store.dispatch("frontendCountryCode/show",o.data.data.company_country_code).then(function(o){null===t.country_code&&(e.flag=o.data.data.flag_emoji,e.form.country_code=o.data.data.calling_code)}).catch(function(t){e.loading.isActive=!1})}).catch(function(t){e.loading.isActive=!1}),this.loading.isActive=!1}catch(e){this.loading.isActive=!1,_.A.error(e)}},computed:{countryCodes:function(){return this.$store.getters["frontendCountryCode/lists"]}},methods:{phoneNumber:function(e){return A.A.phoneNumber(e)},changeCountryCode:function(e){this.flag=e.flag_emoji,this.form.country_code=e.calling_code},changeImage:function(e){this.image=e.target.files[0]},save:function(){var e=this;try{this.loading.isActive=!0;var t=new FormData;t.append("name",this.form.name),t.append("email",this.form.email),t.append("phone",this.form.phone),t.append("country_code",this.form.country_code),this.image&&t.append("image",this.image),this.$store.dispatch("frontendEditProfile/updateProfile",t).then(function(t){e.$store.dispatch("updateAuthInfo",t.data.data).then(function(t){e.loading.isActive=!1,_.A.successFlip(1,e.$t("menu.profile")),e.image="",e.errors={},e.$refs.imageProperty.value=null}).catch(function(t){e.loading.isActive=!1,_.A.error(t)})}).catch(function(t){e.loading.isActive=!1,e.errors=t.response.data.errors})}catch(e){this.loading.isActive=!1,_.A.error(e)}}}};const S=(0,o(6262).A)(D,[["render",function(e,t,o,_,A,D){var S=(0,n.resolveComponent)("LoadingComponent");return(0,n.openBlock)(),(0,n.createElementBlock)(n.Fragment,null,[(0,n.createVNode)(S,{props:A.loading},null,8,["props"]),(0,n.createElementVNode)("h2",a,(0,n.toDisplayString)(e.$t("label.account_information")),1),(0,n.createElementVNode)("form",{onSubmit:t[5]||(t[5]=(0,n.withModifiers)(function(){return D.save&&D.save.apply(D,arguments)},["prevent"])),id:"formElem"},[(0,n.createElementVNode)("div",l,[(0,n.createElementVNode)("h3",r,(0,n.toDisplayString)(e.$t("label.personal_info")),1),(0,n.createElementVNode)("div",i,[(0,n.createElementVNode)("div",c,[(0,n.createElementVNode)("label",s,(0,n.toDisplayString)(e.$t("label.full_name")),1),(0,n.withDirectives)((0,n.createElementVNode)("input",{type:"text",id:"name","onUpdate:modelValue":t[0]||(t[0]=function(e){return A.form.name=e}),class:(0,n.normalizeClass)([A.errors.name?"invalid":"","field-control"])},null,2),[[n.vModelText,A.form.name]]),A.errors.name?((0,n.openBlock)(),(0,n.createElementBlock)("small",d,(0,n.toDisplayString)(A.errors.name[0]),1)):(0,n.createCommentVNode)("",!0)]),(0,n.createElementVNode)("div",m,[(0,n.createElementVNode)("label",p,(0,n.toDisplayString)(e.$t("label.email")),1),(0,n.withDirectives)((0,n.createElementVNode)("input",{type:"email",id:"email","onUpdate:modelValue":t[1]||(t[1]=function(e){return A.form.email=e}),class:(0,n.normalizeClass)([A.errors.email?"invalid":"","field-control"])},null,2),[[n.vModelText,A.form.email]]),A.errors.email?((0,n.openBlock)(),(0,n.createElementBlock)("small",u,(0,n.toDisplayString)(A.errors.email[0]),1)):(0,n.createCommentVNode)("",!0)]),(0,n.createElementVNode)("div",f,[(0,n.createElementVNode)("label",h,(0,n.toDisplayString)(e.$t("label.phone")),1),(0,n.createElementVNode)("div",{class:(0,n.normalizeClass)([A.errors.phone?"invalid":"","field-control flex items-center"])},[(0,n.createElementVNode)("div",g,[(0,n.createElementVNode)("button",y,[(0,n.createTextVNode)((0,n.toDisplayString)(A.flag)+" ",1),(0,n.createElementVNode)("span",v,(0,n.toDisplayString)(A.form.country_code),1),t[6]||(t[6]=(0,n.createElementVNode)("i",{class:"fa-solid fa-caret-down text-xs"},null,-1))]),(0,n.createElementVNode)("ul",b,[((0,n.openBlock)(!0),(0,n.createElementBlock)(n.Fragment,null,(0,n.renderList)(D.countryCodes,function(e){return(0,n.openBlock)(),(0,n.createElementBlock)("li",{onClick:function(t){return D.changeCountryCode(e)},class:"flex items-center gap-2 p-1.5 rounded-md cursor-pointer hover:bg-gray-100"},[(0,n.createTextVNode)((0,n.toDisplayString)(e.flag_emoji)+" ",1),(0,n.createElementVNode)("span",V,(0,n.toDisplayString)(e.calling_code),1)],8,N)}),256))])]),(0,n.withDirectives)((0,n.createElementVNode)("input",{"onUpdate:modelValue":t[2]||(t[2]=function(e){return A.form.phone=e}),onKeypress:t[3]||(t[3]=function(e){return D.phoneNumber(e)}),class:(0,n.normalizeClass)([A.errors.phone?"invalid":"","pl-2 text-sm w-full h-full"]),type:"text",id:"phone"},null,34),[[n.vModelText,A.form.phone]])],2),A.errors.phone?((0,n.openBlock)(),(0,n.createElementBlock)("small",E,(0,n.toDisplayString)(A.errors.phone[0]),1)):(0,n.createCommentVNode)("",!0)]),(0,n.createElementVNode)("div",C,[(0,n.createElementVNode)("label",x,(0,n.toDisplayString)(e.$t("label.upload_image")),1),(0,n.createElementVNode)("input",{onChange:t[4]||(t[4]=function(){return D.changeImage&&D.changeImage.apply(D,arguments)}),class:(0,n.normalizeClass)([A.errors.image?"invalid":"","field-control"]),id:"image",type:"file",ref:"imageProperty",accept:"image/png, image/jpeg, image/jpg"},null,34),A.errors.image?((0,n.openBlock)(),(0,n.createElementBlock)("small",w,(0,n.toDisplayString)(A.errors.image[0]),1)):(0,n.createCommentVNode)("",!0)])])]),(0,n.createElementVNode)("button",k,(0,n.toDisplayString)(e.$t("button.save_changes")),1)],32)],64)}]])}}]);